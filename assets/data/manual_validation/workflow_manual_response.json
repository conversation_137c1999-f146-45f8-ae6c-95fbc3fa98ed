{"success": true, "messages": ["Workflow validation completed using enhanced pipeline", "GO validation successful"], "parsed_data": {"GO": {"global_objectives": {"natural_language": "Core Metadata:\n- name: \"Process Leave Requests\"\n- version: \"1.0\"\n- status: \"Active\"\n- description: \"Manages employee leave requests from submission to approval or rejection\"\n- primary_entity: \"LeaveApplication\"\n- classification: \"workflow\"\n  - Global Objective: \"Leave Request Processing\"\n  - Book: \"Employee Leave Management\"\n  - Chapter: \"Leave Request Lifecycle\"\n  - Tenant: \"Acme Corporation\"", "name": "Process Leave Requests", "version": "1.0", "status": "Active", "description": "Manages employee leave requests from submission to approval or rejection", "primary_entity": "LeaveApplication", "classification": "workflow", "book_name": "Employee Leave Management", "chapter_name": "Leave Request Lifecycle", "tenant_name": "Acme Corporation", "go_id": "GO1", "tenant_id": "t001", "book_id": "b001", "chapter_id": "c001", "version_type": "v2"}, "process_ownership": {"natural_language": "Process Ownership:\n- Originator: Employee\n- Process Owner: HR Manager\n- Business Sponsor: Human Resources Department", "originator": "Employee", "process_owner": "HR Manager", "business_sponsor": "Human Resources Department", "id": "GO1.PO1", "go_id": "GO1"}, "trigger_definition": {"natural_language": "Trigger Definition:\n- Trigger Type: user-initiated\n- Trigger Condition: Employee submits leave request\n- Trigger Schedule: on-demand\n- Trigger Attributes: LeaveApplication.employeeId, LeaveApplication.startDate, LeaveApplication.endDate, LeaveApplication.leaveTypeId", "trigger_type": "on-demand", "trigger_condition": "Employee submits leave request", "trigger_schedule": "on-demand", "trigger_attributes": ["LeaveApplication.employeeId", "LeaveApplication.startDate", "LeaveApplication.endDate", "LeaveApplication.leaveTypeId"], "id": "GO1.TR1", "go_id": "GO1"}, "local_objectives_list": [{"lo_number": 1, "lo_name": "SubmitLeaveRequest", "actor_type": "HUMAN", "natural_language": "LO-1: SubmitLeaveRequest [HUMAN]", "id": "GO1.LO1", "work_source": "origin", "terminal": false}, {"lo_number": 2, "lo_name": "UploadDocumentation", "actor_type": "HUMAN", "natural_language": "LO-2: UploadDocumentation [HUMAN]", "id": "GO1.LO2", "terminal": false}, {"lo_number": 3, "lo_name": "ReviewLeaveRequest", "actor_type": "HUMAN", "natural_language": "LO-3: ReviewLeaveRequest [HUMAN]", "id": "GO1.LO3", "terminal": false}, {"lo_number": 4, "lo_name": "ApproveLeaveRequest", "actor_type": "SYSTEM", "natural_language": "LO-4: ApproveLeaveRequest [SYSTEM]", "id": "GO1.LO4", "terminal": false}, {"lo_number": 5, "lo_name": "RejectLeaveRequest", "actor_type": "SYSTEM", "natural_language": "LO-5: RejectLeaveRequest [SYSTEM]", "id": "GO1.LO5", "terminal": false}, {"lo_number": 6, "lo_name": "NotifyEmployee", "actor_type": "SYSTEM", "natural_language": "LO-6: NotifyEmployee [SYSTEM]", "id": "GO1.LO6", "terminal": false}, {"lo_number": 7, "lo_name": "UpdateCalendar", "actor_type": "SYSTEM", "natural_language": "LO-7: UpdateCalendar [SYSTEM]", "id": "GO1.LO7", "terminal": true}, {"lo_number": 8, "lo_name": "UpdateLeaveBalance", "actor_type": "SYSTEM", "natural_language": "LO-8: UpdateLeaveBalance [SYSTEM]", "id": "GO1.LO8", "terminal": true}, {"lo_number": 9, "lo_name": "LogAuditTrail", "actor_type": "SYSTEM", "natural_language": "LO-9: LogAuditTrail [SYSTEM]", "id": "GO1.LO9", "terminal": true}, {"lo_number": 10, "lo_name": "CancelLeaveRequest", "actor_type": "SYSTEM", "natural_language": "LO-10: CancelLeaveRequest [SYSTEM]", "id": "GO1.LO10", "terminal": false}, {"lo_number": 11, "lo_name": "RollbackLeaveApproval", "actor_type": "SYSTEM", "natural_language": "LO-11: RollbackLeaveApproval [SYSTEM]", "id": "GO1.LO11", "terminal": false}, {"lo_number": 12, "lo_name": "RestoreLeaveBalance", "actor_type": "SYSTEM", "natural_language": "LO-12: RestoreLeaveBalance [SYSTEM]", "id": "GO1.LO12", "terminal": false}], "pathway_definitions": [{"id": "GO1.PD1", "pathway_number": 1, "pathway_name": "Standard Approval (No Documentation)", "steps": ["LO-1", "LO-3", "LO-4", "LO-6", "(LO-7, LO-9)", "LO-8"], "natural_language": "PATHWAY-1: Standard Approval (No Documentation)\nSTEPS: LO-1 → LO-3 → LO-4 → LO-6 → (LO-7, LO-9) → LO-8"}, {"id": "GO1.PD2", "pathway_number": 2, "pathway_name": "Documentation Required <PERSON><PERSON><PERSON>al", "steps": ["LO-1", "LO-2", "LO-3", "LO-4", "LO-6", "(LO-7, LO-9)", "LO-8"], "natural_language": "PATHWAY-2: Documentation Required Approval\nSTEPS: LO-1 → LO-2 → LO-3 → LO-4 → LO-6 → (LO-7, LO-9) → LO-8"}, {"id": "GO1.PD3", "pathway_number": 3, "pathway_name": "Rejection Pathway", "steps": ["LO-1", "LO-3", "LO-5", "LO-6", "(LO-7, LO-9)"], "natural_language": "PATHWAY-3: Rejection Pathway\nSTEPS: LO-1 → LO-3 → LO-5 → LO-6 → (LO-7, LO-9)"}], "business_rules": [{"id": "GO1.BR1", "go_id": "GO1", "rule_name": "validate_leave_request_advance_notice_1", "natural_language": "validate_leave_request_advance_notice_1\nInputs: LeaveApplication with startDate, leaveTypeId; LeaveType with typeId, name\nOperation: conditional_logic({\"condition\": \"AND\", \"rules\": [{\"field\": \"LeaveType.name\", \"operator\": \"notEquals\", \"value\": \"Sick Leave\"}, {\"field\": \"LeaveApplication.startDate\", \"operator\": \"lessThan\", \"value\": \"CURRENT_DATE + 7\"}]}, {\"result\": \"error\", \"message\": \"Leave requests must be submitted at least 7 days in advance for planned leave\"}, {\"result\": \"success\"}, LeaveApplication.validationResult)\nDescription: Validates that planned leave requests (non-sick leave) are submitted at least 7 days in advance.\nOutput: LeaveApplication with validationResult\nError: When a planned leave request is submitted less than 7 days in advance, returns \"Leave requests must be submitted at least 7 days in advance for planned leave\"\nValidation: PRE_INSERT on LeaveApplication entity", "description": "Validates that planned leave requests (non-sick leave) are submitted at least 7 days in advance.", "rule_description": "Validates that planned leave requests (non-sick leave) are submitted at least 7 days in advance.", "rule_inputs": ["LeaveApplication with startDate, leaveTypeId", "LeaveType with typeId, name"], "rule_operation": "conditional_logic({\"condition\": \"AND\", \"rules\": [{\"field\": \"LeaveType.name\", \"operator\": \"notEquals\", \"value\": \"Sick Leave\"}, {\"field\": \"LeaveApplication.startDate\", \"operator\": \"lessThan\", \"value\": \"CURRENT_DATE + 7\"}]}, {\"result\": \"error\", \"message\": \"Leave requests must be submitted at least 7 days in advance for planned leave\"}, {\"result\": \"success\"}, LeaveApplication.validationResult)", "rule_outputs": ["LeaveApplication with validationResult"], "rule_error_message": "When a planned leave request is submitted less than 7 days in advance, returns \"Leave requests must be submitted at least 7 days in advance for planned leave\"", "rule_validation_type": "PRE_INSERT on LeaveApplication entity"}, {"id": "GO1.BR2", "go_id": "GO1", "rule_name": "validate_sick_leave_retroactive_1", "natural_language": "validate_sick_leave_retroactive_1\nInputs: LeaveApplication with startDate, leaveTypeId; LeaveType with typeId, name\nOperation: conditional_logic({\"condition\": \"AND\", \"rules\": [{\"field\": \"LeaveType.name\", \"operator\": \"equals\", \"value\": \"Sick Leave\"}, {\"field\": \"LeaveApplication.startDate\", \"operator\": \"lessThan\", \"value\": \"CURRENT_DATE - 3\"}]}, {\"result\": \"error\", \"message\": \"Sick leave can be submitted retroactively within 3 days\"}, {\"result\": \"success\"}, LeaveApplication.validationResult)\nDescription: Validates that sick leave can be submitted retroactively but only within 3 days.\nOutput: LeaveApplication with validationResult\nError: When sick leave is submitted more than 3 days after the start date, returns \"Sick leave can be submitted retroactively within 3 days\"\nValidation: PRE_INSERT on LeaveApplication entity", "description": "Validates that sick leave can be submitted retroactively but only within 3 days.", "rule_description": "Validates that sick leave can be submitted retroactively but only within 3 days.", "rule_inputs": ["LeaveApplication with startDate, leaveTypeId", "LeaveType with typeId, name"], "rule_operation": "conditional_logic({\"condition\": \"AND\", \"rules\": [{\"field\": \"LeaveType.name\", \"operator\": \"equals\", \"value\": \"Sick Leave\"}, {\"field\": \"LeaveApplication.startDate\", \"operator\": \"lessThan\", \"value\": \"CURRENT_DATE - 3\"}]}, {\"result\": \"error\", \"message\": \"Sick leave can be submitted retroactively within 3 days\"}, {\"result\": \"success\"}, LeaveApplication.validationResult)", "rule_outputs": ["LeaveApplication with validationResult"], "rule_error_message": "When sick leave is submitted more than 3 days after the start date, returns \"Sick leave can be submitted retroactively within 3 days\"", "rule_validation_type": "PRE_INSERT on LeaveApplication entity"}, {"id": "GO1.BR3", "go_id": "GO1", "rule_name": "validate_leave_documentation_requirement_1", "natural_language": "validate_leave_documentation_requirement_1\nInputs: LeaveApplication with leaveTypeId, numDays; LeaveType with typeId, name, requiresDocumentation\nOperation: conditional_logic({\"condition\": \"AND\", \"rules\": [{\"field\": \"LeaveType.name\", \"operator\": \"equals\", \"value\": \"Sick Leave\"}, {\"field\": \"LeaveApplication.numDays\", \"operator\": \"greaterThan\", \"value\": 3}, {\"field\": \"LeaveApplication.documentationProvided\", \"operator\": \"equals\", \"value\": false}]}, {\"result\": \"error\", \"message\": \"Documentation is required for sick leave exceeding 3 days\"}, {\"result\": \"success\"}, LeaveApplication.validationResult)\nDescription: Validates that documentation is provided for sick leave requests exceeding 3 days.\nOutput: LeaveApplication with validationResult\nError: When documentation is not provided for sick leave exceeding 3 days, returns \"Documentation is required for sick leave exceeding 3 days\"\nValidation: PRE_INSERT on LeaveApplication entity", "description": "Validates that documentation is provided for sick leave requests exceeding 3 days.", "rule_description": "Validates that documentation is provided for sick leave requests exceeding 3 days.", "rule_inputs": ["LeaveApplication with leaveTypeId, numDays", "LeaveType with typeId, name, requiresDocumentation"], "rule_operation": "conditional_logic({\"condition\": \"AND\", \"rules\": [{\"field\": \"LeaveType.name\", \"operator\": \"equals\", \"value\": \"Sick Leave\"}, {\"field\": \"LeaveApplication.numDays\", \"operator\": \"greaterThan\", \"value\": 3}, {\"field\": \"LeaveApplication.documentationProvided\", \"operator\": \"equals\", \"value\": false}]}, {\"result\": \"error\", \"message\": \"Documentation is required for sick leave exceeding 3 days\"}, {\"result\": \"success\"}, LeaveApplication.validationResult)", "rule_outputs": ["LeaveApplication with validationResult"], "rule_error_message": "When documentation is not provided for sick leave exceeding 3 days, returns \"Documentation is required for sick leave exceeding 3 days\"", "rule_validation_type": "PRE_INSERT on LeaveApplication entity"}, {"id": "GO1.BR4", "go_id": "GO1", "rule_name": "validate_leave_cancellation_notice_1", "natural_language": "validate_leave_cancellation_notice_1\nInputs: LeaveApplication with leaveId, startDate, status\nOperation: conditional_logic({\"condition\": \"AND\", \"rules\": [{\"field\": \"FUNCTION_TYPE\", \"operator\": \"equals\", \"value\": \"update\"}, {\"field\": \"LeaveApplication.status\", \"operator\": \"changedTo\", \"value\": \"Cancelled\"}, {\"field\": \"LeaveApplication.startDate\", \"operator\": \"lessThan\", \"value\": \"CURRENT_DATE + 1\"}]}, {\"result\": \"error\", \"message\": \"Cancellations must be made at least 24 hours in advance\"}, {\"result\": \"success\"}, LeaveApplication.validationResult)\nDescription: Validates that leave cancellations are made at least 24 hours in advance of the leave start date.\nOutput: LeaveApplication with validationResult\nError: When a leave request is cancelled less than 24 hours before the start date, returns \"Cancellations must be made at least 24 hours in advance\"\nValidation: PRE_UPDATE on LeaveApplication entity", "description": "Validates that leave cancellations are made at least 24 hours in advance of the leave start date.", "rule_description": "Validates that leave cancellations are made at least 24 hours in advance of the leave start date.", "rule_inputs": ["LeaveApplication with leaveId, startDate, status"], "rule_operation": "conditional_logic({\"condition\": \"AND\", \"rules\": [{\"field\": \"FUNCTION_TYPE\", \"operator\": \"equals\", \"value\": \"update\"}, {\"field\": \"LeaveApplication.status\", \"operator\": \"changedTo\", \"value\": \"Cancelled\"}, {\"field\": \"LeaveApplication.startDate\", \"operator\": \"lessThan\", \"value\": \"CURRENT_DATE + 1\"}]}, {\"result\": \"error\", \"message\": \"Cancellations must be made at least 24 hours in advance\"}, {\"result\": \"success\"}, LeaveApplication.validationResult)", "rule_outputs": ["LeaveApplication with validationResult"], "rule_error_message": "When a leave request is cancelled less than 24 hours before the start date, returns \"Cancellations must be made at least 24 hours in advance\"", "rule_validation_type": "PRE_UPDATE on LeaveApplication entity"}, {"id": "GO1.BR5", "go_id": "GO1", "rule_name": "validate_manager_approval_for_long_leave_1", "natural_language": "validate_manager_approval_for_long_leave_1\nInputs: LeaveApplication with numDays, managerApproval\nOperation: conditional_logic({\"condition\": \"AND\", \"rules\": [{\"field\": \"LeaveApplication.numDays\", \"operator\": \"greaterThan\", \"value\": 5}, {\"field\": \"LeaveApplication.managerApproval\", \"operator\": \"equals\", \"value\": false}]}, {\"result\": \"error\", \"message\": \"Leave requests exceeding 5 consecutive days require manager approval\"}, {\"result\": \"success\"}, LeaveApplication.validationResult)\nDescription: Validates that leave requests exceeding 5 consecutive days have manager approval.\nOutput: LeaveApplication with validationResult\nError: When a leave request exceeding 5 days does not have manager approval, returns \"Leave requests exceeding 5 consecutive days require manager approval\"\nValidation: PRE_UPDATE on LeaveApplication entity where status changed to \"Approved\"", "description": "Validates that leave requests exceeding 5 consecutive days have manager approval.", "rule_description": "Validates that leave requests exceeding 5 consecutive days have manager approval.", "rule_inputs": ["LeaveApplication with numDays, managerApproval"], "rule_operation": "conditional_logic({\"condition\": \"AND\", \"rules\": [{\"field\": \"LeaveApplication.numDays\", \"operator\": \"greaterThan\", \"value\": 5}, {\"field\": \"LeaveApplication.managerApproval\", \"operator\": \"equals\", \"value\": false}]}, {\"result\": \"error\", \"message\": \"Leave requests exceeding 5 consecutive days require manager approval\"}, {\"result\": \"success\"}, LeaveApplication.validationResult)", "rule_outputs": ["LeaveApplication with validationResult"], "rule_error_message": "When a leave request exceeding 5 days does not have manager approval, returns \"Leave requests exceeding 5 consecutive days require manager approval\"", "rule_validation_type": "PRE_UPDATE on LeaveApplication entity where status changed to \"Approved\""}], "performance_metadata": {"natural_language": "Performance Metadata:\n- cycle_time: \"2 business days\"\n- number_of_pathways: 4\n- volume_metrics:\n  * average_volume: 120\n  * peak_volume: 250\n  * unit: \"requests/month\"\n- sla_thresholds:\n  * manager_review: \"1 business day\"\n  * notification: \"1 hour\"\n  * system_processing: \"5 minutes\"\n- critical_lo_performance:\n  * \"ReviewLeaveRequest\": \"24 hours maximum\"\n  * \"NotifyEmployee\": \"1 hour maximum\"\n  * \"UpdateCalendar\": \"5 minutes maximum\"\n  * \"UpdateLeaveBalance\": \"5 minutes maximum\"", "metadata_data": {"cycle_time": "2 business days", "number_of_pathways": 4, "volume_metrics": {"average_volume": 120, "peak_volume": 250, "unit": "requests/month"}, "sla_thresholds": {"manager_review": "1 business day", "notification": "1 hour", "system_processing": "5 minutes"}, "critical_lo_performance": {"ReviewLeaveRequest": "24 hours maximum", "NotifyEmployee": "1 hour maximum", "UpdateCalendar": "5 minutes maximum", "UpdateLeaveBalance": "5 minutes maximum"}}, "id": "GO1.PF1", "go_id": "GO1"}, "process_mining_schema": {"natural_language": "Process Mining Schema:\n", "schema_data": {"event_log_specification": {"case_id": "LeaveApplication.leaveId", "activity": "LO_name", "event_type": "start/complete/abort/rollback", "timestamp": "ISO-8601 datetime", "resource": "role/system_executing", "duration": "milliseconds", "attributes": {"entity_state": "json_snapshot", "input_values": "input_parameters", "output_values": "output_results", "execution_status": "success/failure/pending", "error_details": "error_message_if_any"}}}, "id": "GO1.PM1", "go_id": "GO1"}, "performance_discovery_metrics": {"id": "GO1.PDM1", "go_id": "GO1", "natural_language": "Performance Discovery Metrics:\n- pathway_frequency:\n  * \"Standard Approval\":\n    - frequency: 95\n    - percentage: 79\n    - average_duration: \"1.5 days\"\n    - success_rate: 98\n  * \"Documentation Required\":\n    - frequency: 25\n    - percentage: 21\n    - average_duration: \"2.8 days\"\n    - success_rate: 92\n- bottleneck_analysis:\n  * \"ReviewLeaveRequest\":\n    - average_wait_time: \"1.2 days\"\n    - queue_length: 8\n    - resource_utilization: 75\n    - failure_rate: 5\n- resource_patterns:\n  * \"HR Manager\":\n    - active_hours: \"9:00-17:00 business days\"\n    - peak_load_periods: \"Monday mornings, month-end\"\n    - concurrent_executions: 12\n  * \"Notification System\":\n    - active_hours: \"24/7\"\n    - peak_load_periods: \"8:00-10:00 workdays\"\n    - concurrent_executions: 50", "pathway_frequency": {"Standard Approval": {"frequency": 95, "percentage": 79, "average_duration": "1.5 days", "success_rate": 98}, "Documentation Required": {"frequency": 25, "percentage": 21, "average_duration": "2.8 days", "success_rate": 92}}, "bottleneck_analysis": {"ReviewLeaveRequest": {"average_wait_time": "1.2 days", "queue_length": 8, "resource_utilization": 75, "failure_rate": 5}}, "resource_patterns": {"HR Manager": {"active_hours": "9:00-17:00 business days", "peak_load_periods": "Monday mornings, month-end", "concurrent_executions": 12}, "Notification System": {"active_hours": "24/7", "peak_load_periods": "8:00-10:00 workdays", "concurrent_executions": 50}}}, "conformance_analytics": {"id": "GO1.CA1", "go_id": "GO1", "natural_language": "Conformance Analytics:\n- compliance_rate: 96\n- execution_variance:\n  * \"ReviewLeaveRequest\":\n    - expected_duration: \"4 hours\"\n    - actual_duration_range: \"2-24 hours\"\n    - variance_causes: [\"manager availability\", \"high request volume\", \"incomplete documentation\"]\n  * \"NotifyEmployee\":\n    - expected_duration: \"10 minutes\"\n    - actual_duration_range: \"5-25 minutes\"\n    - variance_causes: [\"notification system latency\", \"email delivery issues\"]\n- exception_patterns:\n  * \"input_timeout\":\n    - frequency: 6\n    - affected_pathways: [\"Documentation Required\"]\n    - recovery_success_rate: 85\n  * \"validation_failure\":\n    - frequency: 12\n    - most_common_failures: [\"insufficient leave balance\", \"invalid date range\"]\n    - resolution_time: \"1.5 days\"\n  * \"system_error\":\n    - frequency: 2\n    - error_categories: [\"calendar integration\", \"notification delivery\"]\n    - automatic_recovery_rate: 92", "compliance_rate": 96, "execution_variance": {"ReviewLeaveRequest": {"expected_duration": "4 hours", "actual_duration_range": "2-24 hours", "variance_causes": ["manager availability", "high request volume", "incomplete documentation"]}, "NotifyEmployee": {"expected_duration": "10 minutes", "actual_duration_range": "5-25 minutes", "variance_causes": ["notification system latency", "email delivery issues"]}}, "exception_patterns": {"input_timeout": {"frequency": 6, "affected_pathways": ["Documentation Required"], "recovery_success_rate": 85}, "validation_failure": {"frequency": 12, "most_common_failures": ["insufficient leave balance", "invalid date range"], "resolution_time": "1.5 days"}, "system_error": {"frequency": 2, "error_categories": ["calendar integration", "notification delivery"], "automatic_recovery_rate": 92}}}, "advanced_process_intelligence": {"id": "GO1.API1", "go_id": "GO1", "natural_language": "Advanced Process Intelligence:\n- process_health_score:\n  * performance_score: 88\n  * compliance_score: 96\n  * efficiency_score: 84\n  * overall_health: 89\n- prediction_models:\n  * completion_time_forecast:\n    - algorithm: \"Random Forest Regression\"\n    - accuracy: 87\n    - confidence_interval: \"±0.4 days\"\n  * failure_prediction:\n    - algorithm: \"Gradient Boosting Classification\"\n    - precision: 82\n    - recall: 79\n- optimization_insights:\n  * bottleneck_elimination: [\"Implement auto-approval for standard requests under 2 days\", \"Expand manager delegation options\"]\n  * resource_reallocation: [\"Add review capacity during Monday mornings\", \"Implement notification batching\"]\n  * pathway_optimization: [\"Streamline documentation review process\", \"Implement one-click approvals for recurring requests\"]", "process_health_score": {"performance_score": 88, "compliance_score": 96, "efficiency_score": 84, "overall_health": 89}, "prediction_models": {"completion_time_forecast": {"algorithm": "Random Forest Regression", "accuracy": 87, "confidence_interval": "±0.4 days"}, "failure_prediction": {"algorithm": "Gradient Boosting Classification", "precision": 82, "recall": 79}}, "optimization_insights": {"bottleneck_elimination": ["Implement auto-approval for standard requests under 2 days", "Expand manager delegation options"], "resource_reallocation": ["Add review capacity during Monday mornings", "Implement notification batching"], "pathway_optimization": ["Streamline documentation review process", "Implement one-click approvals for recurring requests"]}}, "rollback_analytics": {"id": "GO1.RA1", "go_id": "GO1", "natural_language": "Rollback Analytics:\n- rollback_frequency: 4\n- rollback_success_rate: 99\n- rollback_triggers:\n  * \"user_initiated\":\n    - frequency: 18\n    - average_impact_scope: 2\n    - average_recovery_time: \"2 hours\"\n  * \"system_error\":\n    - frequency: 2\n    - average_impact_scope: 1\n    - average_recovery_time: \"1 hour\"\n- rollback_pathways:\n  * \"reject_leave_request\":\n    - frequency: 16\n    - success_rate: 99\n    - average_completion_time: \"2.5 hours\"\n  * \"update_leave_balance\":\n    - frequency: 4\n    - success_rate: 95\n    - average_completion_time: \"1.2 hours\"", "rollback_frequency": 4, "rollback_success_rate": 99, "rollback_triggers": {"user_initiated": {"frequency": 18, "average_impact_scope": 2, "average_recovery_time": "2 hours"}, "system_error": {"frequency": 2, "average_impact_scope": 1, "average_recovery_time": "1 hour"}}}, "rollback_pathways": [{"id": "GO1.RP1", "go_id": "GO1", "from_lo": "SubmitLeaveRequest", "to_lo": "CancelLeaveRequest", "pathway_type": "standard", "natural_language": "SubmitLeaveRequest -> CancelLeaveRequest (standard rollback pathway)"}, {"id": "GO1.RP2", "go_id": "GO1", "from_lo": "ApproveLeaveRequest", "to_lo": "RollbackLeaveApproval", "pathway_type": "standard", "natural_language": "ApproveLeaveRequest -> RollbackLeaveApproval (standard rollback pathway)"}, {"id": "GO1.RP3", "go_id": "GO1", "from_lo": "UpdateLeaveBalance", "to_lo": "RestoreLeaveBalance", "pathway_type": "standard", "natural_language": "UpdateLeaveBalance -> RestoreLeaveBalance (standard rollback pathway)"}], "validation_rules": {"id": "GO1.VR1", "go_id": "GO1", "rules": [{"id": "GO1.VR1.R1", "rule_name": "validate_lo_natural_language_names", "natural_language": "validate_lo_natural_language_names\nInputs: GO with pathways\nOperation: validate_all(GO.pathways, \"name\", matches_pattern(\"[A-Z][a-zA-Z]+\"))\nDescription: Validates that all LOs are defined with natural language names\nOutput: ValidationResult with status, message\nError: When LO names don't follow natural language pattern, returns \"All LOs must be defined with natural language names\"\nValidation: PRE_DEPLOY on GO entity", "rule_inputs": ["GO with pathways"], "rule_operation": "validate_all(GO.pathways, \"name\", matches_pattern(\"[A-Z][a-zA-Z]+\"))", "rule_description": "Validates that all LOs are defined with natural language names", "rule_output": "ValidationResult with status, message", "rule_error": "When LO names don't follow natural language pattern, returns \"All LOs must be defined with natural language names\"", "rule_validation": "PRE_DEPLOY on GO entity"}, {"id": "GO1.VR1.R2", "rule_name": "validate_referenced_los_defined", "natural_language": "validate_referenced_los_defined\nInputs: GO with pathways, routings\nOperation: validate_references(GO.routings, \"target\", exists_in(GO.pathways, \"name\"))\nDescription: Validates that every referenced LO is defined in Process Flow section\nOutput: ValidationResult with status, message\nError: When routing references undefined LO, returns \"All referenced LOs must be defined in Process Flow section\"\nValidation: PRE_DEPLOY on GO entity", "rule_inputs": ["GO with pathways, routings"], "rule_operation": "validate_references(GO.routings, \"target\", exists_in(GO.pathways, \"name\"))", "rule_description": "Validates that every referenced LO is defined in Process Flow section", "rule_output": "ValidationResult with status, message", "rule_error": "When routing references undefined LO, returns \"All referenced LOs must be defined in Process Flow section\"", "rule_validation": "PRE_DEPLOY on GO entity"}, {"id": "GO1.VR1.R3", "rule_name": "validate_first_lo_human_initiated", "natural_language": "validate_first_lo_human_initiated\nInputs: GO with pathways\nOperation: validate_equals(GO.pathways[0].actor_type, \"HUMAN\")\nDescription: Validates that the first LO is human-initiated\nOutput: ValidationResult with status, message\nError: When first LO is not human-initiated, returns \"First LO must be human-initiated\"\nValidation: PRE_DEPLOY on GO entity", "rule_inputs": ["GO with pathways"], "rule_operation": "validate_equals(GO.pathways[0].actor_type, \"HUMAN\")", "rule_description": "Validates that the first LO is human-initiated", "rule_output": "ValidationResult with status, message", "rule_error": "When first LO is not human-initiated, returns \"First LO must be human-initiated\"", "rule_validation": "PRE_DEPLOY on GO entity"}, {"id": "GO1.VR1.R4", "rule_name": "validate_terminal_pathways", "natural_language": "validate_terminal_pathways\nInputs: GO with pathways, routings\nOperation: validate_all_paths_terminate(GO.pathways, GO.routings)\nDescription: Validates that all pathways terminate in completion or cancellation\nOutput: ValidationResult with status, message\nError: When pathway doesn't terminate, returns \"All pathways must terminate in completion or cancellation\"\nValidation: PRE_DEPLOY on GO entity", "rule_inputs": ["GO with pathways, routings"], "rule_operation": "validate_all_paths_terminate(GO.pathways, GO.routings)", "rule_description": "Validates that all pathways terminate in completion or cancellation", "rule_output": "ValidationResult with status, message", "rule_error": "When pathway doesn't terminate, returns \"All pathways must terminate in completion or cancellation\"", "rule_validation": "PRE_DEPLOY on GO entity"}, {"id": "GO1.VR1.R5", "rule_name": "validate_rollback_pathways", "natural_language": "validate_rollback_pathways\nInputs: GO with pathways, rollback_pathways\nOperation: validate_all_exist(GO.rollback_pathways, GO.pathways, \"name\")\nDescription: Validates that rollback LOs are included in Process Flow\nOutput: ValidationResult with status, message\nError: When rollback pathway references undefined LO, returns \"All rollback LOs must be included in Process Flow\"\nValidation: PRE_DEPLOY on GO entity", "rule_inputs": ["GO with pathways, rollback_pathways"], "rule_operation": "validate_all_exist(GO.rollback_pathways, GO.pathways, \"name\")", "rule_description": "Validates that rollback LOs are included in Process Flow", "rule_output": "ValidationResult with status, message", "rule_error": "When rollback pathway references undefined LO, returns \"All rollback LOs must be included in Process Flow\"", "rule_validation": "PRE_DEPLOY on GO entity"}, {"id": "GO1.VR1.R6", "rule_name": "validate_parallel_flows", "natural_language": "validate_parallel_flows\nInputs: GO with pathways, parallel_flows\nOperation: validate_all_exist(GO.parallel_flows, GO.pathways, \"name\")\nDescription: Validates that parallel flows reference defined LOs\nOutput: ValidationResult with status, message\nError: When parallel flow references undefined LO, returns \"All parallel flows must reference defined LOs\"\nValidation: PRE_DEPLOY on GO entity", "rule_inputs": ["GO with pathways, parallel_flows"], "rule_operation": "validate_all_exist(GO.parallel_flows, GO.pathways, \"name\")", "rule_description": "Validates that parallel flows reference defined LOs", "rule_output": "ValidationResult with status, message", "rule_error": "When parallel flow references undefined LO, returns \"All parallel flows must reference defined LOs\"", "rule_validation": "PRE_DEPLOY on GO entity"}, {"id": "GO1.VR1.R7", "rule_name": "validate_performance_metadata", "natural_language": "validate_performance_metadata\nInputs: GO with performance_metadata\nOperation: validate_required_fields(GO.performance_metadata, [\"cycle_time\", \"number_of_pathways\", \"volume_metrics\", \"sla_thresholds\", \"critical_lo_performance\"])\nDescription: Validates that all required performance metadata is specified\nOutput: ValidationResult with status, message\nError: When required performance metadata is missing, returns \"All required performance metadata must be specified\"\nValidation: PRE_DEPLOY on GO entity", "rule_inputs": ["GO with performance_metadata"], "rule_operation": "validate_required_fields(GO.performance_metadata, [\"cycle_time\", \"number_of_pathways\", \"volume_metrics\", \"sla_thresholds\", \"critical_lo_performance\"])", "rule_description": "Validates that all required performance metadata is specified", "rule_output": "ValidationResult with status, message", "rule_error": "When required performance metadata is missing, returns \"All required performance metadata must be specified\"", "rule_validation": "PRE_DEPLOY on GO entity"}], "natural_language": "Validation Rules for GO Creation:\nvalidate_lo_natural_language_names\nInputs: GO with pathways\nOperation: validate_all(GO.pathways, \"name\", matches_pattern(\"[A-Z][a-zA-Z]+\"))\nDescription: Validates that all LOs are defined with natural language names\nOutput: ValidationResult with status, message\nError: When LO names don't follow natural language pattern, returns \"All LOs must be defined with natural language names\"\nValidation: PRE_DEPLOY on GO entity\n\nvalidate_referenced_los_defined\nInputs: GO with pathways, routings\nOperation: validate_references(GO.routings, \"target\", exists_in(GO.pathways, \"name\"))\nDescription: Validates that every referenced LO is defined in Process Flow section\nOutput: ValidationResult with status, message\nError: When routing references undefined LO, returns \"All referenced LOs must be defined in Process Flow section\"\nValidation: PRE_DEPLOY on GO entity\n\nvalidate_first_lo_human_initiated\nInputs: GO with pathways\nOperation: validate_equals(GO.pathways[0].actor_type, \"HUMAN\")\nDescription: Validates that the first LO is human-initiated\nOutput: ValidationResult with status, message\nError: When first LO is not human-initiated, returns \"First LO must be human-initiated\"\nValidation: PRE_DEPLOY on GO entity\n\nvalidate_terminal_pathways\nInputs: GO with pathways, routings\nOperation: validate_all_paths_terminate(GO.pathways, GO.routings)\nDescription: Validates that all pathways terminate in completion or cancellation\nOutput: ValidationResult with status, message\nError: When pathway doesn't terminate, returns \"All pathways must terminate in completion or cancellation\"\nValidation: PRE_DEPLOY on GO entity\n\nvalidate_rollback_pathways\nInputs: GO with pathways, rollback_pathways\nOperation: validate_all_exist(GO.rollback_pathways, GO.pathways, \"name\")\nDescription: Validates that rollback LOs are included in Process Flow\nOutput: ValidationResult with status, message\nError: When rollback pathway references undefined LO, returns \"All rollback LOs must be included in Process Flow\"\nValidation: PRE_DEPLOY on GO entity\n\nvalidate_parallel_flows\nInputs: GO with pathways, parallel_flows\nOperation: validate_all_exist(GO.parallel_flows, GO.pathways, \"name\")\nDescription: Validates that parallel flows reference defined LOs\nOutput: ValidationResult with status, message\nError: When parallel flow references undefined LO, returns \"All parallel flows must reference defined LOs\"\nValidation: PRE_DEPLOY on GO entity\n\nvalidate_performance_metadata\nInputs: GO with performance_metadata\nOperation: validate_required_fields(GO.performance_metadata, [\"cycle_time\", \"number_of_pathways\", \"volume_metrics\", \"sla_thresholds\", \"critical_lo_performance\"])\nDescription: Validates that all required performance metadata is specified\nOutput: ValidationResult with status, message\nError: When required performance metadata is missing, returns \"All required performance metadata must be specified\"\nValidation: PRE_DEPLOY on GO entity"}, "data_constraints": [{"id": "GO1.DC1", "go_id": "GO1", "entity": "LeaveApplication", "attribute": "leaveId", "data_type": "string", "constraint_type": "mandatory", "constraint_text": "is mandatory and unique", "error_message": "Leave ID is required and must be unique", "natural_language": "LeaveApplication.leaveId is mandatory and unique"}, {"id": "GO1.DC2", "go_id": "GO1", "entity": "LeaveApplication", "attribute": "employeeId", "data_type": "string", "constraint_type": "mandatory", "constraint_text": "is mandatory and must exist in Employee table", "error_message": "Employee ID is required", "natural_language": "LeaveApplication.employeeId is mandatory and must exist in Employee table"}, {"id": "GO1.DC3", "go_id": "GO1", "entity": "LeaveApplication", "attribute": "startDate", "data_type": "date", "constraint_type": "mandatory", "constraint_text": "is mandatory and must be a valid date", "error_message": "Start date is required and must be a valid date", "natural_language": "LeaveApplication.startDate is mandatory and must be a valid date"}, {"id": "GO1.DC4", "go_id": "GO1", "entity": "LeaveApplication", "attribute": "endDate", "data_type": "date", "constraint_type": "mandatory", "constraint_text": "is mandatory, must be a valid date, and must be after startDate", "error_message": "End date is required, must be a valid date, and must be after start date", "natural_language": "LeaveApplication.endDate is mandatory, must be a valid date, and must be after startDate"}, {"id": "GO1.DC5", "go_id": "GO1", "entity": "LeaveApplication", "attribute": "leaveTypeId", "data_type": "string", "constraint_type": "mandatory", "constraint_text": "is mandatory and must exist in LeaveType table", "error_message": "Leave type is required", "natural_language": "LeaveApplication.leaveTypeId is mandatory and must exist in LeaveType table"}], "process_flow": [{"lo_name": "SubmitLeaveRequest", "actor_type": "HUMAN", "description": "Employee submits a leave request", "route_type": "Alternate", "id": "GO1.PW1", "go_id": "GO1", "lo_id": "GO1.LO1", "natural_language": "1.  SubmitLeaveRequest [HUMAN] \n    Description: Employee submits a leave request\n    Route Type: Alternate\n    a. If LeaveApplication.requiresDocumentation = true, route to UploadDocumentation\n    b. If LeaveApplication.requiresDocumentation = false, route to ReviewLeaveRequest", "conditions": [{"condition": "LeaveApplication.requiresDocumentation = true", "route_to": "GO1.LO2"}, {"condition": "LeaveApplication.requiresDocumentation = false", "route_to": "GO1.LO3"}]}, {"lo_name": "UploadDocumentation", "actor_type": "HUMAN", "description": "Employee uploads required documentation", "route_type": "Sequential", "id": "GO1.PW2", "go_id": "GO1", "lo_id": "GO1.LO2", "natural_language": "2.  UploadDocumentation [HUMAN]\n    Description: Employee uploads required documentation\n    Route Type: Sequential\n    a. Route to ReviewLeaveRequest", "routes": ["GO1.LO3"]}, {"lo_name": "ReviewLeaveRequest", "actor_type": "HUMAN", "description": "Manager reviews the leave request", "route_type": "Alternate", "id": "GO1.PW3", "go_id": "GO1", "lo_id": "GO1.LO3", "natural_language": "3.  ReviewLeaveRequest [HUMAN]\n    Description: Manager reviews the leave request\n    Route Type: Alternate\n    a. If LeaveApplication.status = \"Approved\", route to ApproveLeaveRequest\n    b. If LeaveApplication.status = \"Rejected\", route to RejectLeaveRequest", "conditions": [{"condition": "LeaveApplication.status = \"Approved\"", "route_to": "GO1.LO4"}, {"condition": "LeaveApplication.status = \"Rejected\"", "route_to": "GO1.LO5"}]}, {"lo_name": "ApproveLeaveRequest", "actor_type": "SYSTEM", "description": "System updates leave request status to approved", "route_type": "Sequential", "id": "GO1.PW4", "go_id": "GO1", "lo_id": "GO1.LO4", "natural_language": "4.  ApproveLeaveRequest [SYSTEM]\n    Description: System updates leave request status to approved\n    Route Type: Sequential\n    a. Route to NotifyEmployee", "routes": ["GO1.LO6"]}, {"lo_name": "RejectLeaveRequest", "actor_type": "SYSTEM", "description": "System updates leave request status to rejected", "route_type": "Sequential", "id": "GO1.PW5", "go_id": "GO1", "lo_id": "GO1.LO5", "natural_language": "5.  RejectLeaveRequest [SYSTEM]\n    Description: System updates leave request status to rejected\n    Route Type: Sequential\n    a. Route to NotifyEmployee", "routes": ["GO1.LO6"]}, {"lo_name": "NotifyEmployee", "actor_type": "SYSTEM", "description": "System notifies employee of the decision", "route_type": "<PERSON><PERSON><PERSON>", "id": "GO1.PW6", "go_id": "GO1", "lo_id": "GO1.LO6", "natural_language": "6.  NotifyEmployee [SYSTEM]\n    Description: System notifies employee of the decision\n    Route Type: Parallel\n    a. Route to UpdateCalendar - System updates employee calendar\n    b. Route to LogAuditTrail - System logs audit trail for compliance\n    c. Join at: UpdateLeaveBalance", "parallel_routes": [{"route_to": "GO1.LO7", "description": "System updates employee calendar"}, {"route_to": "GO1.LO9", "description": "System logs audit trail for compliance"}], "join_at": "GO1.LO8"}, {"lo_name": "UpdateCalendar", "actor_type": "SYSTEM", "description": "System updates employee calendar", "route_type": "Alternate", "id": "GO1.PW7", "go_id": "GO1", "lo_id": "GO1.LO7", "natural_language": "7.  UpdateCalendar [SYSTEM]\n    Description: System updates employee calendar\n    Route Type: Alternate\n    a. If LeaveApplication.status = \"Approved\", route to UpdateLeaveBalance\n    b. If LeaveApplication.status = \"Rejected\", route to LogAuditTrail", "conditions": [{"condition": "LeaveApplication.status = \"Approved\"", "route_to": "GO1.LO8"}, {"condition": "LeaveApplication.status = \"Rejected\"", "route_to": "GO1.LO9"}]}, {"lo_name": "UpdateLeaveBalance", "actor_type": "SYSTEM", "description": "System updates employee leave balance", "route_type": "Sequential", "id": "GO1.PW8", "go_id": "GO1", "lo_id": "GO1.LO8", "natural_language": "8.  UpdateLeaveBalance [SYSTEM]\n    Description: System updates employee leave balance\n    Route Type: Sequential\n    a. Complete process"}, {"lo_name": "LogAuditTrail", "actor_type": "SYSTEM", "description": "System logs audit trail for compliance", "route_type": "Sequential", "id": "GO1.PW9", "go_id": "GO1", "lo_id": "GO1.LO9", "natural_language": "9.  LogAuditTrail [SYSTEM]\n    Description: System logs audit trail for compliance\n    Route Type: Sequential\n    a. Route to UpdateLeaveBalance\n\nBusinessRule for LeaveApplication:\nvalidate_leave_request_advance_notice_1\nInputs: LeaveApplication with startDate, leaveTypeId; LeaveType with typeId, name\nOperation: conditional_logic({\"condition\": \"AND\", \"rules\": [{\"field\": \"LeaveType.name\", \"operator\": \"notEquals\", \"value\": \"Sick Leave\"}, {\"field\": \"LeaveApplication.startDate\", \"operator\": \"lessThan\", \"value\": \"CURRENT_DATE + 7\"}]}, {\"result\": \"error\", \"message\": \"Leave requests must be submitted at least 7 days in advance for planned leave\"}, {\"result\": \"success\"}, LeaveApplication.validationResult)\nDescription: Validates that planned leave requests (non-sick leave) are submitted at least 7 days in advance.\nOutput: LeaveApplication with validationResult\nError: When a planned leave request is submitted less than 7 days in advance, returns \"Leave requests must be submitted at least 7 days in advance for planned leave\"\nValidation: PRE_INSERT on LeaveApplication entity\n\nvalidate_sick_leave_retroactive_1\nInputs: LeaveApplication with startDate, leaveTypeId; LeaveType with typeId, name\nOperation: conditional_logic({\"condition\": \"AND\", \"rules\": [{\"field\": \"LeaveType.name\", \"operator\": \"equals\", \"value\": \"Sick Leave\"}, {\"field\": \"LeaveApplication.startDate\", \"operator\": \"lessThan\", \"value\": \"CURRENT_DATE - 3\"}]}, {\"result\": \"error\", \"message\": \"Sick leave can be submitted retroactively within 3 days\"}, {\"result\": \"success\"}, LeaveApplication.validationResult)\nDescription: Validates that sick leave can be submitted retroactively but only within 3 days.\nOutput: LeaveApplication with validationResult\nError: When sick leave is submitted more than 3 days after the start date, returns \"Sick leave can be submitted retroactively within 3 days\"\nValidation: PRE_INSERT on LeaveApplication entity\n\nvalidate_leave_documentation_requirement_1\nInputs: LeaveApplication with leaveTypeId, numDays; LeaveType with typeId, name, requiresDocumentation\nOperation: conditional_logic({\"condition\": \"AND\", \"rules\": [{\"field\": \"LeaveType.name\", \"operator\": \"equals\", \"value\": \"Sick Leave\"}, {\"field\": \"LeaveApplication.numDays\", \"operator\": \"greaterThan\", \"value\": 3}, {\"field\": \"LeaveApplication.documentationProvided\", \"operator\": \"equals\", \"value\": false}]}, {\"result\": \"error\", \"message\": \"Documentation is required for sick leave exceeding 3 days\"}, {\"result\": \"success\"}, LeaveApplication.validationResult)\nDescription: Validates that documentation is provided for sick leave requests exceeding 3 days.\nOutput: LeaveApplication with validationResult\nError: When documentation is not provided for sick leave exceeding 3 days, returns \"Documentation is required for sick leave exceeding 3 days\"\nValidation: PRE_INSERT on LeaveApplication entity\n\nvalidate_leave_cancellation_notice_1\nInputs: LeaveApplication with leaveId, startDate, status\nOperation: conditional_logic({\"condition\": \"AND\", \"rules\": [{\"field\": \"FUNCTION_TYPE\", \"operator\": \"equals\", \"value\": \"update\"}, {\"field\": \"LeaveApplication.status\", \"operator\": \"changedTo\", \"value\": \"Cancelled\"}, {\"field\": \"LeaveApplication.startDate\", \"operator\": \"lessThan\", \"value\": \"CURRENT_DATE + 1\"}]}, {\"result\": \"error\", \"message\": \"Cancellations must be made at least 24 hours in advance\"}, {\"result\": \"success\"}, LeaveApplication.validationResult)\nDescription: Validates that leave cancellations are made at least 24 hours in advance of the leave start date.\nOutput: LeaveApplication with validationResult\nError: When a leave request is cancelled less than 24 hours before the start date, returns \"Cancellations must be made at least 24 hours in advance\"\nValidation: PRE_UPDATE on LeaveApplication entity\n\nvalidate_manager_approval_for_long_leave_1\nInputs: LeaveApplication with numDays, managerApproval\nOperation: conditional_logic({\"condition\": \"AND\", \"rules\": [{\"field\": \"LeaveApplication.numDays\", \"operator\": \"greaterThan\", \"value\": 5}, {\"field\": \"LeaveApplication.managerApproval\", \"operator\": \"equals\", \"value\": false}]}, {\"result\": \"error\", \"message\": \"Leave requests exceeding 5 consecutive days require manager approval\"}, {\"result\": \"success\"}, LeaveApplication.validationResult)\nDescription: Validates that leave requests exceeding 5 consecutive days have manager approval.\nOutput: LeaveApplication with validationResult\nError: When a leave request exceeding 5 days does not have manager approval, returns \"Leave requests exceeding 5 consecutive days require manager approval\"\nValidation: PRE_UPDATE on LeaveApplication entity where status changed to \"Approved\"\n\nPerformance Metadata:\n- cycle_time: \"2 business days\"\n- number_of_pathways: 4\n- volume_metrics:\n  * average_volume: 120\n  * peak_volume: 250\n  * unit: \"requests/month\"\n- sla_thresholds:\n  * manager_review: \"1 business day\"\n  * notification: \"1 hour\"\n  * system_processing: \"5 minutes\"\n- critical_lo_performance:\n  * \"ReviewLeaveRequest\": \"24 hours maximum\"\n  * \"NotifyEmployee\": \"1 hour maximum\"\n  * \"UpdateCalendar\": \"5 minutes maximum\"\n  * \"UpdateLeaveBalance\": \"5 minutes maximum\"\n\nProcess Mining Schema:\n\nEvent Log Specification:\n- case_id: \"LeaveApplication.leaveId\"\n- activity: \"LO_name\"\n- event_type: \"start/complete/abort/rollback\"\n- timestamp: \"ISO-8601 datetime\"\n- resource: \"role/system_executing\"\n- duration: \"milliseconds\"\n- attributes:\n  * entity_state: \"json_snapshot\"\n  * input_values: \"input_parameters\"\n  * output_values: \"output_results\"\n  * execution_status: \"success/failure/pending\"\n  * error_details: \"error_message_if_any\"\n\nPerformance Discovery Metrics:\n- pathway_frequency:\n  * \"Standard Approval\":\n    - frequency: 95\n    - percentage: 79\n    - average_duration: \"1.5 days\"\n    - success_rate: 98\n  * \"Documentation Required\":\n    - frequency: 25\n    - percentage: 21\n    - average_duration: \"2.8 days\"\n    - success_rate: 92\n- bottleneck_analysis:\n  * \"ReviewLeaveRequest\":\n    - average_wait_time: \"1.2 days\"\n    - queue_length: 8\n    - resource_utilization: 75\n    - failure_rate: 5\n- resource_patterns:\n  * \"HR Manager\":\n    - active_hours: \"9:00-17:00 business days\"\n    - peak_load_periods: \"Monday mornings, month-end\"\n    - concurrent_executions: 12\n  * \"Notification System\":\n    - active_hours: \"24/7\"\n    - peak_load_periods: \"8:00-10:00 workdays\"\n    - concurrent_executions: 50\n\nConformance Analytics:\n- compliance_rate: 96\n- execution_variance:\n  * \"ReviewLeaveRequest\":\n    - expected_duration: \"4 hours\"\n    - actual_duration_range: \"2-24 hours\"\n    - variance_causes: [\"manager availability\", \"high request volume\", \"incomplete documentation\"]\n  * \"NotifyEmployee\":\n    - expected_duration: \"10 minutes\"\n    - actual_duration_range: \"5-25 minutes\"\n    - variance_causes: [\"notification system latency\", \"email delivery issues\"]\n- exception_patterns:\n  * \"input_timeout\":\n    - frequency: 6\n    - affected_pathways: [\"Documentation Required\"]\n    - recovery_success_rate: 85\n  * \"validation_failure\":\n    - frequency: 12\n    - most_common_failures: [\"insufficient leave balance\", \"invalid date range\"]\n    - resolution_time: \"1.5 days\"\n  * \"system_error\":\n    - frequency: 2\n    - error_categories: [\"calendar integration\", \"notification delivery\"]\n    - automatic_recovery_rate: 92\n\nAdvanced Process Intelligence:\n- process_health_score:\n  * performance_score: 88\n  * compliance_score: 96\n  * efficiency_score: 84\n  * overall_health: 89\n- prediction_models:\n  * completion_time_forecast:\n    - algorithm: \"Random Forest Regression\"\n    - accuracy: 87\n    - confidence_interval: \"±0.4 days\"\n  * failure_prediction:\n    - algorithm: \"Gradient Boosting Classification\"\n    - precision: 82\n    - recall: 79\n- optimization_insights:\n  * bottleneck_elimination: [\"Implement auto-approval for standard requests under 2 days\", \"Expand manager delegation options\"]\n  * resource_reallocation: [\"Add review capacity during Monday mornings\", \"Implement notification batching\"]\n  * pathway_optimization: [\"Streamline documentation review process\", \"Implement one-click approvals for recurring requests\"]\n\nRollback Analytics:\n- rollback_frequency: 4\n- rollback_success_rate: 99\n- rollback_triggers:\n  * \"user_initiated\":\n    - frequency: 18\n    - average_impact_scope: 2\n    - average_recovery_time: \"2 hours\"\n  * \"system_error\":\n    - frequency: 2\n    - average_impact_scope: 1\n    - average_recovery_time: \"1 hour\"\n- rollback_pathways:\n  * \"reject_leave_request\":\n    - frequency: 16\n    - success_rate: 99\n    - average_completion_time: \"2.5 hours\"\n  * \"update_leave_balance\":\n    - frequency: 4\n    - success_rate: 95\n    - average_completion_time: \"1.2 hours\"\n    \nValidation Rules for GO Creation:\n\nvalidate_lo_natural_language_names\nInputs: GO with pathways\nOperation: validate_all(GO.pathways, \"name\", matches_pattern(\"[A-Z][a-zA-Z]+\"))\nDescription: Validates that all LOs are defined with natural language names\nOutput: ValidationResult with status, message\nError: When LO names don't follow natural language pattern, returns \"All LOs must be defined with natural language names\"\nValidation: PRE_DEPLOY on GO entity\n\nvalidate_referenced_los_defined\nInputs: GO with pathways, routings\nOperation: validate_references(GO.routings, \"target\", exists_in(GO.pathways, \"name\"))\nDescription: Validates that every referenced LO is defined in Process Flow section\nOutput: ValidationResult with status, message\nError: When routing references undefined LO, returns \"All referenced LOs must be defined in Process Flow section\"\nValidation: PRE_DEPLOY on GO entity\n\nvalidate_first_lo_human_initiated\nInputs: GO with pathways\nOperation: validate_equals(GO.pathways[0].actor_type, \"HUMAN\")\nDescription: Validates that the first LO is human-initiated\nOutput: ValidationResult with status, message\nError: When first LO is not human-initiated, returns \"First LO must be human-initiated\"\nValidation: PRE_DEPLOY on GO entity\n\nvalidate_terminal_pathways\nInputs: GO with pathways, routings\nOperation: validate_all_paths_terminate(GO.pathways, GO.routings)\nDescription: Validates that all pathways terminate in completion or cancellation\nOutput: ValidationResult with status, message\nError: When pathway doesn't terminate, returns \"All pathways must terminate in completion or cancellation\"\nValidation: PRE_DEPLOY on GO entity\n\nvalidate_rollback_pathways\nInputs: GO with pathways, rollback_pathways\nOperation: validate_all_exist(GO.rollback_pathways, GO.pathways, \"name\")\nDescription: Validates that rollback LOs are included in Process Flow\nOutput: ValidationResult with status, message\nError: When rollback pathway references undefined LO, returns \"All rollback LOs must be included in Process Flow\"\nValidation: PRE_DEPLOY on GO entity\n\nvalidate_parallel_flows\nInputs: GO with pathways, parallel_flows\nOperation: validate_all_exist(GO.parallel_flows, GO.pathways, \"name\")\nDescription: Validates that parallel flows reference defined LOs\nOutput: ValidationResult with status, message\nError: When parallel flow references undefined LO, returns \"All parallel flows must reference defined LOs\"\nValidation: PRE_DEPLOY on GO entity\n\nvalidate_performance_metadata\nInputs: GO with performance_metadata\nOperation: validate_required_fields(GO.performance_metadata, [\"cycle_time\", \"number_of_pathways\", \"volume_metrics\", \"sla_thresholds\", \"critical_lo_performance\"])\nDescription: Validates that all required performance metadata is specified\nOutput: ValidationResult with status, message\nError: When required performance metadata is missing, returns \"All required performance metadata must be specified\"\nValidation: PRE_DEPLOY on GO entity", "routes": ["GO1.LO8"]}]}}, "validation_errors": null, "parsed_gos": {}, "parsed_los": {}}