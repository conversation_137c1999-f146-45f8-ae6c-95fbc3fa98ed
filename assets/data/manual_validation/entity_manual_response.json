{"success": true, "messages": ["Entity validation successful"], "parsed_data": {"entities": {"Loan": {"entity_id": "E17", "name": "Loan", "natural_language": "Loan has loanId^PK, customerId^FK, loanAmount, interestRate, term, startDate, endDate, status (Active, Closed, Default, Restructured), paymentFrequency (Monthly, Biweekly, Weekly, Quarterly, Annually), totalPaymentsMade, remainingBalance[derived], loanType (Personal, Mortgage, Auto, Education, Business), collateralId^FK, originationFee, lateFeePercentage, earlyPaymentPenalty.", "attributes": {"loanId": {"name": "loanId", "primary_key": true, "foreign_key": false, "calculated": false, "data_type": "Integer", "natural_language": "loanId^PK", "validations": [{"constraint": "be unique", "id": "val_1"}], "default_value": "N/A"}, "customerId": {"name": "customerId", "primary_key": false, "foreign_key": true, "calculated": false, "data_type": "Integer", "natural_language": "customerId^FK", "default_value": "N/A"}, "loanAmount": {"name": "loanAmount", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Decimal", "natural_language": "loanAmount", "validations": [{"constraint": "be greater than 0", "id": "val_2"}], "default_value": "N/A"}, "interestRate": {"name": "interestRate", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Decimal", "natural_language": "interestRate", "validations": [{"constraint": "be greater than or equal to 1", "id": "val_3"}], "default_value": "5.99"}, "term": {"name": "term", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Integer", "natural_language": "term", "validations": [{"constraint": "be greater than or equal to 3", "id": "val_4"}], "default_value": "60"}, "startDate": {"name": "startDate", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Date", "natural_language": "startDate", "validations": [{"constraint": "not be in the future", "id": "val_5"}], "default_value": "CURRENT_DATE"}, "endDate": {"name": "endDate", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Date", "natural_language": "endDate", "validations": [{"constraint": "be after startDate", "id": "val_6"}], "default_value": "N/A"}, "status": {"name": "status", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Enum", "natural_language": "status (Active, Closed, Default, Restructured)", "enum_values": ["Active", "Closed", "<PERSON><PERSON><PERSON>", "Restructured"], "default_value": "Active"}, "paymentFrequency": {"name": "paymentFrequency", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Enum", "natural_language": "paymentFrequency (Monthly, Biweekly, Weekly, Quarterly, Annually)", "enum_values": ["Monthly", "Biweekly", "Weekly", "Quarterly", "Annually"], "default_value": "Monthly"}, "totalPaymentsMade": {"name": "totalPaymentsMade", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Integer", "natural_language": "totalPaymentsMade", "default_value": "0"}, "remainingBalance": {"name": "remainingBalance", "primary_key": false, "foreign_key": false, "calculated": true, "data_type": "Decimal", "natural_language": "remainingBalance[derived]", "default_value": "N/A"}, "loanType": {"name": "loanType", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Enum", "natural_language": "loanType (Personal, Mortgage, Auto, Education, Business)", "enum_values": ["Personal", "Mortgage", "Auto", "Education", "Business"], "default_value": "Personal"}, "collateralId": {"name": "collateralId", "primary_key": false, "foreign_key": true, "calculated": false, "data_type": "Integer", "natural_language": "collateralId^FK", "default_value": "N/A"}, "originationFee": {"name": "originationFee", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Decimal", "natural_language": "originationFee", "default_value": "0.00"}, "lateFeePercentage": {"name": "lateFeePercentage", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Decimal", "natural_language": "lateFeePercentage", "default_value": "5.00"}, "earlyPaymentPenalty": {"name": "earlyPaymentPenalty", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Decimal", "natural_language": "earlyPaymentPenalty", "default_value": "1.00"}}, "relationships": {"rel_1": {"entity": "Customer", "type": "many-to-one", "source_attribute": "customerId", "target_attribute": "customerId", "source_entity": "Loan", "target_entity": "Customer", "cardinality": {"source": "n", "target": "1"}, "join_condition": "Loan.customerId = Customer.customerId", "cascade_operations": {"delete": "restrict", "update": "cascade"}, "natural_language": "-Loan has many-to-one relationship with Customer using Loan.customerId to Customer.customerId^PK"}, "rel_2": {"entity": "Collateral", "type": "many-to-one", "source_attribute": "collateralId", "target_attribute": "collateralId", "source_entity": "Loan", "target_entity": "Collateral", "cardinality": {"source": "n", "target": "1"}, "join_condition": "Loan.collateralId = Collateral.collateralId", "cascade_operations": {"delete": "restrict", "update": "cascade"}, "natural_language": "-Loan has many-to-one relationship with Collateral using Loan.collateralId to Collateral.collateralId^PK"}}, "business_rules": {}, "calculated_fields": {}, "validations": {"val_1": {"attribute": "loanId", "constraint_text": "be unique", "entity": "Loan", "validation_type": "unique", "parameters": {}, "executable_rule": "IS_UNIQUE(loanId)", "natural_language": "-Loan.loanId must be unique"}, "val_2": {"attribute": "loanAmount", "constraint_text": "be greater than 0", "entity": "Loan", "validation_type": "greater_than", "parameters": {"value": "0"}, "executable_rule": "loanAmount > 0", "natural_language": "-Loan.loanAmount must be greater than 0"}, "val_3": {"attribute": "interestRate", "constraint_text": "be greater than or equal to 1", "entity": "Loan", "validation_type": "min_value", "parameters": {"min_value": "1"}, "executable_rule": "interestRate >= 1", "natural_language": "-Loan.interestRate must be greater than or equal to 1.0"}, "val_4": {"attribute": "term", "constraint_text": "be greater than or equal to 3", "entity": "Loan", "validation_type": "min_value", "parameters": {"min_value": "3"}, "executable_rule": "term >= 3", "natural_language": "-Loan.term must be greater than or equal to 3"}, "val_5": {"attribute": "startDate", "constraint_text": "not be in the future", "entity": "Loan", "validation_type": "not_future", "parameters": {}, "executable_rule": "startDate <= CURRENT_DATE", "natural_language": "-Loan.startDate must not be in the future"}, "val_6": {"attribute": "endDate", "constraint_text": "be after startDate", "entity": "Loan", "validation_type": "after_date", "parameters": {"reference_attribute": "startDate"}, "executable_rule": "endDate > startDate", "natural_language": "-Loan.endDate must be after startDate"}}, "constraints": {}, "attribute_metadata": {"loanId": {"natural_language": "-Attribute name: loanId\n-Key: Primary\n-Display Name: Loan ID\n-DataType: Integer\n-Required: Mandatory\n-Format: \"LN-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Auto-increment\n-Error Message: \"Invalid Loan ID format\"\n-Description: Unique identifier for the loan", "key_type": "Primary", "display_name": "Loan ID", "data_type": "Integer", "data_type_full": "Integer", "type": "mandatory", "required": "Mandatory", "format": "\"LN-######\"", "values": "N/A", "default": "N/A", "validation": "Auto-increment", "error_message": "Invalid Loan ID format", "description": "Unique identifier for the loan"}, "customerId": {"natural_language": "-Attribute name: customerId\n-Key: Foreign\n-Display Name: Customer ID\n-DataType: Integer\n-Required: Mandatory\n-Format: \"CUS-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Foreign Key Check\n-Error Message: \"Customer ID must reference an existing customer\"\n-Description: References the customer who took the loan", "key_type": "Foreign", "display_name": "Customer ID", "data_type": "Integer", "data_type_full": "Integer", "type": "mandatory", "required": "Mandatory", "format": "\"CUS-######\"", "values": "N/A", "default": "N/A", "validation": "Foreign Key Check", "error_message": "Customer ID must reference an existing customer", "description": "References the customer who took the loan"}, "loanAmount": {"natural_language": "-Attribute name: loanAmount\n-Key: Non-unique\n-Display Name: Principal <PERSON><PERSON> Amount\n-DataType: Decimal\n-Required: Mandatory\n-Format: \"$#,###.00\"\n-Values: 1000.00-10000000.00\n-Default: N/A\n-Validation: Range Check\n-Error Message: \"Loan amount must be between $1,000 and $10,000,000\"\n-Description: The principal amount borrowed by the customer", "key_type": "Non-unique", "display_name": "Principal <PERSON><PERSON>", "data_type": "Decimal", "data_type_full": "Decimal", "type": "mandatory", "required": "Mandatory", "format": "\"$#,###.00\"", "values": "1000.00-10000000.00", "default": "N/A", "validation": "Range Check", "error_message": "Loan amount must be between $1,000 and $10,000,000", "description": "The principal amount borrowed by the customer"}, "interestRate": {"natural_language": "-Attribute name: interestRate\n-Key: Non-unique\n-Display Name: Annual Interest Rate (%)\n-DataType: Decimal\n-Required: Mandatory\n-Format: \"0.00%\"\n-Values: 1.00-30.00\n-Default: \"5.99\"\n-Validation: Range Check\n-Error Message: \"Interest rate must be between 1.00% and 30.00%\"\n-Description: Annual interest rate applied to the loan", "key_type": "Non-unique", "display_name": "Annual Interest Rate (%)", "data_type": "Decimal", "data_type_full": "Decimal", "type": "mandatory", "required": "Mandatory", "format": "\"0.00%\"", "values": "1.00-30.00", "default": "\"5.99\"", "validation": "Range Check", "error_message": "Interest rate must be between 1.00% and 30.00%", "description": "Annual interest rate applied to the loan"}, "term": {"natural_language": "-Attribute name: term\n-Key: Non-unique\n-Display Name: <PERSON><PERSON> (Months)\n-DataType: Integer\n-Required: Mandatory\n-Format: \"### months\"\n-Values: 3-480\n-Default: \"60\"\n-Validation: Range Check\n-Error Message: \"Term must be between 3 and 480 months\"\n-Description: Duration of the loan in months", "key_type": "Non-unique", "display_name": "<PERSON><PERSON> (Months)", "data_type": "Integer", "data_type_full": "Integer", "type": "mandatory", "required": "Mandatory", "format": "\"### months\"", "values": "3-480", "default": "\"60\"", "validation": "Range Check", "error_message": "Term must be between 3 and 480 months", "description": "Duration of the loan in months"}, "startDate": {"natural_language": "-Attribute name: startDate\n-Key: Non-unique\n-Display Name: Loan Start Date\n-DataType: Date\n-Required: Mandatory\n-Format: \"YYYY-MM-DD\"\n-Values: N/A\n-Default: \"CURRENT_DATE\"\n-Validation: Date Check\n-Error Message: \"Start date cannot be in the future\"\n-Description: Date when the loan becomes active", "key_type": "Non-unique", "display_name": "Loan Start Date", "data_type": "Date", "data_type_full": "Date", "type": "mandatory", "required": "Mandatory", "format": "\"YYYY-MM-DD\"", "values": "N/A", "default": "\"CURRENT_DATE\"", "validation": "Date Check", "error_message": "Start date cannot be in the future", "description": "Date when the loan becomes active"}, "endDate": {"natural_language": "-Attribute name: endDate\n-Key: Non-unique\n-Display Name: Loan End Date\n-DataType: Date\n-Required: Mandatory\n-Format: \"YYYY-MM-DD\"\n-Values: N/A\n-Default: N/A\n-Validation: Date Check\n-Error Message: \"End date must be after start date\"\n-Description: Scheduled date for loan completion", "key_type": "Non-unique", "display_name": "Loan End Date", "data_type": "Date", "data_type_full": "Date", "type": "mandatory", "required": "Mandatory", "format": "\"YYYY-MM-DD\"", "values": "N/A", "default": "N/A", "validation": "Date Check", "error_message": "End date must be after start date", "description": "Scheduled date for loan completion"}, "status": {"natural_language": "-Attribute name: status\n-Key: Non-unique\n-Display Name: Loan Status\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Active\", \"Closed\", \"Default\", \"Restructured\"\n-Default: \"Active\"\n-Validation: List Check\n-Error Message: \"Invalid loan status\"\n-Description: Current status of the loan", "key_type": "Non-unique", "display_name": "Loan Status", "data_type": "Enum", "data_type_full": "Enum", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"Active\", \"Closed\", \"Default\", \"Restructured\"", "default": "\"Active\"", "validation": "List Check", "error_message": "Invalid loan status", "description": "Current status of the loan"}, "paymentFrequency": {"natural_language": "-Attribute name: paymentFrequency\n-Key: Non-unique\n-Display Name: Payment Frequency\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Monthly\", \"Biweekly\", \"Weekly\", \"Quarterly\", \"Annually\"\n-Default: \"Monthly\"\n-Validation: List Check\n-Error Message: \"Invalid payment frequency\"\n-Description: How often payments are made on the loan", "key_type": "Non-unique", "display_name": "Payment Frequency", "data_type": "Enum", "data_type_full": "Enum", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"Monthly\", \"Biweekly\", \"Weekly\", \"Quarterly\", \"Annually\"", "default": "\"Monthly\"", "validation": "List Check", "error_message": "Invalid payment frequency", "description": "How often payments are made on the loan"}, "totalPaymentsMade": {"natural_language": "-Attribute name: totalPaymentsMade\n-Key: Non-unique\n-Display Name: Total Payments Made\n-DataType: Integer\n-Required: Mandatory\n-Format: \"###\"\n-Values: 0-N\n-Default: \"0\"\n-Validation: Range Check\n-Error Message: \"Total payments cannot be negative\"\n-Description: Number of payments completed for this loan", "key_type": "Non-unique", "display_name": "Total Payments Made", "data_type": "Integer", "data_type_full": "Integer", "type": "mandatory", "required": "Mandatory", "format": "\"###\"", "values": "0-N", "default": "\"0\"", "validation": "Range Check", "error_message": "Total payments cannot be negative", "description": "Number of payments completed for this loan"}, "remainingBalance": {"natural_language": "-Attribute name: remainingBalance\n-Key: Non-unique\n-Display Name: Remaining Balance\n-DataType: Decimal\n-Required: Calculated\n-Format: \"$#,###.00\"\n-Values: 0.00-N\n-Default: N/A\n-Validation: Calculation\n-Error Message: N/A\n-Description: Current outstanding balance on the loan", "key_type": "Non-unique", "display_name": "Remaining Balance", "data_type": "Decimal", "data_type_full": "Decimal", "type": "calculated", "required": "Calculated", "format": "\"$#,###.00\"", "values": "0.00-N", "default": "N/A", "validation": "Calculation", "description": "Current outstanding balance on the loan"}, "loanType": {"natural_language": "-Attribute name: loanType\n-Key: Non-unique\n-Display Name: Loan Type\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Personal\", \"Mortgage\", \"Auto\", \"Education\", \"Business\"\n-Default: \"Personal\"\n-Validation: List Check\n-Error Message: \"Invalid loan type\"\n-Description: Category of loan based on purpose", "key_type": "Non-unique", "display_name": "Loan Type", "data_type": "Enum", "data_type_full": "Enum", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"Personal\", \"Mortgage\", \"Auto\", \"Education\", \"Business\"", "default": "\"Personal\"", "validation": "List Check", "error_message": "Invalid loan type", "description": "Category of loan based on purpose"}, "collateralId": {"natural_language": "-Attribute name: collateralId\n-Key: Foreign\n-Display Name: Collateral ID\n-DataType: Integer\n-Required: Optional\n-Format: \"COL-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Foreign Key Check\n-Error Message: \"Collateral ID must reference existing collateral\"\n-Description: References the asset used to secure the loan", "key_type": "Foreign", "display_name": "Collateral ID", "data_type": "Integer", "data_type_full": "Integer", "type": "optional", "required": "Optional", "format": "\"COL-######\"", "values": "N/A", "default": "N/A", "validation": "Foreign Key Check", "error_message": "Collateral ID must reference existing collateral", "description": "References the asset used to secure the loan"}, "originationFee": {"natural_language": "-Attribute name: originationFee\n-Key: Non-unique\n-Display Name: Origination Fee\n-DataType: Decimal\n-Required: Optional\n-Format: \"$#,###.00\"\n-Values: 0.00-N\n-Default: \"0.00\"\n-Validation: Range Check\n-Error Message: \"Origination fee cannot be negative\"\n-Description: Fee charged for processing a new loan application", "key_type": "Non-unique", "display_name": "Origination Fee", "data_type": "Decimal", "data_type_full": "Decimal", "type": "optional", "required": "Optional", "format": "\"$#,###.00\"", "values": "0.00-N", "default": "\"0.00\"", "validation": "Range Check", "error_message": "Origination fee cannot be negative", "description": "Fee charged for processing a new loan application"}, "lateFeePercentage": {"natural_language": "-Attribute name: lateFeePercentage\n-Key: Non-unique\n-Display Name: Late Fee Percentage\n-DataType: Decimal\n-Required: Optional\n-Format: \"0.00%\"\n-Values: 0.00-20.00\n-Default: \"5.00\"\n-Validation: Range Check\n-Error Message: \"Late fee percentage must be between 0% and 20%\"\n-Description: Percentage charged on late payments", "key_type": "Non-unique", "display_name": "Late Fee Percentage", "data_type": "Decimal", "data_type_full": "Decimal", "type": "optional", "required": "Optional", "format": "\"0.00%\"", "values": "0.00-20.00", "default": "\"5.00\"", "validation": "Range Check", "error_message": "Late fee percentage must be between 0% and 20%", "description": "Percentage charged on late payments"}, "earlyPaymentPenalty": {"natural_language": "-Attribute name: earlyPaymentPenalty\n-Key: Non-unique\n-Display Name: Early Payment Penalty\n-DataType: Decimal\n-Required: Optional\n-Format: \"0.00%\"\n-Values: 0.00-10.00\n-Default: \"1.00\"\n-Validation: Range Check\n-Error Message: \"Early payment penalty must be between 0% and 10%\"\n-Description: Penalty charged for early loan payoff", "key_type": "Non-unique", "display_name": "Early Payment Penalty", "data_type": "Decimal", "data_type_full": "Decimal", "type": "optional", "required": "Optional", "format": "\"0.00%\"", "values": "0.00-10.00", "default": "\"1.00\"", "validation": "Range Check", "error_message": "Early payment penalty must be between 0% and 10%", "description": "Penalty charged for early loan payoff"}}, "relationship_properties": {"rel_props_Loan_Customer": {"source_entity": "Loan", "target_entity": "Customer", "on_delete": "Restrict (Prevent deletion of customer with active loans)", "on_update": "Cascade (Update loan records when customer details change)", "foreign_key_type": "Non-Nullable"}}, "synthetic_data": [], "confidential_attributes": [], "internal_attributes": [], "public_attributes": [], "loading_strategies": {}, "archive_strategy": {}, "history_tracking": {}, "workflow": {}, "business_rule_placement": {}, "workflow_placement": {}, "entity_placement": {}, "display_name": "Loan Agreement", "type": "Core Entity", "description": "Represents a loan agreement between the financial institution and a customer.", "enum_attributes": {"status": {"name": "status", "values": ["Active", "Closed", "<PERSON><PERSON><PERSON>", "Restructured"], "source_text": "status (Active, Closed, Default, Restructured)"}, "paymentFrequency": {"name": "paymentFrequency", "values": ["Monthly", "Biweekly", "Weekly", "Quarterly", "Annually"], "source_text": "paymentFrequency (Monthly, Biweekly, Weekly, Quarterly, Annually)"}, "loanType": {"name": "loanType", "values": ["Personal", "Mortgage", "Auto", "Education", "Business"], "source_text": "loanType (Personal, Mortgage, Auto, Education, Business)"}}, "enum_values_list": ["Active", "Closed", "<PERSON><PERSON><PERSON>", "Restructured", "Monthly", "Biweekly", "Weekly", "Quarterly", "Annually", "Personal", "Mortgage", "Auto", "Education", "Business"], "entity_name": "Loan"}, "Customer": {"entity_id": "E18", "name": "Customer", "natural_language": "Customer has customerId^PK, firstName, lastName, email, phone, address, city, state, zipCode, country, dateOfBirth, ssn, creditScore, annualIncome, employmentStatus (Employed, Self-Employed, Unemployed, Retired), employerName, employmentLength, customerType (Individual, Business), status (Active, Inactive, Suspended).", "attributes": {"customerId": {"name": "customerId", "primary_key": true, "foreign_key": false, "calculated": false, "data_type": "Integer", "natural_language": "customerId^PK", "validations": [{"constraint": "be unique", "id": "val_1"}], "default_value": "N/A"}, "firstName": {"name": "firstName", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "firstName", "default_value": "N/A"}, "lastName": {"name": "lastName", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "lastName", "default_value": "N/A"}, "email": {"name": "email", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "email", "validations": [{"constraint": "be a valid email format", "id": "val_2"}], "default_value": "N/A"}, "phone": {"name": "phone", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "phone", "default_value": "N/A"}, "address": {"name": "address", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "address", "default_value": "N/A"}, "city": {"name": "city", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "city", "default_value": "N/A"}, "state": {"name": "state", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "state", "default_value": "N/A"}, "zipCode": {"name": "zipCode", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "zipCode", "default_value": "N/A"}, "country": {"name": "country", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "country", "default_value": "USA"}, "dateOfBirth": {"name": "dateOfBirth", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Date", "natural_language": "dateOfBirth", "validations": [{"constraint": "indicate customer is at least 18 years old", "id": "val_6"}], "default_value": "N/A"}, "ssn": {"name": "ssn", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "ssn", "validations": [{"constraint": "be a valid format and unique", "id": "val_3"}], "default_value": "N/A"}, "creditScore": {"name": "creditScore", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Integer", "natural_language": "creditScore", "validations": [{"constraint": "be between 300 and 850", "id": "val_4"}], "default_value": "N/A"}, "annualIncome": {"name": "annualIncome", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Decimal", "natural_language": "annualIncome", "validations": [{"constraint": "be greater than 0", "id": "val_5"}], "default_value": "N/A"}, "employmentStatus": {"name": "employmentStatus", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Enum", "natural_language": "employmentStatus (Employed, Self-Employed, Unemployed, Retired)", "enum_values": ["Employed", "Self-Employed", "Unemployed", "Retired"], "default_value": "Employed"}, "employerName": {"name": "employerName", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "employerName", "default_value": "N/A"}, "employmentLength": {"name": "employmentLength", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Integer", "natural_language": "employmentLength", "default_value": "0"}, "customerType": {"name": "customerType", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Enum", "natural_language": "customerType (Individual, Business)", "enum_values": ["Individual", "Business"], "default_value": "Individual"}, "status": {"name": "status", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Enum", "natural_language": "status (Active, Inactive, Suspended)", "enum_values": ["Active", "Inactive", "Suspended"], "default_value": "Active"}}, "relationships": {"rel_1": {"entity": "Loan", "type": "one-to-many", "source_attribute": "customerId", "target_attribute": "customerId", "source_entity": "Customer", "target_entity": "Loan", "cardinality": {"source": "1", "target": "n"}, "join_condition": "Customer.customerId = Loan.customerId", "cascade_operations": {"delete": "restrict", "update": "cascade"}, "natural_language": "-Customer has one-to-many relationship with <PERSON><PERSON> using Customer.customerId to Loan.customerId^FK"}, "rel_2": {"entity": "Collateral", "type": "one-to-many", "source_attribute": "customerId", "target_attribute": "customerId", "source_entity": "Customer", "target_entity": "Collateral", "cardinality": {"source": "1", "target": "n"}, "join_condition": "Customer.customerId = Collateral.customerId", "cascade_operations": {"delete": "restrict", "update": "cascade"}, "natural_language": "-Customer has one-to-many relationship with Collateral using Customer.customerId to Collateral.customerId^FK"}}, "business_rules": {}, "calculated_fields": {}, "validations": {"val_1": {"attribute": "customerId", "constraint_text": "be unique", "entity": "Customer", "validation_type": "unique", "parameters": {}, "executable_rule": "IS_UNIQUE(customerId)", "natural_language": "-Customer.customerId must be unique"}, "val_2": {"attribute": "email", "constraint_text": "be a valid email format", "entity": "Customer", "validation_type": "custom", "parameters": {}, "executable_rule": "CUSTOM_VALIDATION(email)", "natural_language": "-Customer.email must be a valid email format"}, "val_3": {"attribute": "ssn", "constraint_text": "be a valid format and unique", "entity": "Customer", "validation_type": "unique", "parameters": {}, "executable_rule": "IS_UNIQUE(ssn)", "natural_language": "-Customer.ssn must be a valid format and unique"}, "val_4": {"attribute": "creditScore", "constraint_text": "be between 300 and 850", "entity": "Customer", "validation_type": "range", "parameters": {"min_value": "300", "max_value": "850"}, "executable_rule": "(creditScore >= 300 AND creditScore <= 850)", "natural_language": "-Customer.creditScore must be between 300 and 850"}, "val_5": {"attribute": "annualIncome", "constraint_text": "be greater than 0", "entity": "Customer", "validation_type": "greater_than", "parameters": {"value": "0"}, "executable_rule": "annualIncome > 0", "natural_language": "-Customer.annualIncome must be greater than 0"}, "val_6": {"attribute": "dateOfBirth", "constraint_text": "indicate customer is at least 18 years old", "entity": "Customer", "validation_type": "enum", "parameters": {}, "executable_rule": "dateOfBirth IS NOT NULL", "natural_language": "-Customer.dateOfBirth must indicate customer is at least 18 years old"}}, "constraints": {}, "attribute_metadata": {"customerId": {"natural_language": "-Attribute name: customerId\n-Key: Primary\n-Display Name: Customer ID\n-DataType: Integer\n-Required: Mandatory\n-Format: \"CUS-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Auto-increment\n-Error Message: \"Invalid Customer ID format\"\n-Description: Unique identifier for the customer", "key_type": "Primary", "display_name": "Customer ID", "data_type": "Integer", "data_type_full": "Integer", "type": "mandatory", "required": "Mandatory", "format": "\"CUS-######\"", "values": "N/A", "default": "N/A", "validation": "Auto-increment", "error_message": "Invalid Customer ID format", "description": "Unique identifier for the customer"}, "firstName": {"natural_language": "-Attribute name: firstName\n-Key: Non-unique\n-Display Name: First Name\n-DataType: String\n-Required: Mandatory\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: \"First name is required\"\n-Description: Customer's first name", "key_type": "Non-unique", "display_name": "First Name", "data_type": "String", "data_type_full": "String", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "error_message": "First name is required", "description": "Customer's first name"}, "lastName": {"natural_language": "-Attribute name: lastName\n-Key: Non-unique\n-Display Name: Last Name\n-DataType: String\n-Required: Mandatory\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: \"Last name is required\"\n-Description: Customer's last name", "key_type": "Non-unique", "display_name": "Last Name", "data_type": "String", "data_type_full": "String", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "error_message": "Last name is required", "description": "Customer's last name"}, "email": {"natural_language": "-Attribute name: email\n-Key: Non-unique\n-Display Name: Email Address\n-DataType: String\n-Required: Mandatory\n-Format: \"<EMAIL>\"\n-Values: N/A\n-Default: N/A\n-Validation: Format Check\n-Error Message: \"Invalid email format\"\n-Description: Customer's email address", "key_type": "Non-unique", "display_name": "Email Address", "data_type": "String", "data_type_full": "String", "type": "mandatory", "required": "Mandatory", "format": "\"<EMAIL>\"", "values": "N/A", "default": "N/A", "validation": "Format Check", "error_message": "Invalid email format", "description": "Customer's email address"}, "phone": {"natural_language": "-Attribute name: phone\n-Key: Non-unique\n-Display Name: Phone Number\n-DataType: String\n-Required: Mandatory\n-Format: \"+#-###-###-####\"\n-Values: N/A\n-Default: N/A\n-Validation: Format Check\n-Error Message: \"Invalid phone number format\"\n-Description: Customer's contact phone number", "key_type": "Non-unique", "display_name": "Phone Number", "data_type": "String", "data_type_full": "String", "type": "mandatory", "required": "Mandatory", "format": "\"+#-###-###-####\"", "values": "N/A", "default": "N/A", "validation": "Format Check", "error_message": "Invalid phone number format", "description": "Customer's contact phone number"}, "address": {"natural_language": "-Attribute name: address\n-Key: Non-unique\n-Display Name: Street Address\n-DataType: String\n-Required: Mandatory\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: \"Address is required\"\n-Description: Customer's street address", "key_type": "Non-unique", "display_name": "Street Address", "data_type": "String", "data_type_full": "String", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "error_message": "Address is required", "description": "Customer's street address"}, "city": {"natural_language": "-Attribute name: city\n-Key: Non-unique\n-Display Name: City\n-DataType: String\n-Required: Mandatory\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: \"City is required\"\n-Description: Customer's city of residence", "key_type": "Non-unique", "display_name": "City", "data_type": "String", "data_type_full": "String", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "error_message": "City is required", "description": "Customer's city of residence"}, "state": {"natural_language": "-Attribute name: state\n-Key: Non-unique\n-Display Name: State/Province\n-DataType: String\n-Required: Mandatory\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: \"State is required\"\n-Description: Customer's state of residence", "key_type": "Non-unique", "display_name": "State/Province", "data_type": "String", "data_type_full": "String", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "error_message": "State is required", "description": "Customer's state of residence"}, "zipCode": {"natural_language": "-Attribute name: zipCode\n-Key: Non-unique\n-Display Name: ZIP/Postal Code\n-DataType: String\n-Required: Mandatory\n-Format: \"#####\" or \"#####-####\"\n-Values: N/A\n-Default: N/A\n-Validation: Format Check\n-Error Message: \"Invalid zip code format\"\n-Description: Customer's zip code", "key_type": "Non-unique", "display_name": "ZIP/Postal Code", "data_type": "String", "data_type_full": "String", "type": "mandatory", "required": "Mandatory", "format": "\"#####\" or \"#####-####\"", "values": "N/A", "default": "N/A", "validation": "Format Check", "error_message": "Invalid zip code format", "description": "Customer's zip code"}, "country": {"natural_language": "-Attribute name: country\n-Key: Non-unique\n-Display Name: Country\n-DataType: String\n-Required: Mandatory\n-Format: N/A\n-Values: N/A\n-Default: \"USA\"\n-Validation: Length Check\n-Error Message: \"Country is required\"\n-Description: Customer's country of residence", "key_type": "Non-unique", "display_name": "Country", "data_type": "String", "data_type_full": "String", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "N/A", "default": "\"USA\"", "validation": "Length Check", "error_message": "Country is required", "description": "Customer's country of residence"}, "dateOfBirth": {"natural_language": "-Attribute name: dateOfBirth\n-Key: Non-unique\n-Display Name: Date of Birth\n-DataType: Date\n-Required: Mandatory\n-Format: \"YYYY-MM-DD\"\n-Values: N/A\n-Default: N/A\n-Validation: Age Check\n-Error Message: \"Customer must be at least 18 years old\"\n-Description: Customer's date of birth", "key_type": "Non-unique", "display_name": "Date of Birth", "data_type": "Date", "data_type_full": "Date", "type": "mandatory", "required": "Mandatory", "format": "\"YYYY-MM-DD\"", "values": "N/A", "default": "N/A", "validation": "Age Check", "error_message": "Customer must be at least 18 years old", "description": "Customer's date of birth"}, "ssn": {"natural_language": "-Attribute name: ssn\n-Key: Non-unique\n-Display Name: Social Security Number\n-DataType: String\n-Required: Mandatory\n-Format: \"###-##-####\"\n-Values: N/A\n-Default: N/A\n-Validation: Format Check\n-Error Message: \"Invalid SSN format\"\n-Description: Customer's Social Security Number", "key_type": "Non-unique", "display_name": "Social Security Number", "data_type": "String", "data_type_full": "String", "type": "mandatory", "required": "Mandatory", "format": "\"###-##-####\"", "values": "N/A", "default": "N/A", "validation": "Format Check", "error_message": "Invalid SSN format", "description": "Customer's Social Security Number"}, "creditScore": {"natural_language": "-Attribute name: creditScore\n-Key: Non-unique\n-Display Name: Credit Score\n-DataType: Integer\n-Required: Mandatory\n-Format: \"###\"\n-Values: 300-850\n-Default: N/A\n-Validation: Range Check\n-Error Message: \"Credit score must be between 300 and 850\"\n-Description: Customer's credit score", "key_type": "Non-unique", "display_name": "Credit Score", "data_type": "Integer", "data_type_full": "Integer", "type": "mandatory", "required": "Mandatory", "format": "\"###\"", "values": "300-850", "default": "N/A", "validation": "Range Check", "error_message": "Credit score must be between 300 and 850", "description": "Customer's credit score"}, "annualIncome": {"natural_language": "-Attribute name: annualIncome\n-Key: Non-unique\n-Display Name: Annual Income\n-DataType: Decimal\n-Required: Mandatory\n-Format: \"$#,###.00\"\n-Values: 0.00-N\n-Default: N/A\n-Validation: Range Check\n-Error Message: \"Annual income must be greater than 0\"\n-Description: Customer's annual income", "key_type": "Non-unique", "display_name": "Annual Income", "data_type": "Decimal", "data_type_full": "Decimal", "type": "mandatory", "required": "Mandatory", "format": "\"$#,###.00\"", "values": "0.00-N", "default": "N/A", "validation": "Range Check", "error_message": "Annual income must be greater than 0", "description": "Customer's annual income"}, "employmentStatus": {"natural_language": "-Attribute name: employmentStatus\n-Key: Non-unique\n-Display Name: Employment Status\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Employed\", \"Self-Employed\", \"Unemployed\", \"Retired\"\n-Default: \"Employed\"\n-Validation: List Check\n-Error Message: \"Invalid employment status\"\n-Description: Customer's current employment status", "key_type": "Non-unique", "display_name": "Employment Status", "data_type": "Enum", "data_type_full": "Enum", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"Employed\", \"Self-Employed\", \"Unemployed\", \"Retired\"", "default": "\"Employed\"", "validation": "List Check", "error_message": "Invalid employment status", "description": "Customer's current employment status"}, "employerName": {"natural_language": "-Attribute name: employerName\n-Key: Non-unique\n-Display Name: Employer Name\n-DataType: String\n-Required: Optional\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: N/A\n-Description: Name of customer's employer", "key_type": "Non-unique", "display_name": "Employer Name", "data_type": "String", "data_type_full": "String", "type": "optional", "required": "Optional", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "description": "Name of customer's employer"}, "employmentLength": {"natural_language": "-Attribute name: employmentLength\n-Key: Non-unique\n-Display Name: Employment Length\n-DataType: Integer\n-Required: Optional\n-Format: \"## years\"\n-Values: 0-N\n-Default: \"0\"\n-Validation: Range Check\n-Error Message: \"Employment length cannot be negative\"\n-Description: Length of employment in years", "key_type": "Non-unique", "display_name": "Employment Length", "data_type": "Integer", "data_type_full": "Integer", "type": "optional", "required": "Optional", "format": "\"## years\"", "values": "0-N", "default": "\"0\"", "validation": "Range Check", "error_message": "Employment length cannot be negative", "description": "Length of employment in years"}, "customerType": {"natural_language": "-Attribute name: customerType\n-Key: Non-unique\n-Display Name: Customer Type\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Individual\", \"Business\"\n-Default: \"Individual\"\n-Validation: List Check\n-Error Message: \"Invalid customer type\"\n-Description: Type of customer", "key_type": "Non-unique", "display_name": "Customer Type", "data_type": "Enum", "data_type_full": "Enum", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"Individual\", \"Business\"", "default": "\"Individual\"", "validation": "List Check", "error_message": "Invalid customer type", "description": "Type of customer"}, "status": {"natural_language": "-Attribute name: status\n-Key: Non-unique\n-Display Name: Account Status\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Active\", \"Inactive\", \"Suspended\"\n-Default: \"Active\"\n-Validation: List Check\n-Error Message: \"Invalid status\"\n-Description: Current status of the customer account", "key_type": "Non-unique", "display_name": "Account Status", "data_type": "Enum", "data_type_full": "Enum", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"Active\", \"Inactive\", \"Suspended\"", "default": "\"Active\"", "validation": "List Check", "error_message": "Invalid status", "description": "Current status of the customer account"}}, "relationship_properties": {"rel_props_Customer_Loan": {"source_entity": "Customer", "target_entity": "Loan", "on_delete": "Restrict (Prevent deletion of customer with active loans)", "on_update": "Cascade (Update loan records when customer details change)", "foreign_key_type": "Non-Nullable"}}, "synthetic_data": [], "confidential_attributes": [], "internal_attributes": [], "public_attributes": [], "loading_strategies": {}, "archive_strategy": {}, "history_tracking": {}, "workflow": {}, "business_rule_placement": {}, "workflow_placement": {}, "entity_placement": {}, "display_name": "Customer", "type": "Core Entity", "description": "Represents a customer of the financial institution.", "enum_attributes": {"employmentStatus": {"name": "employmentStatus", "values": ["Employed", "Self-Employed", "Unemployed", "Retired"], "source_text": "employmentStatus (Employed, Self-Employed, Unemployed, Retired)"}, "customerType": {"name": "customerType", "values": ["Individual", "Business"], "source_text": "customerType (Individual, Business)"}, "status": {"name": "status", "values": ["Active", "Inactive", "Suspended"], "source_text": "status (Active, Inactive, Suspended)"}}, "enum_values_list": ["Employed", "Self-Employed", "Unemployed", "Retired", "Individual", "Business", "Active", "Inactive", "Suspended"], "entity_name": "Customer"}, "Collateral": {"entity_id": "E19", "name": "Collateral", "natural_language": "Collateral has collateralId^PK, customerId^FK, loanId^FK, collateralType (RealEstate, Vehicle, Investment, Cash, Other), description, value, appraisalDate, appraisalValue, lienStatus (FirstLien, SecondLien, NoLien), condition (Excellent, Good, Fair, Poor), assetAddress, assetCity, assetState, assetZipCode, insuranceProvider, insurancePolicyNumber, insuranceExpirationDate, documentationComplete (true, false).", "attributes": {"collateralId": {"name": "collateralId", "primary_key": true, "foreign_key": false, "calculated": false, "data_type": "Integer", "natural_language": "collateralId^PK", "validations": [{"constraint": "be unique", "id": "val_1"}], "default_value": "N/A"}, "customerId": {"name": "customerId", "primary_key": false, "foreign_key": true, "calculated": false, "data_type": "Integer", "natural_language": "customerId^FK", "validations": [{"constraint": "exist in Customer table", "id": "val_2"}, {"constraint": "match the Loan", "id": "val_6"}], "default_value": "N/A"}, "loanId": {"name": "loanId", "primary_key": false, "foreign_key": true, "calculated": false, "data_type": "Integer", "natural_language": "loanId^FK", "validations": [{"constraint": "exist in Loan table", "id": "val_3"}], "default_value": "N/A"}, "collateralType": {"name": "collateralType", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Enum", "natural_language": "collateralType (RealEstate, Vehicle, Investment, Cash, Other)", "enum_values": ["RealEstate", "Vehicle", "Investment", "Cash", "Other"], "default_value": "RealEstate"}, "description": {"name": "description", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "description", "default_value": "N/A"}, "value": {"name": "value", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Decimal", "natural_language": "value", "validations": [{"constraint": "be greater than 0", "id": "val_4"}], "default_value": "N/A"}, "appraisalDate": {"name": "appraisalDate", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Date", "natural_language": "appraisalDate", "default_value": "CURRENT_DATE"}, "appraisalValue": {"name": "appraisalValue", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Decimal", "natural_language": "appraisalValue", "validations": [{"constraint": "be greater than 0", "id": "val_5"}], "default_value": "N/A"}, "lienStatus": {"name": "lienStatus", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Enum", "natural_language": "lienStatus (FirstLien, SecondLien, NoLien)", "enum_values": ["FirstLien", "SecondLien", "NoLien"], "default_value": "FirstLien"}, "condition": {"name": "condition", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Enum", "natural_language": "condition (Excellent, Good, Fair, Poor)", "enum_values": ["Excellent", "Good", "Fair", "Poor"], "default_value": "Good"}, "assetAddress": {"name": "assetAddress", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "assetAddress", "default_value": "N/A"}, "assetCity": {"name": "assetCity", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "assetCity", "default_value": "N/A"}, "assetState": {"name": "assetState", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "assetState", "default_value": "N/A"}, "assetZipCode": {"name": "assetZipCode", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "assetZipCode", "default_value": "N/A"}, "insuranceProvider": {"name": "insuranceProvider", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "insuranceProvider", "default_value": "N/A"}, "insurancePolicyNumber": {"name": "insurancePolicyNumber", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "insurancePolicyNumber", "default_value": "N/A"}, "insuranceExpirationDate": {"name": "insuranceExpirationDate", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Date", "natural_language": "insuranceExpirationDate", "default_value": "N/A"}, "documentationComplete": {"name": "documentationComplete", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Boolean", "natural_language": "documentationComplete (true, false)", "enum_values": ["true", "false"], "default_value": "false"}}, "relationships": {"rel_1": {"entity": "Customer", "type": "many-to-one", "source_attribute": "customerId", "target_attribute": "customerId", "source_entity": "Collateral", "target_entity": "Customer", "cardinality": {"source": "n", "target": "1"}, "join_condition": "Collateral.customerId = Customer.customerId", "cascade_operations": {"delete": "restrict", "update": "cascade"}, "natural_language": "-Collateral has many-to-one relationship with Customer using Collateral.customerId to Customer.customerId^PK"}, "rel_2": {"entity": "Loan", "type": "many-to-one", "source_attribute": "loanId", "target_attribute": "loanId", "source_entity": "Collateral", "target_entity": "Loan", "cardinality": {"source": "n", "target": "1"}, "join_condition": "Collateral.loanId = Loan.loanId", "cascade_operations": {"delete": "restrict", "update": "cascade"}, "natural_language": "-Collateral has many-to-one relationship with Loan using Collateral.loanId to Loan.loanId^PK"}}, "business_rules": {}, "calculated_fields": {}, "validations": {"val_1": {"attribute": "collateralId", "constraint_text": "be unique", "entity": "Collateral", "validation_type": "unique", "parameters": {}, "executable_rule": "IS_UNIQUE(collateralId)", "natural_language": "-Collateral.collateralId must be unique"}, "val_2": {"attribute": "customerId", "constraint_text": "exist in Customer table", "entity": "Collateral", "validation_type": "foreign_key", "parameters": {"referenced_table": "Customer"}, "executable_rule": "FOREIGN_KEY_EXISTS(customerId, Customer)", "natural_language": "-Collateral.customerId must exist in Customer table"}, "val_3": {"attribute": "loanId", "constraint_text": "exist in Loan table", "entity": "Collateral", "validation_type": "foreign_key", "parameters": {"referenced_table": "Loan"}, "executable_rule": "FOREIGN_KEY_EXISTS(loanId, Loan)", "natural_language": "-Collateral.loanId must exist in Loan table"}, "val_4": {"attribute": "value", "constraint_text": "be greater than 0", "entity": "Collateral", "validation_type": "greater_than", "parameters": {"value": "0"}, "executable_rule": "value > 0", "natural_language": "-Collateral.value must be greater than 0"}, "val_5": {"attribute": "appraisalValue", "constraint_text": "be greater than 0", "entity": "Collateral", "validation_type": "greater_than", "parameters": {"value": "0"}, "executable_rule": "appraisalValue > 0", "natural_language": "-Collateral.appraisalValue must be greater than 0"}, "val_6": {"attribute": "customerId", "constraint_text": "match the Loan", "entity": "Collateral", "validation_type": "pattern", "parameters": {}, "executable_rule": "REGEX_MATCH(customerId, '.*')", "natural_language": "-Collateral.customerId must match the Loan.customerId of the associated loan"}}, "constraints": {}, "attribute_metadata": {"collateralId": {"natural_language": "-Attribute name: collateralId\n-Key: Primary\n-Display Name: Collateral ID\n-DataType: Integer\n-Required: Mandatory\n-Format: \"COL-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Auto-increment\n-Error Message: \"Invalid Collateral ID format\"\n-Description: Unique identifier for the collateral", "key_type": "Primary", "display_name": "Collateral ID", "data_type": "Integer", "data_type_full": "Integer", "type": "mandatory", "required": "Mandatory", "format": "\"COL-######\"", "values": "N/A", "default": "N/A", "validation": "Auto-increment", "error_message": "Invalid Collateral ID format", "description": "Unique identifier for the collateral"}, "customerId": {"natural_language": "-Attribute name: customerId\n-Key: Foreign\n-Display Name: Owner Customer ID\n-DataType: Integer\n-Required: Mandatory\n-Format: \"CUS-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Foreign Key Check\n-Error Message: \"Customer ID must reference an existing customer\"\n-Description: References the customer who owns the collateral", "key_type": "Foreign", "display_name": "Owner Customer ID", "data_type": "Integer", "data_type_full": "Integer", "type": "mandatory", "required": "Mandatory", "format": "\"CUS-######\"", "values": "N/A", "default": "N/A", "validation": "Foreign Key Check", "error_message": "Customer ID must reference an existing customer", "description": "References the customer who owns the collateral"}, "loanId": {"natural_language": "-Attribute name: loanId\n-Key: Foreign\n-Display Name: Associated Loan ID\n-DataType: Integer\n-Required: Optional\n-Format: \"LN-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Foreign Key Check\n-Error Message: \"Loan ID must reference an existing loan\"\n-Description: References the loan secured by this collateral", "key_type": "Foreign", "display_name": "Associated Loan ID", "data_type": "Integer", "data_type_full": "Integer", "type": "optional", "required": "Optional", "format": "\"LN-######\"", "values": "N/A", "default": "N/A", "validation": "Foreign Key Check", "error_message": "Loan ID must reference an existing loan", "description": "References the loan secured by this collateral"}, "collateralType": {"natural_language": "-Attribute name: collateralType\n-Key: Non-unique\n-Display Name: Collateral Type\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"RealEstate\", \"Vehicle\", \"Investment\", \"Cash\", \"Other\"\n-Default: \"RealEstate\"\n-Validation: List Check\n-Error Message: \"Invalid collateral type\"\n-Description: Type of asset used as collateral", "key_type": "Non-unique", "display_name": "Collateral Type", "data_type": "Enum", "data_type_full": "Enum", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"RealEstate\", \"Vehicle\", \"Investment\", \"Cash\", \"Other\"", "default": "\"RealEstate\"", "validation": "List Check", "error_message": "Invalid collateral type", "description": "Type of asset used as collateral"}, "description": {"natural_language": "-Attribute name: description\n-Key: Non-unique\n-Display Name: Asset Description\n-DataType: String\n-Required: Mandatory\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: \"Description is required\"\n-Description: Detailed description of the collateral", "key_type": "Non-unique", "display_name": "Asset Description", "data_type": "String", "data_type_full": "String", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "error_message": "Description is required", "description": "Detailed description of the collateral"}, "value": {"natural_language": "-Attribute name: value\n-Key: Non-unique\n-Display Name: Current Value\n-DataType: Decimal\n-Required: Mandatory\n-Format: \"$#,###.00\"\n-Values: 0.00-N\n-Default: N/A\n-Validation: Range Check\n-Error Message: \"Value must be greater than 0\"\n-Description: Current market value of the collateral", "key_type": "Non-unique", "display_name": "Current Value", "data_type": "Decimal", "data_type_full": "Decimal", "type": "mandatory", "required": "Mandatory", "format": "\"$#,###.00\"", "values": "0.00-N", "default": "N/A", "validation": "Range Check", "error_message": "Value must be greater than 0", "description": "Current market value of the collateral"}, "appraisalDate": {"natural_language": "-Attribute name: appraisalDate\n-Key: Non-unique\n-Display Name: Appraisal Date\n-DataType: Date\n-Required: Mandatory\n-Format: \"YYYY-MM-DD\"\n-Values: N/A\n-Default: \"CURRENT_DATE\"\n-Validation: Date Check\n-Error Message: \"Appraisal date is required\"\n-Description: Date when the collateral was appraised", "key_type": "Non-unique", "display_name": "Appraisal Date", "data_type": "Date", "data_type_full": "Date", "type": "mandatory", "required": "Mandatory", "format": "\"YYYY-MM-DD\"", "values": "N/A", "default": "\"CURRENT_DATE\"", "validation": "Date Check", "error_message": "Appraisal date is required", "description": "Date when the collateral was appraised"}, "appraisalValue": {"natural_language": "-Attribute name: appraisalValue\n-Key: Non-unique\n-Display Name: Appraisal Value\n-DataType: Decimal\n-Required: Mandatory\n-Format: \"$#,###.00\"\n-Values: 0.00-N\n-Default: N/A\n-Validation: Range Check\n-Error Message: \"Appraisal value must be greater than 0\"\n-Description: Value determined during appraisal", "key_type": "Non-unique", "display_name": "Appraisal Value", "data_type": "Decimal", "data_type_full": "Decimal", "type": "mandatory", "required": "Mandatory", "format": "\"$#,###.00\"", "values": "0.00-N", "default": "N/A", "validation": "Range Check", "error_message": "Appraisal value must be greater than 0", "description": "Value determined during appraisal"}, "lienStatus": {"natural_language": "-Attribute name: lienStatus\n-Key: Non-unique\n-Display Name: Lien Status\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"FirstLien\", \"SecondLien\", \"NoLien\"\n-Default: \"FirstLien\"\n-Validation: List Check\n-Error Message: \"Invalid lien status\"\n-Description: Status of liens against this collateral", "key_type": "Non-unique", "display_name": "Lien Status", "data_type": "Enum", "data_type_full": "Enum", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"FirstLien\", \"SecondLien\", \"NoLien\"", "default": "\"FirstLien\"", "validation": "List Check", "error_message": "Invalid lien status", "description": "Status of liens against this collateral"}, "condition": {"natural_language": "-Attribute name: condition\n-Key: Non-unique\n-Display Name: Asset Condition\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Excellent\", \"Good\", \"Fair\", \"Poor\"\n-Default: \"Good\"\n-Validation: List Check\n-Error Message: \"Invalid condition\"\n-Description: Physical condition of the collateral", "key_type": "Non-unique", "display_name": "Asset Condition", "data_type": "Enum", "data_type_full": "Enum", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"Excellent\", \"Good\", \"Fair\", \"Poor\"", "default": "\"Good\"", "validation": "List Check", "error_message": "Invalid condition", "description": "Physical condition of the collateral"}, "assetAddress": {"natural_language": "-Attribute name: assetAddress\n-Key: Non-unique\n-Display Name: Asset Street Address\n-DataType: String\n-Required: Optional\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: N/A\n-Description: Address of the physical asset", "key_type": "Non-unique", "display_name": "Asset Street Address", "data_type": "String", "data_type_full": "String", "type": "optional", "required": "Optional", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "description": "Address of the physical asset"}, "assetCity": {"natural_language": "-Attribute name: assetCity\n-Key: Non-unique\n-Display Name: Asset City\n-DataType: String\n-Required: Optional\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: N/A\n-Description: City where the physical asset is located", "key_type": "Non-unique", "display_name": "Asset City", "data_type": "String", "data_type_full": "String", "type": "optional", "required": "Optional", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "description": "City where the physical asset is located"}, "assetState": {"natural_language": "-Attribute name: assetState\n-Key: Non-unique\n-Display Name: Asset State/Province\n-DataType: String\n-Required: Optional\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: N/A\n-Description: State where the physical asset is located", "key_type": "Non-unique", "display_name": "Asset State/Province", "data_type": "String", "data_type_full": "String", "type": "optional", "required": "Optional", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "description": "State where the physical asset is located"}, "assetZipCode": {"natural_language": "-Attribute name: assetZipCode\n-Key: Non-unique\n-Display Name: Asset ZIP/Postal Code\n-DataType: String\n-Required: Optional\n-Format: \"#####\" or \"#####-####\"\n-Values: N/A\n-Default: N/A\n-Validation: Format Check\n-Error Message: \"Invalid zip code format\"\n-Description: Zip code where the physical asset is located", "key_type": "Non-unique", "display_name": "Asset ZIP/Postal Code", "data_type": "String", "data_type_full": "String", "type": "optional", "required": "Optional", "format": "\"#####\" or \"#####-####\"", "values": "N/A", "default": "N/A", "validation": "Format Check", "error_message": "Invalid zip code format", "description": "Zip code where the physical asset is located"}, "insuranceProvider": {"natural_language": "-Attribute name: insuranceProvider\n-Key: Non-unique\n-Display Name: Insurance Provider\n-DataType: String\n-Required: Optional\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: N/A\n-Description: Insurance company covering the collateral", "key_type": "Non-unique", "display_name": "Insurance Provider", "data_type": "String", "data_type_full": "String", "type": "optional", "required": "Optional", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "description": "Insurance company covering the collateral"}, "insurancePolicyNumber": {"natural_language": "-Attribute name: insurancePolicyNumber\n-Key: Non-unique\n-Display Name: Insurance Policy Number\n-DataType: String\n-Required: Optional\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: N/A\n-Description: Insurance policy number for the collateral", "key_type": "Non-unique", "display_name": "Insurance Policy Number", "data_type": "String", "data_type_full": "String", "type": "optional", "required": "Optional", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "description": "Insurance policy number for the collateral"}, "insuranceExpirationDate": {"natural_language": "-Attribute name: insuranceExpirationDate\n-Key: Non-unique\n-Display Name: Insurance Expiration Date\n-DataType: Date\n-Required: Optional\n-Format: \"YYYY-MM-DD\"\n-Values: N/A\n-Default: N/A\n-Validation: Date Check\n-Error Message: \"Expiration date must be in the future\"\n-Description: Date when the insurance policy expires", "key_type": "Non-unique", "display_name": "Insurance Expiration Date", "data_type": "Date", "data_type_full": "Date", "type": "optional", "required": "Optional", "format": "\"YYYY-MM-DD\"", "values": "N/A", "default": "N/A", "validation": "Date Check", "error_message": "Expiration date must be in the future", "description": "Date when the insurance policy expires"}, "documentationComplete": {"natural_language": "-Attribute name: documentationComplete\n-Key: Non-unique\n-Display Name: Documentation Complete\n-DataType: Boolean\n-Required: Mandatory\n-Format: N/A\n-Values: \"true\", \"false\"\n-Default: \"false\"\n-Validation: Boolean Check\n-Error Message: \"Invalid documentation status\"\n-Description: Indicates if all required documentation is complete", "key_type": "Non-unique", "display_name": "Documentation Complete", "data_type": "Boolean", "data_type_full": "Boolean", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"true\", \"false\"", "default": "\"false\"", "validation": "Boolean Check", "error_message": "Invalid documentation status", "description": "Indicates if all required documentation is complete"}}, "relationship_properties": {"rel_props_Collateral_Customer": {"source_entity": "Collateral", "target_entity": "Customer", "on_delete": "Restrict (Prevent deletion of customer with active collateral)", "on_update": "Cascade (Update collateral records when customer details change)", "foreign_key_type": "Non-Nullable"}}, "synthetic_data": [], "confidential_attributes": [], "internal_attributes": [], "public_attributes": [], "loading_strategies": {}, "archive_strategy": {}, "history_tracking": {}, "workflow": {}, "business_rule_placement": {}, "workflow_placement": {}, "entity_placement": {}, "display_name": "Collateral Asset", "type": "Core Entity", "description": "Represents an asset used to secure a loan.", "enum_attributes": {"collateralType": {"name": "collateralType", "values": ["RealEstate", "Vehicle", "Investment", "Cash", "Other"], "source_text": "collateralType (RealEstate, Vehicle, Investment, Cash, Other)"}, "lienStatus": {"name": "lienStatus", "values": ["FirstLien", "SecondLien", "NoLien"], "source_text": "lienStatus (FirstLien, SecondLien, NoLien)"}, "condition": {"name": "condition", "values": ["Excellent", "Good", "Fair", "Poor"], "source_text": "condition (Excellent, Good, Fair, Poor)"}, "documentationComplete": {"name": "documentationComplete", "values": ["true", "false"], "source_text": "documentationComplete (true, false)"}}, "enum_values_list": ["RealEstate", "Vehicle", "Investment", "Cash", "Other", "FirstLien", "SecondLien", "NoLien", "Excellent", "Good", "Fair", "Poor", "true", "false"], "entity_name": "Collateral"}, "Payment": {"entity_id": "E20", "name": "Payment", "natural_language": "Payment has paymentId^PK, loanId^FK, amount, paymentDate, dueDate, status (Pending, Completed, Failed, Cancelled), paymentMethod (CreditCard, DebitCard, BankTransfer, Cash, Check, AutoDebit), referenceNumber, lateFee, paymentType (Regular, ExtraPayment, LatePayment, InterestOnly, PrincipalOnly), notes.", "attributes": {"paymentId": {"name": "paymentId", "primary_key": true, "foreign_key": false, "calculated": false, "data_type": "Integer", "natural_language": "paymentId^PK", "validations": [{"constraint": "be unique", "id": "val_1"}], "default_value": "N/A"}, "loanId": {"name": "loanId", "primary_key": false, "foreign_key": true, "calculated": false, "data_type": "Integer", "natural_language": "loanId^FK", "validations": [{"constraint": "exist in Loan table", "id": "val_2"}], "default_value": "N/A"}, "amount": {"name": "amount", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Decimal", "natural_language": "amount", "validations": [{"constraint": "be greater than 0", "id": "val_3"}], "default_value": "N/A"}, "paymentDate": {"name": "paymentDate", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Date", "natural_language": "paymentDate", "validations": [{"constraint": "be a valid date", "id": "val_5"}], "default_value": "CURRENT_DATE"}, "dueDate": {"name": "dueDate", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Date", "natural_language": "dueDate", "validations": [{"constraint": "be a valid date", "id": "val_4"}], "default_value": "N/A"}, "status": {"name": "status", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Enum", "natural_language": "status (Pending, Completed, Failed, Cancelled)", "enum_values": ["Pending", "Completed", "Failed", "Cancelled"], "validations": [{"constraint": "be one of \"Pending\", \"Completed\", \"Failed\", \"Cancelled\"", "id": "val_6"}], "default_value": "Pending"}, "paymentMethod": {"name": "paymentMethod", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Enum", "natural_language": "paymentMethod (CreditCard, DebitCard, BankTransfer, Cash, Check, AutoDebit)", "enum_values": ["CreditCard", "DebitCard", "BankTransfer", "Cash", "Check", "AutoDebit"], "default_value": "AutoDebit"}, "referenceNumber": {"name": "referenceNumber", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "referenceNumber", "default_value": "N/A"}, "lateFee": {"name": "lateFee", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Decimal", "natural_language": "lateFee", "default_value": "0.00"}, "paymentType": {"name": "paymentType", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Enum", "natural_language": "paymentType (Regular, ExtraPayment, LatePayment, InterestOnly, PrincipalOnly)", "enum_values": ["Regular", "ExtraPayment", "LatePayment", "InterestOnly", "Principal<PERSON><PERSON><PERSON>"], "default_value": "Regular"}, "notes": {"name": "notes", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "notes", "default_value": "N/A"}}, "relationships": {"rel_1": {"entity": "Loan", "type": "many-to-one", "source_attribute": "loanId", "target_attribute": "loanId", "source_entity": "Payment", "target_entity": "Loan", "cardinality": {"source": "n", "target": "1"}, "join_condition": "Payment.loanId = Loan.loanId", "cascade_operations": {"delete": "restrict", "update": "cascade"}, "natural_language": "-Payment has many-to-one relationship with Loan using Payment.loanId to Loan.loanId^PK"}}, "business_rules": {}, "calculated_fields": {}, "validations": {"val_1": {"attribute": "paymentId", "constraint_text": "be unique", "entity": "Payment", "validation_type": "unique", "parameters": {}, "executable_rule": "IS_UNIQUE(paymentId)", "natural_language": "-Payment.paymentId must be unique"}, "val_2": {"attribute": "loanId", "constraint_text": "exist in Loan table", "entity": "Payment", "validation_type": "foreign_key", "parameters": {"referenced_table": "Loan"}, "executable_rule": "FOREIGN_KEY_EXISTS(loanId, Loan)", "natural_language": "-Payment.loanId must exist in Loan table"}, "val_3": {"attribute": "amount", "constraint_text": "be greater than 0", "entity": "Payment", "validation_type": "greater_than", "parameters": {"value": "0"}, "executable_rule": "amount > 0", "natural_language": "-Payment.amount must be greater than 0"}, "val_4": {"attribute": "dueDate", "constraint_text": "be a valid date", "entity": "Payment", "validation_type": "custom", "parameters": {}, "executable_rule": "CUSTOM_VALIDATION(dueDate)", "natural_language": "-Payment.dueDate must be a valid date"}, "val_5": {"attribute": "paymentDate", "constraint_text": "be a valid date", "entity": "Payment", "validation_type": "custom", "parameters": {}, "executable_rule": "CUSTOM_VALIDATION(paymentDate)", "natural_language": "-Payment.paymentDate must be a valid date"}, "val_6": {"attribute": "status", "constraint_text": "be one of \"Pending\", \"Completed\", \"Failed\", \"Cancelled\"", "entity": "Payment", "validation_type": "enum", "parameters": {}, "executable_rule": "status IS NOT NULL", "natural_language": "-Payment.status must be one of \"Pending\", \"Completed\", \"Failed\", \"Cancelled\""}}, "constraints": {}, "attribute_metadata": {"paymentId": {"natural_language": "-Attribute name: paymentId\n-Key: Primary\n-Display Name: Payment ID\n-DataType: Integer\n-Required: Mandatory\n-Format: \"PMT-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Auto-increment\n-Error Message: \"Invalid Payment ID format\"\n-Description: Unique identifier for the payment", "key_type": "Primary", "display_name": "Payment ID", "data_type": "Integer", "data_type_full": "Integer", "type": "mandatory", "required": "Mandatory", "format": "\"PMT-######\"", "values": "N/A", "default": "N/A", "validation": "Auto-increment", "error_message": "Invalid Payment ID format", "description": "Unique identifier for the payment"}, "loanId": {"natural_language": "-Attribute name: loanId\n-Key: Foreign\n-Display Name: Loan ID\n-DataType: Integer\n-Required: Mandatory\n-Format: \"LN-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Foreign Key Check\n-Error Message: \"Loan ID must reference an existing loan\"\n-Description: References the loan this payment applies to", "key_type": "Foreign", "display_name": "Loan ID", "data_type": "Integer", "data_type_full": "Integer", "type": "mandatory", "required": "Mandatory", "format": "\"LN-######\"", "values": "N/A", "default": "N/A", "validation": "Foreign Key Check", "error_message": "Loan ID must reference an existing loan", "description": "References the loan this payment applies to"}, "amount": {"natural_language": "-Attribute name: amount\n-Key: Non-unique\n-Display Name: Payment Amount\n-DataType: Decimal\n-Required: Mandatory\n-Format: \"$#,###.00\"\n-Values: 0.01-N\n-Default: N/A\n-Validation: Range Check\n-Error Message: \"Payment amount must be greater than 0\"\n-Description: Amount of the payment", "key_type": "Non-unique", "display_name": "Payment Amount", "data_type": "Decimal", "data_type_full": "Decimal", "type": "mandatory", "required": "Mandatory", "format": "\"$#,###.00\"", "values": "0.01-N", "default": "N/A", "validation": "Range Check", "error_message": "Payment amount must be greater than 0", "description": "Amount of the payment"}, "paymentDate": {"natural_language": "-Attribute name: paymentDate\n-Key: Non-unique\n-Display Name: Payment Date\n-DataType: Date\n-Required: Mandatory\n-Format: \"YYYY-MM-DD\"\n-Values: N/A\n-Default: \"CURRENT_DATE\"\n-Validation: Date Check\n-Error Message: \"Payment date is required\"\n-Description: Date when the payment was made", "key_type": "Non-unique", "display_name": "Payment Date", "data_type": "Date", "data_type_full": "Date", "type": "mandatory", "required": "Mandatory", "format": "\"YYYY-MM-DD\"", "values": "N/A", "default": "\"CURRENT_DATE\"", "validation": "Date Check", "error_message": "Payment date is required", "description": "Date when the payment was made"}, "dueDate": {"natural_language": "-Attribute name: dueDate\n-Key: Non-unique\n-Display Name: Due Date\n-DataType: Date\n-Required: Mandatory\n-Format: \"YYYY-MM-DD\"\n-Values: N/A\n-Default: N/A\n-Validation: Date Check\n-Error Message: \"Due date is required\"\n-Description: Date when the payment is due", "key_type": "Non-unique", "display_name": "Due Date", "data_type": "Date", "data_type_full": "Date", "type": "mandatory", "required": "Mandatory", "format": "\"YYYY-MM-DD\"", "values": "N/A", "default": "N/A", "validation": "Date Check", "error_message": "Due date is required", "description": "Date when the payment is due"}, "status": {"natural_language": "-Attribute name: status\n-Key: Non-unique\n-Display Name: Payment Status\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Pending\", \"Completed\", \"Failed\", \"Cancelled\"\n-Default: \"Pending\"\n-Validation: List Check\n-Error Message: \"Invalid payment status\"\n-Description: Current status of the payment", "key_type": "Non-unique", "display_name": "Payment Status", "data_type": "Enum", "data_type_full": "Enum", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"Pending\", \"Completed\", \"Failed\", \"Cancelled\"", "default": "\"Pending\"", "validation": "List Check", "error_message": "Invalid payment status", "description": "Current status of the payment"}, "paymentMethod": {"natural_language": "-Attribute name: paymentMethod\n-Key: Non-unique\n-Display Name: Payment Method\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"CreditCard\", \"DebitCard\", \"BankTransfer\", \"Cash\", \"Check\", \"AutoDebit\"\n-Default: \"AutoDebit\"\n-Validation: List Check\n-Error Message: \"Invalid payment method\"\n-Description: Method used for payment", "key_type": "Non-unique", "display_name": "Payment Method", "data_type": "Enum", "data_type_full": "Enum", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"CreditCard\", \"DebitCard\", \"BankTransfer\", \"Cash\", \"Check\", \"AutoDebit\"", "default": "\"AutoDebit\"", "validation": "List Check", "error_message": "Invalid payment method", "description": "Method used for payment"}, "referenceNumber": {"natural_language": "-Attribute name: referenceNumber\n-Key: Non-unique\n-Display Name: Reference Number\n-DataType: String\n-Required: Optional\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: N/A\n-Description: External reference number for the payment", "key_type": "Non-unique", "display_name": "Reference Number", "data_type": "String", "data_type_full": "String", "type": "optional", "required": "Optional", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "description": "External reference number for the payment"}, "lateFee": {"natural_language": "-Attribute name: lateFee\n-Key: Non-unique\n-Display Name: Late Fee\n-DataType: Decimal\n-Required: Optional\n-Format: \"$#,###.00\"\n-Values: 0.00-N\n-Default: \"0.00\"\n-Validation: Range Check\n-Error Message: \"Late fee cannot be negative\"\n-Description: Additional fee applied for late payments", "key_type": "Non-unique", "display_name": "Late Fee", "data_type": "Decimal", "data_type_full": "Decimal", "type": "optional", "required": "Optional", "format": "\"$#,###.00\"", "values": "0.00-N", "default": "\"0.00\"", "validation": "Range Check", "error_message": "Late fee cannot be negative", "description": "Additional fee applied for late payments"}, "paymentType": {"natural_language": "-Attribute name: paymentType\n-Key: Non-unique\n-Display Name: Payment Type\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Regular\", \"ExtraPayment\", \"LatePayment\", \"InterestOnly\", \"PrincipalOnly\"\n-Default: \"Regular\"\n-Validation: List Check\n-Error Message: \"Invalid payment type\"\n-Description: Type of payment being made", "key_type": "Non-unique", "display_name": "Payment Type", "data_type": "Enum", "data_type_full": "Enum", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"Regular\", \"ExtraPayment\", \"LatePayment\", \"InterestOnly\", \"PrincipalOnly\"", "default": "\"Regular\"", "validation": "List Check", "error_message": "Invalid payment type", "description": "Type of payment being made"}, "notes": {"natural_language": "-Attribute name: notes\n-Key: Non-unique\n-Display Name: Payment Notes\n-DataType: String\n-Required: Optional\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: N/A\n-Description: Additional notes about the payment", "key_type": "Non-unique", "display_name": "Payment Notes", "data_type": "String", "data_type_full": "String", "type": "optional", "required": "Optional", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "description": "Additional notes about the payment"}}, "relationship_properties": {"rel_props_Payment_Loan": {"source_entity": "Payment", "target_entity": "Loan", "on_delete": "Cascade (Remove payments when loan is deleted)", "on_update": "Cascade (Update payment records when loan details change)", "foreign_key_type": "Non-Nullable"}}, "synthetic_data": [], "confidential_attributes": [], "internal_attributes": [], "public_attributes": [], "loading_strategies": {}, "archive_strategy": {}, "history_tracking": {}, "workflow": {}, "business_rule_placement": {}, "workflow_placement": {}, "entity_placement": {}, "display_name": "Loan Payment", "type": "Core Entity", "description": "Represents a payment made toward a loan.", "enum_attributes": {"status": {"name": "status", "values": ["Pending", "Completed", "Failed", "Cancelled"], "source_text": "status (Pending, Completed, Failed, Cancelled)"}, "paymentMethod": {"name": "paymentMethod", "values": ["CreditCard", "DebitCard", "BankTransfer", "Cash", "Check", "AutoDebit"], "source_text": "paymentMethod (CreditCard, DebitCard, BankTransfer, Cash, Check, AutoDebit)"}, "paymentType": {"name": "paymentType", "values": ["Regular", "ExtraPayment", "LatePayment", "InterestOnly", "Principal<PERSON><PERSON><PERSON>"], "source_text": "paymentType (Regular, ExtraPayment, LatePayment, InterestOnly, PrincipalOnly)"}}, "enum_values_list": ["Pending", "Completed", "Failed", "Cancelled", "CreditCard", "DebitCard", "BankTransfer", "Cash", "Check", "AutoDebit", "Regular", "ExtraPayment", "LatePayment", "InterestOnly", "Principal<PERSON><PERSON><PERSON>"], "entity_name": "Payment"}}}, "validation_errors": null, "parsed_entities": {"Loan": {"entity_id": "E17", "name": "Loan", "natural_language": "Loan has loanId^PK, customerId^FK, loanAmount, interestRate, term, startDate, endDate, status (Active, Closed, Default, Restructured), paymentFrequency (Monthly, Biweekly, Weekly, Quarterly, Annually), totalPaymentsMade, remainingBalance[derived], loanType (Personal, Mortgage, Auto, Education, Business), collateralId^FK, originationFee, lateFeePercentage, earlyPaymentPenalty.", "attributes": {"loanId": {"name": "loanId", "primary_key": true, "foreign_key": false, "calculated": false, "data_type": "Integer", "natural_language": "loanId^PK", "validations": [{"constraint": "be unique", "id": "val_1"}], "default_value": "N/A"}, "customerId": {"name": "customerId", "primary_key": false, "foreign_key": true, "calculated": false, "data_type": "Integer", "natural_language": "customerId^FK", "default_value": "N/A"}, "loanAmount": {"name": "loanAmount", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Decimal", "natural_language": "loanAmount", "validations": [{"constraint": "be greater than 0", "id": "val_2"}], "default_value": "N/A"}, "interestRate": {"name": "interestRate", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Decimal", "natural_language": "interestRate", "validations": [{"constraint": "be greater than or equal to 1", "id": "val_3"}], "default_value": "5.99"}, "term": {"name": "term", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Integer", "natural_language": "term", "validations": [{"constraint": "be greater than or equal to 3", "id": "val_4"}], "default_value": "60"}, "startDate": {"name": "startDate", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Date", "natural_language": "startDate", "validations": [{"constraint": "not be in the future", "id": "val_5"}], "default_value": "CURRENT_DATE"}, "endDate": {"name": "endDate", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Date", "natural_language": "endDate", "validations": [{"constraint": "be after startDate", "id": "val_6"}], "default_value": "N/A"}, "status": {"name": "status", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Enum", "natural_language": "status (Active, Closed, Default, Restructured)", "enum_values": ["Active", "Closed", "<PERSON><PERSON><PERSON>", "Restructured"], "default_value": "Active"}, "paymentFrequency": {"name": "paymentFrequency", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Enum", "natural_language": "paymentFrequency (Monthly, Biweekly, Weekly, Quarterly, Annually)", "enum_values": ["Monthly", "Biweekly", "Weekly", "Quarterly", "Annually"], "default_value": "Monthly"}, "totalPaymentsMade": {"name": "totalPaymentsMade", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Integer", "natural_language": "totalPaymentsMade", "default_value": "0"}, "remainingBalance": {"name": "remainingBalance", "primary_key": false, "foreign_key": false, "calculated": true, "data_type": "Decimal", "natural_language": "remainingBalance[derived]", "default_value": "N/A"}, "loanType": {"name": "loanType", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Enum", "natural_language": "loanType (Personal, Mortgage, Auto, Education, Business)", "enum_values": ["Personal", "Mortgage", "Auto", "Education", "Business"], "default_value": "Personal"}, "collateralId": {"name": "collateralId", "primary_key": false, "foreign_key": true, "calculated": false, "data_type": "Integer", "natural_language": "collateralId^FK", "default_value": "N/A"}, "originationFee": {"name": "originationFee", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Decimal", "natural_language": "originationFee", "default_value": "0.00"}, "lateFeePercentage": {"name": "lateFeePercentage", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Decimal", "natural_language": "lateFeePercentage", "default_value": "5.00"}, "earlyPaymentPenalty": {"name": "earlyPaymentPenalty", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Decimal", "natural_language": "earlyPaymentPenalty", "default_value": "1.00"}}, "relationships": {"rel_1": {"entity": "Customer", "type": "many-to-one", "source_attribute": "customerId", "target_attribute": "customerId", "source_entity": "Loan", "target_entity": "Customer", "cardinality": {"source": "n", "target": "1"}, "join_condition": "Loan.customerId = Customer.customerId", "cascade_operations": {"delete": "restrict", "update": "cascade"}, "natural_language": "-Loan has many-to-one relationship with Customer using Loan.customerId to Customer.customerId^PK"}, "rel_2": {"entity": "Collateral", "type": "many-to-one", "source_attribute": "collateralId", "target_attribute": "collateralId", "source_entity": "Loan", "target_entity": "Collateral", "cardinality": {"source": "n", "target": "1"}, "join_condition": "Loan.collateralId = Collateral.collateralId", "cascade_operations": {"delete": "restrict", "update": "cascade"}, "natural_language": "-Loan has many-to-one relationship with Collateral using Loan.collateralId to Collateral.collateralId^PK"}}, "business_rules": {}, "calculated_fields": {}, "validations": {"val_1": {"attribute": "loanId", "constraint_text": "be unique", "entity": "Loan", "validation_type": "unique", "parameters": {}, "executable_rule": "IS_UNIQUE(loanId)", "natural_language": "-Loan.loanId must be unique"}, "val_2": {"attribute": "loanAmount", "constraint_text": "be greater than 0", "entity": "Loan", "validation_type": "greater_than", "parameters": {"value": "0"}, "executable_rule": "loanAmount > 0", "natural_language": "-Loan.loanAmount must be greater than 0"}, "val_3": {"attribute": "interestRate", "constraint_text": "be greater than or equal to 1", "entity": "Loan", "validation_type": "min_value", "parameters": {"min_value": "1"}, "executable_rule": "interestRate >= 1", "natural_language": "-Loan.interestRate must be greater than or equal to 1.0"}, "val_4": {"attribute": "term", "constraint_text": "be greater than or equal to 3", "entity": "Loan", "validation_type": "min_value", "parameters": {"min_value": "3"}, "executable_rule": "term >= 3", "natural_language": "-Loan.term must be greater than or equal to 3"}, "val_5": {"attribute": "startDate", "constraint_text": "not be in the future", "entity": "Loan", "validation_type": "not_future", "parameters": {}, "executable_rule": "startDate <= CURRENT_DATE", "natural_language": "-Loan.startDate must not be in the future"}, "val_6": {"attribute": "endDate", "constraint_text": "be after startDate", "entity": "Loan", "validation_type": "after_date", "parameters": {"reference_attribute": "startDate"}, "executable_rule": "endDate > startDate", "natural_language": "-Loan.endDate must be after startDate"}}, "constraints": {}, "attribute_metadata": {"loanId": {"natural_language": "-Attribute name: loanId\n-Key: Primary\n-Display Name: Loan ID\n-DataType: Integer\n-Required: Mandatory\n-Format: \"LN-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Auto-increment\n-Error Message: \"Invalid Loan ID format\"\n-Description: Unique identifier for the loan", "key_type": "Primary", "display_name": "Loan ID", "data_type": "Integer", "data_type_full": "Integer", "type": "mandatory", "required": "Mandatory", "format": "\"LN-######\"", "values": "N/A", "default": "N/A", "validation": "Auto-increment", "error_message": "Invalid Loan ID format", "description": "Unique identifier for the loan"}, "customerId": {"natural_language": "-Attribute name: customerId\n-Key: Foreign\n-Display Name: Customer ID\n-DataType: Integer\n-Required: Mandatory\n-Format: \"CUS-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Foreign Key Check\n-Error Message: \"Customer ID must reference an existing customer\"\n-Description: References the customer who took the loan", "key_type": "Foreign", "display_name": "Customer ID", "data_type": "Integer", "data_type_full": "Integer", "type": "mandatory", "required": "Mandatory", "format": "\"CUS-######\"", "values": "N/A", "default": "N/A", "validation": "Foreign Key Check", "error_message": "Customer ID must reference an existing customer", "description": "References the customer who took the loan"}, "loanAmount": {"natural_language": "-Attribute name: loanAmount\n-Key: Non-unique\n-Display Name: Principal <PERSON><PERSON> Amount\n-DataType: Decimal\n-Required: Mandatory\n-Format: \"$#,###.00\"\n-Values: 1000.00-10000000.00\n-Default: N/A\n-Validation: Range Check\n-Error Message: \"Loan amount must be between $1,000 and $10,000,000\"\n-Description: The principal amount borrowed by the customer", "key_type": "Non-unique", "display_name": "Principal <PERSON><PERSON>", "data_type": "Decimal", "data_type_full": "Decimal", "type": "mandatory", "required": "Mandatory", "format": "\"$#,###.00\"", "values": "1000.00-10000000.00", "default": "N/A", "validation": "Range Check", "error_message": "Loan amount must be between $1,000 and $10,000,000", "description": "The principal amount borrowed by the customer"}, "interestRate": {"natural_language": "-Attribute name: interestRate\n-Key: Non-unique\n-Display Name: Annual Interest Rate (%)\n-DataType: Decimal\n-Required: Mandatory\n-Format: \"0.00%\"\n-Values: 1.00-30.00\n-Default: \"5.99\"\n-Validation: Range Check\n-Error Message: \"Interest rate must be between 1.00% and 30.00%\"\n-Description: Annual interest rate applied to the loan", "key_type": "Non-unique", "display_name": "Annual Interest Rate (%)", "data_type": "Decimal", "data_type_full": "Decimal", "type": "mandatory", "required": "Mandatory", "format": "\"0.00%\"", "values": "1.00-30.00", "default": "\"5.99\"", "validation": "Range Check", "error_message": "Interest rate must be between 1.00% and 30.00%", "description": "Annual interest rate applied to the loan"}, "term": {"natural_language": "-Attribute name: term\n-Key: Non-unique\n-Display Name: <PERSON><PERSON> (Months)\n-DataType: Integer\n-Required: Mandatory\n-Format: \"### months\"\n-Values: 3-480\n-Default: \"60\"\n-Validation: Range Check\n-Error Message: \"Term must be between 3 and 480 months\"\n-Description: Duration of the loan in months", "key_type": "Non-unique", "display_name": "<PERSON><PERSON> (Months)", "data_type": "Integer", "data_type_full": "Integer", "type": "mandatory", "required": "Mandatory", "format": "\"### months\"", "values": "3-480", "default": "\"60\"", "validation": "Range Check", "error_message": "Term must be between 3 and 480 months", "description": "Duration of the loan in months"}, "startDate": {"natural_language": "-Attribute name: startDate\n-Key: Non-unique\n-Display Name: Loan Start Date\n-DataType: Date\n-Required: Mandatory\n-Format: \"YYYY-MM-DD\"\n-Values: N/A\n-Default: \"CURRENT_DATE\"\n-Validation: Date Check\n-Error Message: \"Start date cannot be in the future\"\n-Description: Date when the loan becomes active", "key_type": "Non-unique", "display_name": "Loan Start Date", "data_type": "Date", "data_type_full": "Date", "type": "mandatory", "required": "Mandatory", "format": "\"YYYY-MM-DD\"", "values": "N/A", "default": "\"CURRENT_DATE\"", "validation": "Date Check", "error_message": "Start date cannot be in the future", "description": "Date when the loan becomes active"}, "endDate": {"natural_language": "-Attribute name: endDate\n-Key: Non-unique\n-Display Name: Loan End Date\n-DataType: Date\n-Required: Mandatory\n-Format: \"YYYY-MM-DD\"\n-Values: N/A\n-Default: N/A\n-Validation: Date Check\n-Error Message: \"End date must be after start date\"\n-Description: Scheduled date for loan completion", "key_type": "Non-unique", "display_name": "Loan End Date", "data_type": "Date", "data_type_full": "Date", "type": "mandatory", "required": "Mandatory", "format": "\"YYYY-MM-DD\"", "values": "N/A", "default": "N/A", "validation": "Date Check", "error_message": "End date must be after start date", "description": "Scheduled date for loan completion"}, "status": {"natural_language": "-Attribute name: status\n-Key: Non-unique\n-Display Name: Loan Status\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Active\", \"Closed\", \"Default\", \"Restructured\"\n-Default: \"Active\"\n-Validation: List Check\n-Error Message: \"Invalid loan status\"\n-Description: Current status of the loan", "key_type": "Non-unique", "display_name": "Loan Status", "data_type": "Enum", "data_type_full": "Enum", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"Active\", \"Closed\", \"Default\", \"Restructured\"", "default": "\"Active\"", "validation": "List Check", "error_message": "Invalid loan status", "description": "Current status of the loan"}, "paymentFrequency": {"natural_language": "-Attribute name: paymentFrequency\n-Key: Non-unique\n-Display Name: Payment Frequency\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Monthly\", \"Biweekly\", \"Weekly\", \"Quarterly\", \"Annually\"\n-Default: \"Monthly\"\n-Validation: List Check\n-Error Message: \"Invalid payment frequency\"\n-Description: How often payments are made on the loan", "key_type": "Non-unique", "display_name": "Payment Frequency", "data_type": "Enum", "data_type_full": "Enum", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"Monthly\", \"Biweekly\", \"Weekly\", \"Quarterly\", \"Annually\"", "default": "\"Monthly\"", "validation": "List Check", "error_message": "Invalid payment frequency", "description": "How often payments are made on the loan"}, "totalPaymentsMade": {"natural_language": "-Attribute name: totalPaymentsMade\n-Key: Non-unique\n-Display Name: Total Payments Made\n-DataType: Integer\n-Required: Mandatory\n-Format: \"###\"\n-Values: 0-N\n-Default: \"0\"\n-Validation: Range Check\n-Error Message: \"Total payments cannot be negative\"\n-Description: Number of payments completed for this loan", "key_type": "Non-unique", "display_name": "Total Payments Made", "data_type": "Integer", "data_type_full": "Integer", "type": "mandatory", "required": "Mandatory", "format": "\"###\"", "values": "0-N", "default": "\"0\"", "validation": "Range Check", "error_message": "Total payments cannot be negative", "description": "Number of payments completed for this loan"}, "remainingBalance": {"natural_language": "-Attribute name: remainingBalance\n-Key: Non-unique\n-Display Name: Remaining Balance\n-DataType: Decimal\n-Required: Calculated\n-Format: \"$#,###.00\"\n-Values: 0.00-N\n-Default: N/A\n-Validation: Calculation\n-Error Message: N/A\n-Description: Current outstanding balance on the loan", "key_type": "Non-unique", "display_name": "Remaining Balance", "data_type": "Decimal", "data_type_full": "Decimal", "type": "calculated", "required": "Calculated", "format": "\"$#,###.00\"", "values": "0.00-N", "default": "N/A", "validation": "Calculation", "description": "Current outstanding balance on the loan"}, "loanType": {"natural_language": "-Attribute name: loanType\n-Key: Non-unique\n-Display Name: Loan Type\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Personal\", \"Mortgage\", \"Auto\", \"Education\", \"Business\"\n-Default: \"Personal\"\n-Validation: List Check\n-Error Message: \"Invalid loan type\"\n-Description: Category of loan based on purpose", "key_type": "Non-unique", "display_name": "Loan Type", "data_type": "Enum", "data_type_full": "Enum", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"Personal\", \"Mortgage\", \"Auto\", \"Education\", \"Business\"", "default": "\"Personal\"", "validation": "List Check", "error_message": "Invalid loan type", "description": "Category of loan based on purpose"}, "collateralId": {"natural_language": "-Attribute name: collateralId\n-Key: Foreign\n-Display Name: Collateral ID\n-DataType: Integer\n-Required: Optional\n-Format: \"COL-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Foreign Key Check\n-Error Message: \"Collateral ID must reference existing collateral\"\n-Description: References the asset used to secure the loan", "key_type": "Foreign", "display_name": "Collateral ID", "data_type": "Integer", "data_type_full": "Integer", "type": "optional", "required": "Optional", "format": "\"COL-######\"", "values": "N/A", "default": "N/A", "validation": "Foreign Key Check", "error_message": "Collateral ID must reference existing collateral", "description": "References the asset used to secure the loan"}, "originationFee": {"natural_language": "-Attribute name: originationFee\n-Key: Non-unique\n-Display Name: Origination Fee\n-DataType: Decimal\n-Required: Optional\n-Format: \"$#,###.00\"\n-Values: 0.00-N\n-Default: \"0.00\"\n-Validation: Range Check\n-Error Message: \"Origination fee cannot be negative\"\n-Description: Fee charged for processing a new loan application", "key_type": "Non-unique", "display_name": "Origination Fee", "data_type": "Decimal", "data_type_full": "Decimal", "type": "optional", "required": "Optional", "format": "\"$#,###.00\"", "values": "0.00-N", "default": "\"0.00\"", "validation": "Range Check", "error_message": "Origination fee cannot be negative", "description": "Fee charged for processing a new loan application"}, "lateFeePercentage": {"natural_language": "-Attribute name: lateFeePercentage\n-Key: Non-unique\n-Display Name: Late Fee Percentage\n-DataType: Decimal\n-Required: Optional\n-Format: \"0.00%\"\n-Values: 0.00-20.00\n-Default: \"5.00\"\n-Validation: Range Check\n-Error Message: \"Late fee percentage must be between 0% and 20%\"\n-Description: Percentage charged on late payments", "key_type": "Non-unique", "display_name": "Late Fee Percentage", "data_type": "Decimal", "data_type_full": "Decimal", "type": "optional", "required": "Optional", "format": "\"0.00%\"", "values": "0.00-20.00", "default": "\"5.00\"", "validation": "Range Check", "error_message": "Late fee percentage must be between 0% and 20%", "description": "Percentage charged on late payments"}, "earlyPaymentPenalty": {"natural_language": "-Attribute name: earlyPaymentPenalty\n-Key: Non-unique\n-Display Name: Early Payment Penalty\n-DataType: Decimal\n-Required: Optional\n-Format: \"0.00%\"\n-Values: 0.00-10.00\n-Default: \"1.00\"\n-Validation: Range Check\n-Error Message: \"Early payment penalty must be between 0% and 10%\"\n-Description: Penalty charged for early loan payoff", "key_type": "Non-unique", "display_name": "Early Payment Penalty", "data_type": "Decimal", "data_type_full": "Decimal", "type": "optional", "required": "Optional", "format": "\"0.00%\"", "values": "0.00-10.00", "default": "\"1.00\"", "validation": "Range Check", "error_message": "Early payment penalty must be between 0% and 10%", "description": "Penalty charged for early loan payoff"}}, "relationship_properties": {"rel_props_Loan_Customer": {"source_entity": "Loan", "target_entity": "Customer", "on_delete": "Restrict (Prevent deletion of customer with active loans)", "on_update": "Cascade (Update loan records when customer details change)", "foreign_key_type": "Non-Nullable"}}, "synthetic_data": [], "confidential_attributes": [], "internal_attributes": [], "public_attributes": [], "loading_strategies": {}, "archive_strategy": {}, "history_tracking": {}, "workflow": {}, "business_rule_placement": {}, "workflow_placement": {}, "entity_placement": {}, "display_name": "Loan Agreement", "type": "Core Entity", "description": "Represents a loan agreement between the financial institution and a customer.", "enum_attributes": {"status": {"name": "status", "values": ["Active", "Closed", "<PERSON><PERSON><PERSON>", "Restructured"], "source_text": "status (Active, Closed, Default, Restructured)"}, "paymentFrequency": {"name": "paymentFrequency", "values": ["Monthly", "Biweekly", "Weekly", "Quarterly", "Annually"], "source_text": "paymentFrequency (Monthly, Biweekly, Weekly, Quarterly, Annually)"}, "loanType": {"name": "loanType", "values": ["Personal", "Mortgage", "Auto", "Education", "Business"], "source_text": "loanType (Personal, Mortgage, Auto, Education, Business)"}}, "enum_values_list": ["Active", "Closed", "<PERSON><PERSON><PERSON>", "Restructured", "Monthly", "Biweekly", "Weekly", "Quarterly", "Annually", "Personal", "Mortgage", "Auto", "Education", "Business"], "entity_name": "Loan"}, "Customer": {"entity_id": "E18", "name": "Customer", "natural_language": "Customer has customerId^PK, firstName, lastName, email, phone, address, city, state, zipCode, country, dateOfBirth, ssn, creditScore, annualIncome, employmentStatus (Employed, Self-Employed, Unemployed, Retired), employerName, employmentLength, customerType (Individual, Business), status (Active, Inactive, Suspended).", "attributes": {"customerId": {"name": "customerId", "primary_key": true, "foreign_key": false, "calculated": false, "data_type": "Integer", "natural_language": "customerId^PK", "validations": [{"constraint": "be unique", "id": "val_1"}], "default_value": "N/A"}, "firstName": {"name": "firstName", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "firstName", "default_value": "N/A"}, "lastName": {"name": "lastName", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "lastName", "default_value": "N/A"}, "email": {"name": "email", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "email", "validations": [{"constraint": "be a valid email format", "id": "val_2"}], "default_value": "N/A"}, "phone": {"name": "phone", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "phone", "default_value": "N/A"}, "address": {"name": "address", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "address", "default_value": "N/A"}, "city": {"name": "city", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "city", "default_value": "N/A"}, "state": {"name": "state", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "state", "default_value": "N/A"}, "zipCode": {"name": "zipCode", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "zipCode", "default_value": "N/A"}, "country": {"name": "country", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "country", "default_value": "USA"}, "dateOfBirth": {"name": "dateOfBirth", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Date", "natural_language": "dateOfBirth", "validations": [{"constraint": "indicate customer is at least 18 years old", "id": "val_6"}], "default_value": "N/A"}, "ssn": {"name": "ssn", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "ssn", "validations": [{"constraint": "be a valid format and unique", "id": "val_3"}], "default_value": "N/A"}, "creditScore": {"name": "creditScore", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Integer", "natural_language": "creditScore", "validations": [{"constraint": "be between 300 and 850", "id": "val_4"}], "default_value": "N/A"}, "annualIncome": {"name": "annualIncome", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Decimal", "natural_language": "annualIncome", "validations": [{"constraint": "be greater than 0", "id": "val_5"}], "default_value": "N/A"}, "employmentStatus": {"name": "employmentStatus", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Enum", "natural_language": "employmentStatus (Employed, Self-Employed, Unemployed, Retired)", "enum_values": ["Employed", "Self-Employed", "Unemployed", "Retired"], "default_value": "Employed"}, "employerName": {"name": "employerName", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "employerName", "default_value": "N/A"}, "employmentLength": {"name": "employmentLength", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Integer", "natural_language": "employmentLength", "default_value": "0"}, "customerType": {"name": "customerType", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Enum", "natural_language": "customerType (Individual, Business)", "enum_values": ["Individual", "Business"], "default_value": "Individual"}, "status": {"name": "status", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Enum", "natural_language": "status (Active, Inactive, Suspended)", "enum_values": ["Active", "Inactive", "Suspended"], "default_value": "Active"}}, "relationships": {"rel_1": {"entity": "Loan", "type": "one-to-many", "source_attribute": "customerId", "target_attribute": "customerId", "source_entity": "Customer", "target_entity": "Loan", "cardinality": {"source": "1", "target": "n"}, "join_condition": "Customer.customerId = Loan.customerId", "cascade_operations": {"delete": "restrict", "update": "cascade"}, "natural_language": "-Customer has one-to-many relationship with <PERSON><PERSON> using Customer.customerId to Loan.customerId^FK"}, "rel_2": {"entity": "Collateral", "type": "one-to-many", "source_attribute": "customerId", "target_attribute": "customerId", "source_entity": "Customer", "target_entity": "Collateral", "cardinality": {"source": "1", "target": "n"}, "join_condition": "Customer.customerId = Collateral.customerId", "cascade_operations": {"delete": "restrict", "update": "cascade"}, "natural_language": "-Customer has one-to-many relationship with Collateral using Customer.customerId to Collateral.customerId^FK"}}, "business_rules": {}, "calculated_fields": {}, "validations": {"val_1": {"attribute": "customerId", "constraint_text": "be unique", "entity": "Customer", "validation_type": "unique", "parameters": {}, "executable_rule": "IS_UNIQUE(customerId)", "natural_language": "-Customer.customerId must be unique"}, "val_2": {"attribute": "email", "constraint_text": "be a valid email format", "entity": "Customer", "validation_type": "custom", "parameters": {}, "executable_rule": "CUSTOM_VALIDATION(email)", "natural_language": "-Customer.email must be a valid email format"}, "val_3": {"attribute": "ssn", "constraint_text": "be a valid format and unique", "entity": "Customer", "validation_type": "unique", "parameters": {}, "executable_rule": "IS_UNIQUE(ssn)", "natural_language": "-Customer.ssn must be a valid format and unique"}, "val_4": {"attribute": "creditScore", "constraint_text": "be between 300 and 850", "entity": "Customer", "validation_type": "range", "parameters": {"min_value": "300", "max_value": "850"}, "executable_rule": "(creditScore >= 300 AND creditScore <= 850)", "natural_language": "-Customer.creditScore must be between 300 and 850"}, "val_5": {"attribute": "annualIncome", "constraint_text": "be greater than 0", "entity": "Customer", "validation_type": "greater_than", "parameters": {"value": "0"}, "executable_rule": "annualIncome > 0", "natural_language": "-Customer.annualIncome must be greater than 0"}, "val_6": {"attribute": "dateOfBirth", "constraint_text": "indicate customer is at least 18 years old", "entity": "Customer", "validation_type": "enum", "parameters": {}, "executable_rule": "dateOfBirth IS NOT NULL", "natural_language": "-Customer.dateOfBirth must indicate customer is at least 18 years old"}}, "constraints": {}, "attribute_metadata": {"customerId": {"natural_language": "-Attribute name: customerId\n-Key: Primary\n-Display Name: Customer ID\n-DataType: Integer\n-Required: Mandatory\n-Format: \"CUS-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Auto-increment\n-Error Message: \"Invalid Customer ID format\"\n-Description: Unique identifier for the customer", "key_type": "Primary", "display_name": "Customer ID", "data_type": "Integer", "data_type_full": "Integer", "type": "mandatory", "required": "Mandatory", "format": "\"CUS-######\"", "values": "N/A", "default": "N/A", "validation": "Auto-increment", "error_message": "Invalid Customer ID format", "description": "Unique identifier for the customer"}, "firstName": {"natural_language": "-Attribute name: firstName\n-Key: Non-unique\n-Display Name: First Name\n-DataType: String\n-Required: Mandatory\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: \"First name is required\"\n-Description: Customer's first name", "key_type": "Non-unique", "display_name": "First Name", "data_type": "String", "data_type_full": "String", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "error_message": "First name is required", "description": "Customer's first name"}, "lastName": {"natural_language": "-Attribute name: lastName\n-Key: Non-unique\n-Display Name: Last Name\n-DataType: String\n-Required: Mandatory\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: \"Last name is required\"\n-Description: Customer's last name", "key_type": "Non-unique", "display_name": "Last Name", "data_type": "String", "data_type_full": "String", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "error_message": "Last name is required", "description": "Customer's last name"}, "email": {"natural_language": "-Attribute name: email\n-Key: Non-unique\n-Display Name: Email Address\n-DataType: String\n-Required: Mandatory\n-Format: \"<EMAIL>\"\n-Values: N/A\n-Default: N/A\n-Validation: Format Check\n-Error Message: \"Invalid email format\"\n-Description: Customer's email address", "key_type": "Non-unique", "display_name": "Email Address", "data_type": "String", "data_type_full": "String", "type": "mandatory", "required": "Mandatory", "format": "\"<EMAIL>\"", "values": "N/A", "default": "N/A", "validation": "Format Check", "error_message": "Invalid email format", "description": "Customer's email address"}, "phone": {"natural_language": "-Attribute name: phone\n-Key: Non-unique\n-Display Name: Phone Number\n-DataType: String\n-Required: Mandatory\n-Format: \"+#-###-###-####\"\n-Values: N/A\n-Default: N/A\n-Validation: Format Check\n-Error Message: \"Invalid phone number format\"\n-Description: Customer's contact phone number", "key_type": "Non-unique", "display_name": "Phone Number", "data_type": "String", "data_type_full": "String", "type": "mandatory", "required": "Mandatory", "format": "\"+#-###-###-####\"", "values": "N/A", "default": "N/A", "validation": "Format Check", "error_message": "Invalid phone number format", "description": "Customer's contact phone number"}, "address": {"natural_language": "-Attribute name: address\n-Key: Non-unique\n-Display Name: Street Address\n-DataType: String\n-Required: Mandatory\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: \"Address is required\"\n-Description: Customer's street address", "key_type": "Non-unique", "display_name": "Street Address", "data_type": "String", "data_type_full": "String", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "error_message": "Address is required", "description": "Customer's street address"}, "city": {"natural_language": "-Attribute name: city\n-Key: Non-unique\n-Display Name: City\n-DataType: String\n-Required: Mandatory\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: \"City is required\"\n-Description: Customer's city of residence", "key_type": "Non-unique", "display_name": "City", "data_type": "String", "data_type_full": "String", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "error_message": "City is required", "description": "Customer's city of residence"}, "state": {"natural_language": "-Attribute name: state\n-Key: Non-unique\n-Display Name: State/Province\n-DataType: String\n-Required: Mandatory\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: \"State is required\"\n-Description: Customer's state of residence", "key_type": "Non-unique", "display_name": "State/Province", "data_type": "String", "data_type_full": "String", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "error_message": "State is required", "description": "Customer's state of residence"}, "zipCode": {"natural_language": "-Attribute name: zipCode\n-Key: Non-unique\n-Display Name: ZIP/Postal Code\n-DataType: String\n-Required: Mandatory\n-Format: \"#####\" or \"#####-####\"\n-Values: N/A\n-Default: N/A\n-Validation: Format Check\n-Error Message: \"Invalid zip code format\"\n-Description: Customer's zip code", "key_type": "Non-unique", "display_name": "ZIP/Postal Code", "data_type": "String", "data_type_full": "String", "type": "mandatory", "required": "Mandatory", "format": "\"#####\" or \"#####-####\"", "values": "N/A", "default": "N/A", "validation": "Format Check", "error_message": "Invalid zip code format", "description": "Customer's zip code"}, "country": {"natural_language": "-Attribute name: country\n-Key: Non-unique\n-Display Name: Country\n-DataType: String\n-Required: Mandatory\n-Format: N/A\n-Values: N/A\n-Default: \"USA\"\n-Validation: Length Check\n-Error Message: \"Country is required\"\n-Description: Customer's country of residence", "key_type": "Non-unique", "display_name": "Country", "data_type": "String", "data_type_full": "String", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "N/A", "default": "\"USA\"", "validation": "Length Check", "error_message": "Country is required", "description": "Customer's country of residence"}, "dateOfBirth": {"natural_language": "-Attribute name: dateOfBirth\n-Key: Non-unique\n-Display Name: Date of Birth\n-DataType: Date\n-Required: Mandatory\n-Format: \"YYYY-MM-DD\"\n-Values: N/A\n-Default: N/A\n-Validation: Age Check\n-Error Message: \"Customer must be at least 18 years old\"\n-Description: Customer's date of birth", "key_type": "Non-unique", "display_name": "Date of Birth", "data_type": "Date", "data_type_full": "Date", "type": "mandatory", "required": "Mandatory", "format": "\"YYYY-MM-DD\"", "values": "N/A", "default": "N/A", "validation": "Age Check", "error_message": "Customer must be at least 18 years old", "description": "Customer's date of birth"}, "ssn": {"natural_language": "-Attribute name: ssn\n-Key: Non-unique\n-Display Name: Social Security Number\n-DataType: String\n-Required: Mandatory\n-Format: \"###-##-####\"\n-Values: N/A\n-Default: N/A\n-Validation: Format Check\n-Error Message: \"Invalid SSN format\"\n-Description: Customer's Social Security Number", "key_type": "Non-unique", "display_name": "Social Security Number", "data_type": "String", "data_type_full": "String", "type": "mandatory", "required": "Mandatory", "format": "\"###-##-####\"", "values": "N/A", "default": "N/A", "validation": "Format Check", "error_message": "Invalid SSN format", "description": "Customer's Social Security Number"}, "creditScore": {"natural_language": "-Attribute name: creditScore\n-Key: Non-unique\n-Display Name: Credit Score\n-DataType: Integer\n-Required: Mandatory\n-Format: \"###\"\n-Values: 300-850\n-Default: N/A\n-Validation: Range Check\n-Error Message: \"Credit score must be between 300 and 850\"\n-Description: Customer's credit score", "key_type": "Non-unique", "display_name": "Credit Score", "data_type": "Integer", "data_type_full": "Integer", "type": "mandatory", "required": "Mandatory", "format": "\"###\"", "values": "300-850", "default": "N/A", "validation": "Range Check", "error_message": "Credit score must be between 300 and 850", "description": "Customer's credit score"}, "annualIncome": {"natural_language": "-Attribute name: annualIncome\n-Key: Non-unique\n-Display Name: Annual Income\n-DataType: Decimal\n-Required: Mandatory\n-Format: \"$#,###.00\"\n-Values: 0.00-N\n-Default: N/A\n-Validation: Range Check\n-Error Message: \"Annual income must be greater than 0\"\n-Description: Customer's annual income", "key_type": "Non-unique", "display_name": "Annual Income", "data_type": "Decimal", "data_type_full": "Decimal", "type": "mandatory", "required": "Mandatory", "format": "\"$#,###.00\"", "values": "0.00-N", "default": "N/A", "validation": "Range Check", "error_message": "Annual income must be greater than 0", "description": "Customer's annual income"}, "employmentStatus": {"natural_language": "-Attribute name: employmentStatus\n-Key: Non-unique\n-Display Name: Employment Status\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Employed\", \"Self-Employed\", \"Unemployed\", \"Retired\"\n-Default: \"Employed\"\n-Validation: List Check\n-Error Message: \"Invalid employment status\"\n-Description: Customer's current employment status", "key_type": "Non-unique", "display_name": "Employment Status", "data_type": "Enum", "data_type_full": "Enum", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"Employed\", \"Self-Employed\", \"Unemployed\", \"Retired\"", "default": "\"Employed\"", "validation": "List Check", "error_message": "Invalid employment status", "description": "Customer's current employment status"}, "employerName": {"natural_language": "-Attribute name: employerName\n-Key: Non-unique\n-Display Name: Employer Name\n-DataType: String\n-Required: Optional\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: N/A\n-Description: Name of customer's employer", "key_type": "Non-unique", "display_name": "Employer Name", "data_type": "String", "data_type_full": "String", "type": "optional", "required": "Optional", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "description": "Name of customer's employer"}, "employmentLength": {"natural_language": "-Attribute name: employmentLength\n-Key: Non-unique\n-Display Name: Employment Length\n-DataType: Integer\n-Required: Optional\n-Format: \"## years\"\n-Values: 0-N\n-Default: \"0\"\n-Validation: Range Check\n-Error Message: \"Employment length cannot be negative\"\n-Description: Length of employment in years", "key_type": "Non-unique", "display_name": "Employment Length", "data_type": "Integer", "data_type_full": "Integer", "type": "optional", "required": "Optional", "format": "\"## years\"", "values": "0-N", "default": "\"0\"", "validation": "Range Check", "error_message": "Employment length cannot be negative", "description": "Length of employment in years"}, "customerType": {"natural_language": "-Attribute name: customerType\n-Key: Non-unique\n-Display Name: Customer Type\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Individual\", \"Business\"\n-Default: \"Individual\"\n-Validation: List Check\n-Error Message: \"Invalid customer type\"\n-Description: Type of customer", "key_type": "Non-unique", "display_name": "Customer Type", "data_type": "Enum", "data_type_full": "Enum", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"Individual\", \"Business\"", "default": "\"Individual\"", "validation": "List Check", "error_message": "Invalid customer type", "description": "Type of customer"}, "status": {"natural_language": "-Attribute name: status\n-Key: Non-unique\n-Display Name: Account Status\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Active\", \"Inactive\", \"Suspended\"\n-Default: \"Active\"\n-Validation: List Check\n-Error Message: \"Invalid status\"\n-Description: Current status of the customer account", "key_type": "Non-unique", "display_name": "Account Status", "data_type": "Enum", "data_type_full": "Enum", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"Active\", \"Inactive\", \"Suspended\"", "default": "\"Active\"", "validation": "List Check", "error_message": "Invalid status", "description": "Current status of the customer account"}}, "relationship_properties": {"rel_props_Customer_Loan": {"source_entity": "Customer", "target_entity": "Loan", "on_delete": "Restrict (Prevent deletion of customer with active loans)", "on_update": "Cascade (Update loan records when customer details change)", "foreign_key_type": "Non-Nullable"}}, "synthetic_data": [], "confidential_attributes": [], "internal_attributes": [], "public_attributes": [], "loading_strategies": {}, "archive_strategy": {}, "history_tracking": {}, "workflow": {}, "business_rule_placement": {}, "workflow_placement": {}, "entity_placement": {}, "display_name": "Customer", "type": "Core Entity", "description": "Represents a customer of the financial institution.", "enum_attributes": {"employmentStatus": {"name": "employmentStatus", "values": ["Employed", "Self-Employed", "Unemployed", "Retired"], "source_text": "employmentStatus (Employed, Self-Employed, Unemployed, Retired)"}, "customerType": {"name": "customerType", "values": ["Individual", "Business"], "source_text": "customerType (Individual, Business)"}, "status": {"name": "status", "values": ["Active", "Inactive", "Suspended"], "source_text": "status (Active, Inactive, Suspended)"}}, "enum_values_list": ["Employed", "Self-Employed", "Unemployed", "Retired", "Individual", "Business", "Active", "Inactive", "Suspended"], "entity_name": "Customer"}, "Collateral": {"entity_id": "E19", "name": "Collateral", "natural_language": "Collateral has collateralId^PK, customerId^FK, loanId^FK, collateralType (RealEstate, Vehicle, Investment, Cash, Other), description, value, appraisalDate, appraisalValue, lienStatus (FirstLien, SecondLien, NoLien), condition (Excellent, Good, Fair, Poor), assetAddress, assetCity, assetState, assetZipCode, insuranceProvider, insurancePolicyNumber, insuranceExpirationDate, documentationComplete (true, false).", "attributes": {"collateralId": {"name": "collateralId", "primary_key": true, "foreign_key": false, "calculated": false, "data_type": "Integer", "natural_language": "collateralId^PK", "validations": [{"constraint": "be unique", "id": "val_1"}], "default_value": "N/A"}, "customerId": {"name": "customerId", "primary_key": false, "foreign_key": true, "calculated": false, "data_type": "Integer", "natural_language": "customerId^FK", "validations": [{"constraint": "exist in Customer table", "id": "val_2"}, {"constraint": "match the Loan", "id": "val_6"}], "default_value": "N/A"}, "loanId": {"name": "loanId", "primary_key": false, "foreign_key": true, "calculated": false, "data_type": "Integer", "natural_language": "loanId^FK", "validations": [{"constraint": "exist in Loan table", "id": "val_3"}], "default_value": "N/A"}, "collateralType": {"name": "collateralType", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Enum", "natural_language": "collateralType (RealEstate, Vehicle, Investment, Cash, Other)", "enum_values": ["RealEstate", "Vehicle", "Investment", "Cash", "Other"], "default_value": "RealEstate"}, "description": {"name": "description", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "description", "default_value": "N/A"}, "value": {"name": "value", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Decimal", "natural_language": "value", "validations": [{"constraint": "be greater than 0", "id": "val_4"}], "default_value": "N/A"}, "appraisalDate": {"name": "appraisalDate", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Date", "natural_language": "appraisalDate", "default_value": "CURRENT_DATE"}, "appraisalValue": {"name": "appraisalValue", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Decimal", "natural_language": "appraisalValue", "validations": [{"constraint": "be greater than 0", "id": "val_5"}], "default_value": "N/A"}, "lienStatus": {"name": "lienStatus", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Enum", "natural_language": "lienStatus (FirstLien, SecondLien, NoLien)", "enum_values": ["FirstLien", "SecondLien", "NoLien"], "default_value": "FirstLien"}, "condition": {"name": "condition", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Enum", "natural_language": "condition (Excellent, Good, Fair, Poor)", "enum_values": ["Excellent", "Good", "Fair", "Poor"], "default_value": "Good"}, "assetAddress": {"name": "assetAddress", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "assetAddress", "default_value": "N/A"}, "assetCity": {"name": "assetCity", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "assetCity", "default_value": "N/A"}, "assetState": {"name": "assetState", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "assetState", "default_value": "N/A"}, "assetZipCode": {"name": "assetZipCode", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "assetZipCode", "default_value": "N/A"}, "insuranceProvider": {"name": "insuranceProvider", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "insuranceProvider", "default_value": "N/A"}, "insurancePolicyNumber": {"name": "insurancePolicyNumber", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "insurancePolicyNumber", "default_value": "N/A"}, "insuranceExpirationDate": {"name": "insuranceExpirationDate", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Date", "natural_language": "insuranceExpirationDate", "default_value": "N/A"}, "documentationComplete": {"name": "documentationComplete", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Boolean", "natural_language": "documentationComplete (true, false)", "enum_values": ["true", "false"], "default_value": "false"}}, "relationships": {"rel_1": {"entity": "Customer", "type": "many-to-one", "source_attribute": "customerId", "target_attribute": "customerId", "source_entity": "Collateral", "target_entity": "Customer", "cardinality": {"source": "n", "target": "1"}, "join_condition": "Collateral.customerId = Customer.customerId", "cascade_operations": {"delete": "restrict", "update": "cascade"}, "natural_language": "-Collateral has many-to-one relationship with Customer using Collateral.customerId to Customer.customerId^PK"}, "rel_2": {"entity": "Loan", "type": "many-to-one", "source_attribute": "loanId", "target_attribute": "loanId", "source_entity": "Collateral", "target_entity": "Loan", "cardinality": {"source": "n", "target": "1"}, "join_condition": "Collateral.loanId = Loan.loanId", "cascade_operations": {"delete": "restrict", "update": "cascade"}, "natural_language": "-Collateral has many-to-one relationship with Loan using Collateral.loanId to Loan.loanId^PK"}}, "business_rules": {}, "calculated_fields": {}, "validations": {"val_1": {"attribute": "collateralId", "constraint_text": "be unique", "entity": "Collateral", "validation_type": "unique", "parameters": {}, "executable_rule": "IS_UNIQUE(collateralId)", "natural_language": "-Collateral.collateralId must be unique"}, "val_2": {"attribute": "customerId", "constraint_text": "exist in Customer table", "entity": "Collateral", "validation_type": "foreign_key", "parameters": {"referenced_table": "Customer"}, "executable_rule": "FOREIGN_KEY_EXISTS(customerId, Customer)", "natural_language": "-Collateral.customerId must exist in Customer table"}, "val_3": {"attribute": "loanId", "constraint_text": "exist in Loan table", "entity": "Collateral", "validation_type": "foreign_key", "parameters": {"referenced_table": "Loan"}, "executable_rule": "FOREIGN_KEY_EXISTS(loanId, Loan)", "natural_language": "-Collateral.loanId must exist in Loan table"}, "val_4": {"attribute": "value", "constraint_text": "be greater than 0", "entity": "Collateral", "validation_type": "greater_than", "parameters": {"value": "0"}, "executable_rule": "value > 0", "natural_language": "-Collateral.value must be greater than 0"}, "val_5": {"attribute": "appraisalValue", "constraint_text": "be greater than 0", "entity": "Collateral", "validation_type": "greater_than", "parameters": {"value": "0"}, "executable_rule": "appraisalValue > 0", "natural_language": "-Collateral.appraisalValue must be greater than 0"}, "val_6": {"attribute": "customerId", "constraint_text": "match the Loan", "entity": "Collateral", "validation_type": "pattern", "parameters": {}, "executable_rule": "REGEX_MATCH(customerId, '.*')", "natural_language": "-Collateral.customerId must match the Loan.customerId of the associated loan"}}, "constraints": {}, "attribute_metadata": {"collateralId": {"natural_language": "-Attribute name: collateralId\n-Key: Primary\n-Display Name: Collateral ID\n-DataType: Integer\n-Required: Mandatory\n-Format: \"COL-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Auto-increment\n-Error Message: \"Invalid Collateral ID format\"\n-Description: Unique identifier for the collateral", "key_type": "Primary", "display_name": "Collateral ID", "data_type": "Integer", "data_type_full": "Integer", "type": "mandatory", "required": "Mandatory", "format": "\"COL-######\"", "values": "N/A", "default": "N/A", "validation": "Auto-increment", "error_message": "Invalid Collateral ID format", "description": "Unique identifier for the collateral"}, "customerId": {"natural_language": "-Attribute name: customerId\n-Key: Foreign\n-Display Name: Owner Customer ID\n-DataType: Integer\n-Required: Mandatory\n-Format: \"CUS-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Foreign Key Check\n-Error Message: \"Customer ID must reference an existing customer\"\n-Description: References the customer who owns the collateral", "key_type": "Foreign", "display_name": "Owner Customer ID", "data_type": "Integer", "data_type_full": "Integer", "type": "mandatory", "required": "Mandatory", "format": "\"CUS-######\"", "values": "N/A", "default": "N/A", "validation": "Foreign Key Check", "error_message": "Customer ID must reference an existing customer", "description": "References the customer who owns the collateral"}, "loanId": {"natural_language": "-Attribute name: loanId\n-Key: Foreign\n-Display Name: Associated Loan ID\n-DataType: Integer\n-Required: Optional\n-Format: \"LN-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Foreign Key Check\n-Error Message: \"Loan ID must reference an existing loan\"\n-Description: References the loan secured by this collateral", "key_type": "Foreign", "display_name": "Associated Loan ID", "data_type": "Integer", "data_type_full": "Integer", "type": "optional", "required": "Optional", "format": "\"LN-######\"", "values": "N/A", "default": "N/A", "validation": "Foreign Key Check", "error_message": "Loan ID must reference an existing loan", "description": "References the loan secured by this collateral"}, "collateralType": {"natural_language": "-Attribute name: collateralType\n-Key: Non-unique\n-Display Name: Collateral Type\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"RealEstate\", \"Vehicle\", \"Investment\", \"Cash\", \"Other\"\n-Default: \"RealEstate\"\n-Validation: List Check\n-Error Message: \"Invalid collateral type\"\n-Description: Type of asset used as collateral", "key_type": "Non-unique", "display_name": "Collateral Type", "data_type": "Enum", "data_type_full": "Enum", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"RealEstate\", \"Vehicle\", \"Investment\", \"Cash\", \"Other\"", "default": "\"RealEstate\"", "validation": "List Check", "error_message": "Invalid collateral type", "description": "Type of asset used as collateral"}, "description": {"natural_language": "-Attribute name: description\n-Key: Non-unique\n-Display Name: Asset Description\n-DataType: String\n-Required: Mandatory\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: \"Description is required\"\n-Description: Detailed description of the collateral", "key_type": "Non-unique", "display_name": "Asset Description", "data_type": "String", "data_type_full": "String", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "error_message": "Description is required", "description": "Detailed description of the collateral"}, "value": {"natural_language": "-Attribute name: value\n-Key: Non-unique\n-Display Name: Current Value\n-DataType: Decimal\n-Required: Mandatory\n-Format: \"$#,###.00\"\n-Values: 0.00-N\n-Default: N/A\n-Validation: Range Check\n-Error Message: \"Value must be greater than 0\"\n-Description: Current market value of the collateral", "key_type": "Non-unique", "display_name": "Current Value", "data_type": "Decimal", "data_type_full": "Decimal", "type": "mandatory", "required": "Mandatory", "format": "\"$#,###.00\"", "values": "0.00-N", "default": "N/A", "validation": "Range Check", "error_message": "Value must be greater than 0", "description": "Current market value of the collateral"}, "appraisalDate": {"natural_language": "-Attribute name: appraisalDate\n-Key: Non-unique\n-Display Name: Appraisal Date\n-DataType: Date\n-Required: Mandatory\n-Format: \"YYYY-MM-DD\"\n-Values: N/A\n-Default: \"CURRENT_DATE\"\n-Validation: Date Check\n-Error Message: \"Appraisal date is required\"\n-Description: Date when the collateral was appraised", "key_type": "Non-unique", "display_name": "Appraisal Date", "data_type": "Date", "data_type_full": "Date", "type": "mandatory", "required": "Mandatory", "format": "\"YYYY-MM-DD\"", "values": "N/A", "default": "\"CURRENT_DATE\"", "validation": "Date Check", "error_message": "Appraisal date is required", "description": "Date when the collateral was appraised"}, "appraisalValue": {"natural_language": "-Attribute name: appraisalValue\n-Key: Non-unique\n-Display Name: Appraisal Value\n-DataType: Decimal\n-Required: Mandatory\n-Format: \"$#,###.00\"\n-Values: 0.00-N\n-Default: N/A\n-Validation: Range Check\n-Error Message: \"Appraisal value must be greater than 0\"\n-Description: Value determined during appraisal", "key_type": "Non-unique", "display_name": "Appraisal Value", "data_type": "Decimal", "data_type_full": "Decimal", "type": "mandatory", "required": "Mandatory", "format": "\"$#,###.00\"", "values": "0.00-N", "default": "N/A", "validation": "Range Check", "error_message": "Appraisal value must be greater than 0", "description": "Value determined during appraisal"}, "lienStatus": {"natural_language": "-Attribute name: lienStatus\n-Key: Non-unique\n-Display Name: Lien Status\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"FirstLien\", \"SecondLien\", \"NoLien\"\n-Default: \"FirstLien\"\n-Validation: List Check\n-Error Message: \"Invalid lien status\"\n-Description: Status of liens against this collateral", "key_type": "Non-unique", "display_name": "Lien Status", "data_type": "Enum", "data_type_full": "Enum", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"FirstLien\", \"SecondLien\", \"NoLien\"", "default": "\"FirstLien\"", "validation": "List Check", "error_message": "Invalid lien status", "description": "Status of liens against this collateral"}, "condition": {"natural_language": "-Attribute name: condition\n-Key: Non-unique\n-Display Name: Asset Condition\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Excellent\", \"Good\", \"Fair\", \"Poor\"\n-Default: \"Good\"\n-Validation: List Check\n-Error Message: \"Invalid condition\"\n-Description: Physical condition of the collateral", "key_type": "Non-unique", "display_name": "Asset Condition", "data_type": "Enum", "data_type_full": "Enum", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"Excellent\", \"Good\", \"Fair\", \"Poor\"", "default": "\"Good\"", "validation": "List Check", "error_message": "Invalid condition", "description": "Physical condition of the collateral"}, "assetAddress": {"natural_language": "-Attribute name: assetAddress\n-Key: Non-unique\n-Display Name: Asset Street Address\n-DataType: String\n-Required: Optional\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: N/A\n-Description: Address of the physical asset", "key_type": "Non-unique", "display_name": "Asset Street Address", "data_type": "String", "data_type_full": "String", "type": "optional", "required": "Optional", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "description": "Address of the physical asset"}, "assetCity": {"natural_language": "-Attribute name: assetCity\n-Key: Non-unique\n-Display Name: Asset City\n-DataType: String\n-Required: Optional\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: N/A\n-Description: City where the physical asset is located", "key_type": "Non-unique", "display_name": "Asset City", "data_type": "String", "data_type_full": "String", "type": "optional", "required": "Optional", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "description": "City where the physical asset is located"}, "assetState": {"natural_language": "-Attribute name: assetState\n-Key: Non-unique\n-Display Name: Asset State/Province\n-DataType: String\n-Required: Optional\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: N/A\n-Description: State where the physical asset is located", "key_type": "Non-unique", "display_name": "Asset State/Province", "data_type": "String", "data_type_full": "String", "type": "optional", "required": "Optional", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "description": "State where the physical asset is located"}, "assetZipCode": {"natural_language": "-Attribute name: assetZipCode\n-Key: Non-unique\n-Display Name: Asset ZIP/Postal Code\n-DataType: String\n-Required: Optional\n-Format: \"#####\" or \"#####-####\"\n-Values: N/A\n-Default: N/A\n-Validation: Format Check\n-Error Message: \"Invalid zip code format\"\n-Description: Zip code where the physical asset is located", "key_type": "Non-unique", "display_name": "Asset ZIP/Postal Code", "data_type": "String", "data_type_full": "String", "type": "optional", "required": "Optional", "format": "\"#####\" or \"#####-####\"", "values": "N/A", "default": "N/A", "validation": "Format Check", "error_message": "Invalid zip code format", "description": "Zip code where the physical asset is located"}, "insuranceProvider": {"natural_language": "-Attribute name: insuranceProvider\n-Key: Non-unique\n-Display Name: Insurance Provider\n-DataType: String\n-Required: Optional\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: N/A\n-Description: Insurance company covering the collateral", "key_type": "Non-unique", "display_name": "Insurance Provider", "data_type": "String", "data_type_full": "String", "type": "optional", "required": "Optional", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "description": "Insurance company covering the collateral"}, "insurancePolicyNumber": {"natural_language": "-Attribute name: insurancePolicyNumber\n-Key: Non-unique\n-Display Name: Insurance Policy Number\n-DataType: String\n-Required: Optional\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: N/A\n-Description: Insurance policy number for the collateral", "key_type": "Non-unique", "display_name": "Insurance Policy Number", "data_type": "String", "data_type_full": "String", "type": "optional", "required": "Optional", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "description": "Insurance policy number for the collateral"}, "insuranceExpirationDate": {"natural_language": "-Attribute name: insuranceExpirationDate\n-Key: Non-unique\n-Display Name: Insurance Expiration Date\n-DataType: Date\n-Required: Optional\n-Format: \"YYYY-MM-DD\"\n-Values: N/A\n-Default: N/A\n-Validation: Date Check\n-Error Message: \"Expiration date must be in the future\"\n-Description: Date when the insurance policy expires", "key_type": "Non-unique", "display_name": "Insurance Expiration Date", "data_type": "Date", "data_type_full": "Date", "type": "optional", "required": "Optional", "format": "\"YYYY-MM-DD\"", "values": "N/A", "default": "N/A", "validation": "Date Check", "error_message": "Expiration date must be in the future", "description": "Date when the insurance policy expires"}, "documentationComplete": {"natural_language": "-Attribute name: documentationComplete\n-Key: Non-unique\n-Display Name: Documentation Complete\n-DataType: Boolean\n-Required: Mandatory\n-Format: N/A\n-Values: \"true\", \"false\"\n-Default: \"false\"\n-Validation: Boolean Check\n-Error Message: \"Invalid documentation status\"\n-Description: Indicates if all required documentation is complete", "key_type": "Non-unique", "display_name": "Documentation Complete", "data_type": "Boolean", "data_type_full": "Boolean", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"true\", \"false\"", "default": "\"false\"", "validation": "Boolean Check", "error_message": "Invalid documentation status", "description": "Indicates if all required documentation is complete"}}, "relationship_properties": {"rel_props_Collateral_Customer": {"source_entity": "Collateral", "target_entity": "Customer", "on_delete": "Restrict (Prevent deletion of customer with active collateral)", "on_update": "Cascade (Update collateral records when customer details change)", "foreign_key_type": "Non-Nullable"}}, "synthetic_data": [], "confidential_attributes": [], "internal_attributes": [], "public_attributes": [], "loading_strategies": {}, "archive_strategy": {}, "history_tracking": {}, "workflow": {}, "business_rule_placement": {}, "workflow_placement": {}, "entity_placement": {}, "display_name": "Collateral Asset", "type": "Core Entity", "description": "Represents an asset used to secure a loan.", "enum_attributes": {"collateralType": {"name": "collateralType", "values": ["RealEstate", "Vehicle", "Investment", "Cash", "Other"], "source_text": "collateralType (RealEstate, Vehicle, Investment, Cash, Other)"}, "lienStatus": {"name": "lienStatus", "values": ["FirstLien", "SecondLien", "NoLien"], "source_text": "lienStatus (FirstLien, SecondLien, NoLien)"}, "condition": {"name": "condition", "values": ["Excellent", "Good", "Fair", "Poor"], "source_text": "condition (Excellent, Good, Fair, Poor)"}, "documentationComplete": {"name": "documentationComplete", "values": ["true", "false"], "source_text": "documentationComplete (true, false)"}}, "enum_values_list": ["RealEstate", "Vehicle", "Investment", "Cash", "Other", "FirstLien", "SecondLien", "NoLien", "Excellent", "Good", "Fair", "Poor", "true", "false"], "entity_name": "Collateral"}, "Payment": {"entity_id": "E20", "name": "Payment", "natural_language": "Payment has paymentId^PK, loanId^FK, amount, paymentDate, dueDate, status (Pending, Completed, Failed, Cancelled), paymentMethod (CreditCard, DebitCard, BankTransfer, Cash, Check, AutoDebit), referenceNumber, lateFee, paymentType (Regular, ExtraPayment, LatePayment, InterestOnly, PrincipalOnly), notes.", "attributes": {"paymentId": {"name": "paymentId", "primary_key": true, "foreign_key": false, "calculated": false, "data_type": "Integer", "natural_language": "paymentId^PK", "validations": [{"constraint": "be unique", "id": "val_1"}], "default_value": "N/A"}, "loanId": {"name": "loanId", "primary_key": false, "foreign_key": true, "calculated": false, "data_type": "Integer", "natural_language": "loanId^FK", "validations": [{"constraint": "exist in Loan table", "id": "val_2"}], "default_value": "N/A"}, "amount": {"name": "amount", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Decimal", "natural_language": "amount", "validations": [{"constraint": "be greater than 0", "id": "val_3"}], "default_value": "N/A"}, "paymentDate": {"name": "paymentDate", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Date", "natural_language": "paymentDate", "validations": [{"constraint": "be a valid date", "id": "val_5"}], "default_value": "CURRENT_DATE"}, "dueDate": {"name": "dueDate", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Date", "natural_language": "dueDate", "validations": [{"constraint": "be a valid date", "id": "val_4"}], "default_value": "N/A"}, "status": {"name": "status", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Enum", "natural_language": "status (Pending, Completed, Failed, Cancelled)", "enum_values": ["Pending", "Completed", "Failed", "Cancelled"], "validations": [{"constraint": "be one of \"Pending\", \"Completed\", \"Failed\", \"Cancelled\"", "id": "val_6"}], "default_value": "Pending"}, "paymentMethod": {"name": "paymentMethod", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Enum", "natural_language": "paymentMethod (CreditCard, DebitCard, BankTransfer, Cash, Check, AutoDebit)", "enum_values": ["CreditCard", "DebitCard", "BankTransfer", "Cash", "Check", "AutoDebit"], "default_value": "AutoDebit"}, "referenceNumber": {"name": "referenceNumber", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "referenceNumber", "default_value": "N/A"}, "lateFee": {"name": "lateFee", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Decimal", "natural_language": "lateFee", "default_value": "0.00"}, "paymentType": {"name": "paymentType", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "Enum", "natural_language": "paymentType (Regular, ExtraPayment, LatePayment, InterestOnly, PrincipalOnly)", "enum_values": ["Regular", "ExtraPayment", "LatePayment", "InterestOnly", "Principal<PERSON><PERSON><PERSON>"], "default_value": "Regular"}, "notes": {"name": "notes", "primary_key": false, "foreign_key": false, "calculated": false, "data_type": "String", "natural_language": "notes", "default_value": "N/A"}}, "relationships": {"rel_1": {"entity": "Loan", "type": "many-to-one", "source_attribute": "loanId", "target_attribute": "loanId", "source_entity": "Payment", "target_entity": "Loan", "cardinality": {"source": "n", "target": "1"}, "join_condition": "Payment.loanId = Loan.loanId", "cascade_operations": {"delete": "restrict", "update": "cascade"}, "natural_language": "-Payment has many-to-one relationship with Loan using Payment.loanId to Loan.loanId^PK"}}, "business_rules": {}, "calculated_fields": {}, "validations": {"val_1": {"attribute": "paymentId", "constraint_text": "be unique", "entity": "Payment", "validation_type": "unique", "parameters": {}, "executable_rule": "IS_UNIQUE(paymentId)", "natural_language": "-Payment.paymentId must be unique"}, "val_2": {"attribute": "loanId", "constraint_text": "exist in Loan table", "entity": "Payment", "validation_type": "foreign_key", "parameters": {"referenced_table": "Loan"}, "executable_rule": "FOREIGN_KEY_EXISTS(loanId, Loan)", "natural_language": "-Payment.loanId must exist in Loan table"}, "val_3": {"attribute": "amount", "constraint_text": "be greater than 0", "entity": "Payment", "validation_type": "greater_than", "parameters": {"value": "0"}, "executable_rule": "amount > 0", "natural_language": "-Payment.amount must be greater than 0"}, "val_4": {"attribute": "dueDate", "constraint_text": "be a valid date", "entity": "Payment", "validation_type": "custom", "parameters": {}, "executable_rule": "CUSTOM_VALIDATION(dueDate)", "natural_language": "-Payment.dueDate must be a valid date"}, "val_5": {"attribute": "paymentDate", "constraint_text": "be a valid date", "entity": "Payment", "validation_type": "custom", "parameters": {}, "executable_rule": "CUSTOM_VALIDATION(paymentDate)", "natural_language": "-Payment.paymentDate must be a valid date"}, "val_6": {"attribute": "status", "constraint_text": "be one of \"Pending\", \"Completed\", \"Failed\", \"Cancelled\"", "entity": "Payment", "validation_type": "enum", "parameters": {}, "executable_rule": "status IS NOT NULL", "natural_language": "-Payment.status must be one of \"Pending\", \"Completed\", \"Failed\", \"Cancelled\""}}, "constraints": {}, "attribute_metadata": {"paymentId": {"natural_language": "-Attribute name: paymentId\n-Key: Primary\n-Display Name: Payment ID\n-DataType: Integer\n-Required: Mandatory\n-Format: \"PMT-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Auto-increment\n-Error Message: \"Invalid Payment ID format\"\n-Description: Unique identifier for the payment", "key_type": "Primary", "display_name": "Payment ID", "data_type": "Integer", "data_type_full": "Integer", "type": "mandatory", "required": "Mandatory", "format": "\"PMT-######\"", "values": "N/A", "default": "N/A", "validation": "Auto-increment", "error_message": "Invalid Payment ID format", "description": "Unique identifier for the payment"}, "loanId": {"natural_language": "-Attribute name: loanId\n-Key: Foreign\n-Display Name: Loan ID\n-DataType: Integer\n-Required: Mandatory\n-Format: \"LN-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Foreign Key Check\n-Error Message: \"Loan ID must reference an existing loan\"\n-Description: References the loan this payment applies to", "key_type": "Foreign", "display_name": "Loan ID", "data_type": "Integer", "data_type_full": "Integer", "type": "mandatory", "required": "Mandatory", "format": "\"LN-######\"", "values": "N/A", "default": "N/A", "validation": "Foreign Key Check", "error_message": "Loan ID must reference an existing loan", "description": "References the loan this payment applies to"}, "amount": {"natural_language": "-Attribute name: amount\n-Key: Non-unique\n-Display Name: Payment Amount\n-DataType: Decimal\n-Required: Mandatory\n-Format: \"$#,###.00\"\n-Values: 0.01-N\n-Default: N/A\n-Validation: Range Check\n-Error Message: \"Payment amount must be greater than 0\"\n-Description: Amount of the payment", "key_type": "Non-unique", "display_name": "Payment Amount", "data_type": "Decimal", "data_type_full": "Decimal", "type": "mandatory", "required": "Mandatory", "format": "\"$#,###.00\"", "values": "0.01-N", "default": "N/A", "validation": "Range Check", "error_message": "Payment amount must be greater than 0", "description": "Amount of the payment"}, "paymentDate": {"natural_language": "-Attribute name: paymentDate\n-Key: Non-unique\n-Display Name: Payment Date\n-DataType: Date\n-Required: Mandatory\n-Format: \"YYYY-MM-DD\"\n-Values: N/A\n-Default: \"CURRENT_DATE\"\n-Validation: Date Check\n-Error Message: \"Payment date is required\"\n-Description: Date when the payment was made", "key_type": "Non-unique", "display_name": "Payment Date", "data_type": "Date", "data_type_full": "Date", "type": "mandatory", "required": "Mandatory", "format": "\"YYYY-MM-DD\"", "values": "N/A", "default": "\"CURRENT_DATE\"", "validation": "Date Check", "error_message": "Payment date is required", "description": "Date when the payment was made"}, "dueDate": {"natural_language": "-Attribute name: dueDate\n-Key: Non-unique\n-Display Name: Due Date\n-DataType: Date\n-Required: Mandatory\n-Format: \"YYYY-MM-DD\"\n-Values: N/A\n-Default: N/A\n-Validation: Date Check\n-Error Message: \"Due date is required\"\n-Description: Date when the payment is due", "key_type": "Non-unique", "display_name": "Due Date", "data_type": "Date", "data_type_full": "Date", "type": "mandatory", "required": "Mandatory", "format": "\"YYYY-MM-DD\"", "values": "N/A", "default": "N/A", "validation": "Date Check", "error_message": "Due date is required", "description": "Date when the payment is due"}, "status": {"natural_language": "-Attribute name: status\n-Key: Non-unique\n-Display Name: Payment Status\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Pending\", \"Completed\", \"Failed\", \"Cancelled\"\n-Default: \"Pending\"\n-Validation: List Check\n-Error Message: \"Invalid payment status\"\n-Description: Current status of the payment", "key_type": "Non-unique", "display_name": "Payment Status", "data_type": "Enum", "data_type_full": "Enum", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"Pending\", \"Completed\", \"Failed\", \"Cancelled\"", "default": "\"Pending\"", "validation": "List Check", "error_message": "Invalid payment status", "description": "Current status of the payment"}, "paymentMethod": {"natural_language": "-Attribute name: paymentMethod\n-Key: Non-unique\n-Display Name: Payment Method\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"CreditCard\", \"DebitCard\", \"BankTransfer\", \"Cash\", \"Check\", \"AutoDebit\"\n-Default: \"AutoDebit\"\n-Validation: List Check\n-Error Message: \"Invalid payment method\"\n-Description: Method used for payment", "key_type": "Non-unique", "display_name": "Payment Method", "data_type": "Enum", "data_type_full": "Enum", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"CreditCard\", \"DebitCard\", \"BankTransfer\", \"Cash\", \"Check\", \"AutoDebit\"", "default": "\"AutoDebit\"", "validation": "List Check", "error_message": "Invalid payment method", "description": "Method used for payment"}, "referenceNumber": {"natural_language": "-Attribute name: referenceNumber\n-Key: Non-unique\n-Display Name: Reference Number\n-DataType: String\n-Required: Optional\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: N/A\n-Description: External reference number for the payment", "key_type": "Non-unique", "display_name": "Reference Number", "data_type": "String", "data_type_full": "String", "type": "optional", "required": "Optional", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "description": "External reference number for the payment"}, "lateFee": {"natural_language": "-Attribute name: lateFee\n-Key: Non-unique\n-Display Name: Late Fee\n-DataType: Decimal\n-Required: Optional\n-Format: \"$#,###.00\"\n-Values: 0.00-N\n-Default: \"0.00\"\n-Validation: Range Check\n-Error Message: \"Late fee cannot be negative\"\n-Description: Additional fee applied for late payments", "key_type": "Non-unique", "display_name": "Late Fee", "data_type": "Decimal", "data_type_full": "Decimal", "type": "optional", "required": "Optional", "format": "\"$#,###.00\"", "values": "0.00-N", "default": "\"0.00\"", "validation": "Range Check", "error_message": "Late fee cannot be negative", "description": "Additional fee applied for late payments"}, "paymentType": {"natural_language": "-Attribute name: paymentType\n-Key: Non-unique\n-Display Name: Payment Type\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Regular\", \"ExtraPayment\", \"LatePayment\", \"InterestOnly\", \"PrincipalOnly\"\n-Default: \"Regular\"\n-Validation: List Check\n-Error Message: \"Invalid payment type\"\n-Description: Type of payment being made", "key_type": "Non-unique", "display_name": "Payment Type", "data_type": "Enum", "data_type_full": "Enum", "type": "mandatory", "required": "Mandatory", "format": "N/A", "values": "\"Regular\", \"ExtraPayment\", \"LatePayment\", \"InterestOnly\", \"PrincipalOnly\"", "default": "\"Regular\"", "validation": "List Check", "error_message": "Invalid payment type", "description": "Type of payment being made"}, "notes": {"natural_language": "-Attribute name: notes\n-Key: Non-unique\n-Display Name: Payment Notes\n-DataType: String\n-Required: Optional\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: N/A\n-Description: Additional notes about the payment", "key_type": "Non-unique", "display_name": "Payment Notes", "data_type": "String", "data_type_full": "String", "type": "optional", "required": "Optional", "format": "N/A", "values": "N/A", "default": "N/A", "validation": "Length Check", "description": "Additional notes about the payment"}}, "relationship_properties": {"rel_props_Payment_Loan": {"source_entity": "Payment", "target_entity": "Loan", "on_delete": "Cascade (Remove payments when loan is deleted)", "on_update": "Cascade (Update payment records when loan details change)", "foreign_key_type": "Non-Nullable"}}, "synthetic_data": [], "confidential_attributes": [], "internal_attributes": [], "public_attributes": [], "loading_strategies": {}, "archive_strategy": {}, "history_tracking": {}, "workflow": {}, "business_rule_placement": {}, "workflow_placement": {}, "entity_placement": {}, "display_name": "Loan Payment", "type": "Core Entity", "description": "Represents a payment made toward a loan.", "enum_attributes": {"status": {"name": "status", "values": ["Pending", "Completed", "Failed", "Cancelled"], "source_text": "status (Pending, Completed, Failed, Cancelled)"}, "paymentMethod": {"name": "paymentMethod", "values": ["CreditCard", "DebitCard", "BankTransfer", "Cash", "Check", "AutoDebit"], "source_text": "paymentMethod (CreditCard, DebitCard, BankTransfer, Cash, Check, AutoDebit)"}, "paymentType": {"name": "paymentType", "values": ["Regular", "ExtraPayment", "LatePayment", "InterestOnly", "Principal<PERSON><PERSON><PERSON>"], "source_text": "paymentType (Regular, ExtraPayment, LatePayment, InterestOnly, PrincipalOnly)"}}, "enum_values_list": ["Pending", "Completed", "Failed", "Cancelled", "CreditCard", "DebitCard", "BankTransfer", "Cash", "Check", "AutoDebit", "Regular", "ExtraPayment", "LatePayment", "InterestOnly", "Principal<PERSON><PERSON><PERSON>"], "entity_name": "Payment"}}, "parsed_attributes": {}, "parsed_relationships": {}}