{"success": true, "messages": ["Workflow validation completed using enhanced pipeline", "LO validation successful"], "parsed_data": {"local_objectives": {"name": "Submit Leave Request", "version": "1.0", "status": "Active", "workflow_source": "origin", "function_type": "Create", "agent_type": "HUMAN", "execution_rights": "Employee", "natural_language": "name: \"Submit Leave Request\"\nversion: \"1.0\"\nstatus: \"Active\"\nworkflow_source: \"origin\"\nfunction_type: \"Create\"\nagent_type: \"HUMAN\"\n*Employee has execution rights*", "go_id": "GO1", "lo_id": "GO1.LO1"}, "lo_input_stack": {"id": "GO1.LO1.IP1", "lo_id": "GO1.LO1", "description": "Input stack for LeaveApplication", "natural_language": "LeaveApplication with leaveId*, employeeId*, startDate*, endDate*, numDays*, reason*, leaveTypeName* (Annual Leave, Sick Leave, Personal Leave), leaveSubTypeName* [dependent on leaveTypeName], requiresDocumentation* (true, false), status* (Pending, Approved, Rejected), instructions [information]*, allowedNumberOfDays [constant]*"}, "lo_input_items": [{"id": "GO1.LO1.IP1.IT1", "input_stack_id": "GO1.LO1.IP1", "slot_id": "LeaveApplication.leaveId", "required": true, "data_type": "string", "dependent_attribute": false, "dependent_attribute_value": null, "enum_values": null, "read_only": false, "agent_type": "HUMAN", "source_type": "user", "source_description": "* LeaveApplication.leaveId leaveId*", "natural_language": "* LeaveApplication.leaveId leaveId*"}, {"id": "GO1.LO1.IP1.IT2", "input_stack_id": "GO1.LO1.IP1", "slot_id": "LeaveApplication.employeeId", "required": true, "data_type": "string", "dependent_attribute": false, "dependent_attribute_value": null, "enum_values": null, "read_only": false, "agent_type": "HUMAN", "source_type": "user", "source_description": "* LeaveApplication.employeeId employeeId*", "natural_language": "* LeaveApplication.employeeId employeeId*"}, {"id": "GO1.LO1.IP1.IT3", "input_stack_id": "GO1.LO1.IP1", "slot_id": "LeaveApplication.startDate", "required": true, "data_type": "date", "dependent_attribute": false, "dependent_attribute_value": null, "enum_values": null, "read_only": false, "agent_type": "HUMAN", "source_type": "user", "source_description": "* LeaveApplication.startDate startDate*", "natural_language": "* LeaveApplication.startDate startDate*"}, {"id": "GO1.LO1.IP1.IT4", "input_stack_id": "GO1.LO1.IP1", "slot_id": "LeaveApplication.endDate", "required": true, "data_type": "date", "dependent_attribute": false, "dependent_attribute_value": null, "enum_values": null, "read_only": false, "agent_type": "HUMAN", "source_type": "user", "source_description": "* LeaveApplication.endDate endDate*", "natural_language": "* LeaveApplication.endDate endDate*"}, {"id": "GO1.LO1.IP1.IT5", "input_stack_id": "GO1.LO1.IP1", "slot_id": "LeaveApplication.numDays", "required": true, "data_type": "number", "dependent_attribute": false, "dependent_attribute_value": null, "enum_values": null, "read_only": false, "agent_type": "HUMAN", "source_type": "user", "source_description": "* LeaveApplication.numDays numDays*", "natural_language": "* LeaveApplication.numDays numDays*"}, {"id": "GO1.LO1.IP1.IT6", "input_stack_id": "GO1.LO1.IP1", "slot_id": "LeaveApplication.reason", "required": true, "data_type": "string", "dependent_attribute": false, "dependent_attribute_value": null, "enum_values": null, "read_only": false, "agent_type": "HUMAN", "source_type": "user", "source_description": "* LeaveApplication.reason reason*", "natural_language": "* LeaveApplication.reason reason*"}, {"id": "GO1.LO1.IP1.IT7", "input_stack_id": "GO1.LO1.IP1", "slot_id": "LeaveApplication.leaveTypeName", "required": true, "data_type": "string", "dependent_attribute": false, "dependent_attribute_value": null, "enum_values": ["Annual Leave", "Sick Leave", "Personal Leave"], "read_only": false, "agent_type": "HUMAN", "source_type": "user", "source_description": "* LeaveApplication.leaveTypeName leaveTypeName* (Annual Leave, Sick Leave, Personal Leave)", "natural_language": "* LeaveApplication.leaveTypeName leaveTypeName* (Annual Leave, Sick Leave, Personal Leave)"}, {"id": "GO1.LO1.IP1.IT8", "input_stack_id": "GO1.LO1.IP1", "slot_id": "LeaveApplication.leaveSubTypeName", "required": true, "data_type": "string", "dependent_attribute": true, "dependent_attribute_value": "LeaveApplication.leaveTypeName", "enum_values": null, "read_only": false, "agent_type": "HUMAN", "source_type": "user", "source_description": "* LeaveApplication.leaveSubTypeName leaveSubTypeName* [dependent on leaveTypeName]", "natural_language": "* LeaveApplication.leaveSubTypeName leaveSubTypeName* [dependent on leaveTypeName]", "nested_function_id": "GO1.LO1.NF3"}, {"id": "GO1.LO1.IP1.IT9", "input_stack_id": "GO1.LO1.IP1", "slot_id": "LeaveApplication.requiresDocumentation", "required": true, "data_type": "boolean", "dependent_attribute": false, "dependent_attribute_value": null, "enum_values": ["true", "false"], "read_only": false, "agent_type": "HUMAN", "source_type": "user", "source_description": "* LeaveApplication.requiresDocumentation requiresDocumentation* (true, false)", "natural_language": "* LeaveApplication.requiresDocumentation requiresDocumentation* (true, false)"}, {"id": "GO1.LO1.IP1.IT10", "input_stack_id": "GO1.LO1.IP1", "slot_id": "LeaveApplication.status", "required": true, "data_type": "string", "dependent_attribute": false, "dependent_attribute_value": null, "enum_values": ["Pending", "Approved", "Rejected"], "read_only": false, "agent_type": "HUMAN", "source_type": "user", "source_description": "* LeaveApplication.status status* (Pending, Approved, Rejected)", "natural_language": "* LeaveApplication.status status* (Pending, Approved, Rejected)"}, {"id": "GO1.LO1.IP1.IT11", "input_stack_id": "GO1.LO1.IP1", "slot_id": "LeaveApplication.instructions", "required": true, "data_type": "string", "dependent_attribute": true, "dependent_attribute_value": "LeaveApplication.leaveTypeName,LeaveApplication.leaveSubTypeName", "enum_values": null, "read_only": true, "agent_type": "DIGITAL", "source_type": "information", "source_description": "* LeaveApplication.instructions instructions [information]*", "natural_language": "* LeaveApplication.instructions instructions [information]*", "nested_function_id": "GO1.LO1.NF6", "information_field": true}, {"id": "GO1.LO1.IP1.IT12", "input_stack_id": "GO1.LO1.IP1", "slot_id": "LeaveApplication.allowedNumberOfDays", "required": true, "data_type": "number", "dependent_attribute": false, "dependent_attribute_value": null, "enum_values": null, "read_only": false, "agent_type": "HUMAN", "source_type": "constant", "source_description": "* LeaveApplication.allowedNumberOfDays allowedNumberOfDays [constant]*", "natural_language": "* LeaveApplication.allowedNumberOfDays allowedNumberOfDays [constant]*", "constant_field": true}], "lo_output_stack": {"id": "GO1.LO1.OP", "lo_id": "GO1.LO1", "description": "Output stack for SubmitLeaveRequest", "natural_language": "LeaveApplication with leaveId, employeeId, startDate, endDate, numDays, reason, leaveTypeName, leaveSubTypeName, requiresDocumentation, status, submissionDate, allowedNumberOfDays"}, "lo_output_items": [{"id": "GO1.LO1.OP.IT1", "output_stack_id": "GO1.LO1.OP", "slot_id": "LeaveApplication.leaveId", "lo_id": "GO1.LO1", "type": "string", "natural_language": "* LeaveApplication.leaveId"}, {"id": "GO1.LO1.OP.IT2", "output_stack_id": "GO1.LO1.OP", "slot_id": "LeaveApplication.employeeId", "lo_id": "GO1.LO1", "type": "string", "natural_language": "* LeaveApplication.employeeId"}, {"id": "GO1.LO1.OP.IT3", "output_stack_id": "GO1.LO1.OP", "slot_id": "LeaveApplication.startDate", "lo_id": "GO1.LO1", "type": "date", "natural_language": "* LeaveApplication.startDate"}, {"id": "GO1.LO1.OP.IT4", "output_stack_id": "GO1.LO1.OP", "slot_id": "LeaveApplication.endDate", "lo_id": "GO1.LO1", "type": "date", "natural_language": "* LeaveApplication.endDate"}, {"id": "GO1.LO1.OP.IT5", "output_stack_id": "GO1.LO1.OP", "slot_id": "LeaveApplication.numDays", "lo_id": "GO1.LO1", "type": "number", "natural_language": "* LeaveApplication.numDays"}, {"id": "GO1.LO1.OP.IT6", "output_stack_id": "GO1.LO1.OP", "slot_id": "LeaveApplication.reason", "lo_id": "GO1.LO1", "type": "string", "natural_language": "* LeaveApplication.reason"}, {"id": "GO1.LO1.OP.IT7", "output_stack_id": "GO1.LO1.OP", "slot_id": "LeaveApplication.leaveTypeName", "lo_id": "GO1.LO1", "type": "string", "natural_language": "* LeaveApplication.leaveTypeName"}, {"id": "GO1.LO1.OP.IT8", "output_stack_id": "GO1.LO1.OP", "slot_id": "LeaveApplication.leaveSubTypeName", "lo_id": "GO1.LO1", "type": "string", "natural_language": "* LeaveApplication.leaveSubTypeName"}, {"id": "GO1.LO1.OP.IT9", "output_stack_id": "GO1.LO1.OP", "slot_id": "LeaveApplication.requiresDocumentation", "lo_id": "GO1.LO1", "type": "boolean", "natural_language": "* LeaveApplication.requiresDocumentation"}, {"id": "GO1.LO1.OP.IT10", "output_stack_id": "GO1.LO1.OP", "slot_id": "LeaveApplication.status", "lo_id": "GO1.LO1", "type": "string", "natural_language": "* LeaveApplication.status"}, {"id": "GO1.LO1.OP.IT11", "output_stack_id": "GO1.LO1.OP", "slot_id": "LeaveApplication.submissionDate", "lo_id": "GO1.LO1", "type": "datetime", "natural_language": "* LeaveApplication.submissionDate", "nested_function_id": "GO1.LO1.NF4"}, {"id": "GO1.LO1.OP.IT12", "output_stack_id": "GO1.LO1.OP", "slot_id": "LeaveApplication.allowedNumberOfDays", "lo_id": "GO1.LO1", "type": "number", "natural_language": "* LeaveApplication.allowedNumberOfDays"}], "lo_entity_validations": [{"lo_entity_validation_id": "GO1.LO1.EV1", "lo_id": "GO1.LO1", "lo_input_items_id": "GO1.LO1.IP1.IT1", "entity_id": "LeaveApplication", "attribute_id": "LeaveApplication.leaveId", "validation_condition": "mandatory AND unique", "error_message": "Leave ID is required and must be unique", "natural_language": "* LeaveApplication.leaveId (string) is mandatory and unique. Error message: \"Leave ID is required and must be unique\""}, {"lo_entity_validation_id": "GO1.LO1.EV2", "lo_id": "GO1.LO1", "lo_input_items_id": "GO1.LO1.IP1.IT2", "entity_id": "LeaveApplication", "attribute_id": "LeaveApplication.employeeId", "validation_condition": "mandatory AND exists_in(Employee)", "error_message": "Employee ID is required", "natural_language": "* LeaveApplication.employeeId (string) is mandatory and must exist in Employee table. Error message: \"Employee ID is required\""}, {"lo_entity_validation_id": "GO1.LO1.EV3", "lo_id": "GO1.LO1", "lo_input_items_id": "GO1.LO1.IP1.IT3", "entity_id": "LeaveApplication", "attribute_id": "LeaveApplication.startDate", "validation_condition": "mandatory AND valid_date", "error_message": "Start date is required", "natural_language": "* LeaveApplication.startDate (date) is mandatory and must be a valid date. Error message: \"Start date is required\""}, {"lo_entity_validation_id": "GO1.LO1.EV4", "lo_id": "GO1.LO1", "lo_input_items_id": "GO1.LO1.IP1.IT4", "entity_id": "LeaveApplication", "attribute_id": "LeaveApplication.endDate", "validation_condition": "mandatory AND greater_than(LeaveApplication.startDate)", "error_message": "End date must be after start date", "natural_language": "* LeaveApplication.endDate (date) is mandatory and must be after startDate. Error message: \"End date must be after start date\""}, {"lo_entity_validation_id": "GO1.LO1.EV5", "lo_id": "GO1.LO1", "lo_input_items_id": "GO1.LO1.IP1.IT5", "entity_id": "LeaveApplication", "attribute_id": "LeaveApplication.numDays", "validation_condition": "mandatory AND greater_than(0)", "error_message": "Number of days must be greater than 0", "natural_language": "* LeaveApplication.numDays (number) is mandatory and must be greater than 0. Error message: \"Number of days must be greater than 0\""}, {"lo_entity_validation_id": "GO1.LO1.EV6", "lo_id": "GO1.LO1", "lo_input_items_id": "GO1.LO1.IP1.IT6", "entity_id": "LeaveApplication", "attribute_id": "LeaveApplication.reason", "validation_condition": "mandatory AND min_length(10)", "error_message": "Please provide a detailed reason for your leave request", "natural_language": "* LeaveApplication.reason (string) is mandatory with minimum 10 characters. Error message: \"Please provide a detailed reason for your leave request\""}, {"lo_entity_validation_id": "GO1.LO1.EV7", "lo_id": "GO1.LO1", "lo_input_items_id": "GO1.LO1.IP1.IT7", "entity_id": "LeaveApplication", "attribute_id": "LeaveApplication.leaveTypeName", "validation_condition": "mandatory AND enum_value(['Annual Leave', 'Sick Leave', 'Personal Leave'])", "error_message": "Please select a valid leave type", "natural_language": "* LeaveApplication.leaveTypeName (string) is mandatory and must be one of \"Annual Leave\", \"Sick Leave\", \"Personal Leave\". Error message: \"Please select a valid leave type\""}, {"lo_entity_validation_id": "GO1.LO1.EV8", "lo_id": "GO1.LO1", "lo_input_items_id": "GO1.LO1.IP1.IT8", "entity_id": "LeaveApplication", "attribute_id": "LeaveApplication.leaveSubTypeName", "validation_condition": "mandatory AND exists_in(LeaveSubType) AND dependent_on(LeaveApplication.leaveTypeName)", "error_message": "Please select a valid leave sub-type", "natural_language": "* LeaveApplication.leaveSubTypeName (string) is mandatory and must exist in LeaveSubType table for selected leaveTypeName. Error message: \"Please select a valid leave sub-type\""}, {"lo_entity_validation_id": "GO1.LO1.EV9", "lo_id": "GO1.LO1", "lo_input_items_id": "GO1.LO1.IP1.IT12", "entity_id": "LeaveApplication", "attribute_id": "LeaveApplication.allowedNumberOfDays", "validation_condition": "mandatory AND greater_than(0)", "error_message": "Invalid allowed days configuration", "natural_language": "* LeaveApplication.allowedNumberOfDays (number) is mandatory and must be greater than 0. Error message: \"Invalid allowed days configuration\""}, {"lo_entity_validation_id": "GO1.LO1.EV10", "lo_id": "GO1.LO1", "lo_input_items_id": "GO1.LO1.IP1.IT10", "entity_id": "LeaveApplication", "attribute_id": "LeaveApplication.status", "validation_condition": "enum_value(['Pending', 'Approved', 'Rejected'])", "error_message": "Invalid status value", "natural_language": "* LeaveApplication.status (enum) must be one of \"Pending\", \"Approved\", \"Rejected\". Error message: \"Invalid status value\""}], "lo_ui_stack": {"id": "GO1.LO1.UI1", "lo_ui_stack_id": "GO1.LO1.UI1", "lo_ui_form": "FORM", "lo_ui_style_parameters": {}, "natural_language": "* LeaveApplication.leaveId displays as oj-input-text with readonly property.\n* LeaveApplication.employeeId displays as oj-input-text with readonly property and value bound to current_user.\n* LeaveApplication.startDate displays as oj-input-date with min-value set to current date.\n* LeaveApplication.endDate displays as oj-input-date with min-value bound to startDate.\n* LeaveApplication.numDays displays as oj-input-number with readonly property.\n* LeaveApplication.reason displays as oj-text-area with rows=3 and maxlength=500.\n* LeaveApplication.leaveTypeName displays as oj-combobox-one with source from LeaveType entity.\n* LeaveApplication.leaveSubTypeName displays as oj-combobox-one with source dependent on leaveTypeName selection.\n* LeaveApplication.requiresDocumentation displays as oj-switch with readonly property.\n* LeaveApplication.instructions [information] displays as oj-text with formatting-class \"policy-highlight\".\n* LeaveApplication.allowedNumberOfDays displays as oj-input-number with readonly property.\n* System provides contextual help for LeaveApplication.leaveTypeName explaining \"Select the type of leave you are requesting. Different leave types may have different approval requirements.\""}, "lo_ui_entity_attribute_stack": [{"id": "GO1.LO1.UI1.EAUI1", "lo_ui_entity_attribute_stack_id": "GO1.LO1.UI1.EAUI1", "entity_id": "LeaveApplication", "attribute_id": "LeaveApplication.leaveId", "ui_form": "INPUT_TEXT", "style_parameters": {}, "helper_tip": null, "read_only": true, "natural_language": "* LeaveApplication.leaveId displays as oj-input-text with readonly property."}, {"id": "GO1.LO1.UI1.EAUI2", "lo_ui_entity_attribute_stack_id": "GO1.LO1.UI1.EAUI2", "entity_id": "LeaveApplication", "attribute_id": "LeaveApplication.employeeId", "ui_form": "INPUT_TEXT", "style_parameters": {}, "helper_tip": null, "read_only": true, "natural_language": "* LeaveApplication.employeeId displays as oj-input-text with readonly property and value bound to current_user."}, {"id": "GO1.LO1.UI1.EAUI3", "lo_ui_entity_attribute_stack_id": "GO1.LO1.UI1.EAUI3", "entity_id": "LeaveApplication", "attribute_id": "LeaveApplication.startDate", "ui_form": "DATE_PICKER", "style_parameters": {"min_value": "current_date"}, "helper_tip": null, "read_only": false, "natural_language": "* LeaveApplication.startDate displays as oj-input-date with min-value set to current date."}, {"id": "GO1.LO1.UI1.EAUI4", "lo_ui_entity_attribute_stack_id": "GO1.LO1.UI1.EAUI4", "entity_id": "LeaveApplication", "attribute_id": "LeaveApplication.endDate", "ui_form": "DATE_PICKER", "style_parameters": {"min_value": "LeaveApplication.startDate"}, "helper_tip": null, "read_only": false, "natural_language": "* LeaveApplication.endDate displays as oj-input-date with min-value bound to startDate."}, {"id": "GO1.LO1.UI1.EAUI5", "lo_ui_entity_attribute_stack_id": "GO1.LO1.UI1.EAUI5", "entity_id": "LeaveApplication", "attribute_id": "LeaveApplication.numDays", "ui_form": "NUMBER_INPUT", "style_parameters": {}, "helper_tip": null, "read_only": true, "natural_language": "* LeaveApplication.numDays displays as oj-input-number with readonly property."}, {"id": "GO1.LO1.UI1.EAUI6", "lo_ui_entity_attribute_stack_id": "GO1.LO1.UI1.EAUI6", "entity_id": "LeaveApplication", "attribute_id": "LeaveApplication.reason", "ui_form": "TEXT_AREA", "style_parameters": {"rows": 3, "maxlength": 500}, "helper_tip": null, "read_only": false, "natural_language": "* LeaveApplication.reason displays as oj-text-area with rows=3 and maxlength=500."}, {"id": "GO1.LO1.UI1.EAUI7", "lo_ui_entity_attribute_stack_id": "GO1.LO1.UI1.EAUI7", "entity_id": "LeaveApplication", "attribute_id": "LeaveApplication.leaveTypeName", "ui_form": "DROPDOWN", "style_parameters": {"source": "LeaveType"}, "helper_tip": "Select the type of leave you are requesting. Different leave types may have different approval requirements.", "read_only": false, "natural_language": "* LeaveApplication.leaveTypeName displays as oj-combobox-one with source from LeaveType entity."}, {"id": "GO1.LO1.UI1.EAUI8", "lo_ui_entity_attribute_stack_id": "GO1.LO1.UI1.EAUI8", "entity_id": "LeaveApplication", "attribute_id": "LeaveApplication.leaveSubTypeName", "ui_form": "DROPDOWN", "style_parameters": {"source": "dependent on leaveTypeName selection"}, "helper_tip": null, "read_only": false, "natural_language": "* LeaveApplication.leaveSubTypeName displays as oj-combobox-one with source dependent on leaveTypeName selection."}, {"id": "GO1.LO1.UI1.EAUI9", "lo_ui_entity_attribute_stack_id": "GO1.LO1.UI1.EAUI9", "entity_id": "LeaveApplication", "attribute_id": "LeaveApplication.requiresDocumentation", "ui_form": "SWITCH", "style_parameters": {}, "helper_tip": null, "read_only": true, "natural_language": "* LeaveApplication.requiresDocumentation displays as oj-switch with readonly property."}, {"id": "GO1.LO1.UI1.EAUI10", "lo_ui_entity_attribute_stack_id": "GO1.LO1.UI1.EAUI10", "entity_id": "LeaveApplication", "attribute_id": "LeaveApplication.instructions", "ui_form": "TEXT_DISPLAY", "style_parameters": {"formatting_class": "policy-highlight"}, "helper_tip": null, "read_only": true, "natural_language": "* LeaveApplication.instructions [information] displays as oj-text with formatting-class \"policy-highlight\"."}, {"id": "GO1.LO1.UI1.EAUI11", "lo_ui_entity_attribute_stack_id": "GO1.LO1.UI1.EAUI11", "entity_id": "LeaveApplication", "attribute_id": "LeaveApplication.allowedNumberOfDays", "ui_form": "NUMBER_INPUT", "style_parameters": {}, "helper_tip": null, "read_only": true, "natural_language": "* LeaveApplication.allowedNumberOfDays displays as oj-input-number with readonly property."}], "lo_data_mapping_stack": {"id": "GO1.LO1.MP1", "lo_data_mapping_stack_id": "GO1.LO1.MP1", "lo_id": "GO1.LO1", "description": "Data mapping stack for SubmitLeaveRequest", "created_at": null, "created_by": null, "updated_by": null, "updated_at": null, "natural_language": "* SubmitLeaveRequest.output.leaveId maps to UploadDocumentation.input.leaveId using direct mapping.\n* SubmitLeaveRequest.output.employeeId maps to UploadDocumentation.input.employeeId using direct mapping.\n* SubmitLeaveRequest.output.leaveTypeName maps to UploadDocumentation.input.leaveTypeName using direct mapping.\n* SubmitLeaveRequest.output.leaveSubTypeName maps to UploadDocumentation.input.leaveSubTypeName using direct mapping.\n* SubmitLeaveRequest.output.requiresDocumentation maps to UploadDocumentation.input.requiresDocumentation using direct mapping.\n* SubmitLeaveRequest.output.leaveId maps to ReviewLeaveRequest.input.leaveId using direct mapping.\n* SubmitLeaveRequest.output.employeeId maps to ReviewLeaveRequest.input.employeeId using direct mapping.\n* SubmitLeaveRequest.output.startDate maps to ReviewLeaveRequest.input.startDate using direct mapping.\n* SubmitLeaveRequest.output.endDate maps to ReviewLeaveRequest.input.endDate using direct mapping.\n* SubmitLeaveRequest.output.numDays maps to ReviewLeaveRequest.input.numDays using direct mapping.\n* SubmitLeaveRequest.output.reason maps to ReviewLeaveRequest.input.reason using direct mapping.\n* SubmitLeaveRequest.output.leaveTypeName maps to ReviewLeaveRequest.input.leaveTypeName using direct mapping.\n* SubmitLeaveRequest.output.leaveSubTypeName maps to ReviewLeaveRequest.input.leaveSubTypeName using direct mapping.\n* SubmitLeaveRequest.output.allowedNumberOfDays maps to ReviewLeaveRequest.input.allowedNumberOfDays using direct mapping."}, "lo_data_mappings": [{"id": "GO1.LO1.MP1.IT1", "lo_data_mapping_item_id": "GO1.LO1.MP1.IT1", "mapping_stack": "GO1.LO1.MP1", "source_output_stack_item_id": "GO1.LO1.OP.IT1", "source_entity": "LeaveApplication", "source_attribute": "LeaveApplication.leaveId", "target_input_stack_item_id": "GO1.LO2.IP1.IT1", "target_entity": "UploadDocumentation", "target_attribute": "UploadDocumentation.leaveId", "mapping_type": "direct", "natural_language": "* SubmitLeaveRequest.output.leaveId maps to UploadDocumentation.input.leaveId using direct mapping."}, {"id": "GO1.LO1.MP1.IT2", "lo_data_mapping_item_id": "GO1.LO1.MP1.IT2", "mapping_stack": "GO1.LO1.MP1", "source_output_stack_item_id": "GO1.LO1.OP.IT2", "source_entity": "LeaveApplication", "source_attribute": "LeaveApplication.employeeId", "target_input_stack_item_id": "GO1.LO2.IP1.IT2", "target_entity": "UploadDocumentation", "target_attribute": "UploadDocumentation.employeeId", "mapping_type": "direct", "natural_language": "* SubmitLeaveRequest.output.employeeId maps to UploadDocumentation.input.employeeId using direct mapping."}, {"id": "GO1.LO1.MP1.IT3", "lo_data_mapping_item_id": "GO1.LO1.MP1.IT3", "mapping_stack": "GO1.LO1.MP1", "source_output_stack_item_id": "GO1.LO1.OP.IT7", "source_entity": "LeaveApplication", "source_attribute": "LeaveApplication.leaveTypeName", "target_input_stack_item_id": "GO1.LO2.IP1.IT3", "target_entity": "UploadDocumentation", "target_attribute": "UploadDocumentation.leaveTypeName", "mapping_type": "direct", "natural_language": "* SubmitLeaveRequest.output.leaveTypeName maps to UploadDocumentation.input.leaveTypeName using direct mapping."}, {"id": "GO1.LO1.MP1.IT4", "lo_data_mapping_item_id": "GO1.LO1.MP1.IT4", "mapping_stack": "GO1.LO1.MP1", "source_output_stack_item_id": "GO1.LO1.OP.IT8", "source_entity": "LeaveApplication", "source_attribute": "LeaveApplication.leaveSubTypeName", "target_input_stack_item_id": "GO1.LO2.IP1.IT4", "target_entity": "UploadDocumentation", "target_attribute": "UploadDocumentation.leaveSubTypeName", "mapping_type": "direct", "natural_language": "* SubmitLeaveRequest.output.leaveSubTypeName maps to UploadDocumentation.input.leaveSubTypeName using direct mapping."}, {"id": "GO1.LO1.MP1.IT5", "lo_data_mapping_item_id": "GO1.LO1.MP1.IT5", "mapping_stack": "GO1.LO1.MP1", "source_output_stack_item_id": "GO1.LO1.OP.IT9", "source_entity": "LeaveApplication", "source_attribute": "LeaveApplication.requiresDocumentation", "target_input_stack_item_id": "GO1.LO2.IP1.IT5", "target_entity": "UploadDocumentation", "target_attribute": "UploadDocumentation.requiresDocumentation", "mapping_type": "direct", "natural_language": "* SubmitLeaveRequest.output.requiresDocumentation maps to UploadDocumentation.input.requiresDocumentation using direct mapping."}, {"id": "GO1.LO1.MP1.IT6", "lo_data_mapping_item_id": "GO1.LO1.MP1.IT6", "mapping_stack": "GO1.LO1.MP1", "source_output_stack_item_id": "GO1.LO1.OP.IT1", "source_entity": "LeaveApplication", "source_attribute": "LeaveApplication.leaveId", "target_input_stack_item_id": "GO1.LO3.IP1.IT1", "target_entity": "ReviewLeaveRequest", "target_attribute": "ReviewLeaveRequest.leaveId", "mapping_type": "direct", "natural_language": "* SubmitLeaveRequest.output.leaveId maps to ReviewLeaveRequest.input.leaveId using direct mapping."}, {"id": "GO1.LO1.MP1.IT7", "lo_data_mapping_item_id": "GO1.LO1.MP1.IT7", "mapping_stack": "GO1.LO1.MP1", "source_output_stack_item_id": "GO1.LO1.OP.IT7", "source_entity": "LeaveApplication", "source_attribute": "LeaveApplication.leaveTypeName", "target_input_stack_item_id": "GO1.LO3.IP1.IT2", "target_entity": "ReviewLeaveRequest", "target_attribute": "ReviewLeaveRequest.leaveTypeName", "mapping_type": "direct", "natural_language": "* SubmitLeaveRequest.output.leaveTypeName maps to ReviewLeaveRequest.input.leaveTypeName using direct mapping."}, {"id": "GO1.LO1.MP1.IT8", "lo_data_mapping_item_id": "GO1.LO1.MP1.IT8", "mapping_stack": "GO1.LO1.MP1", "source_output_stack_item_id": "GO1.LO1.OP.IT8", "source_entity": "LeaveApplication", "source_attribute": "LeaveApplication.leaveSubTypeName", "target_input_stack_item_id": "GO1.LO3.IP1.IT3", "target_entity": "ReviewLeaveRequest", "target_attribute": "ReviewLeaveRequest.leaveSubTypeName", "mapping_type": "direct", "natural_language": "* SubmitLeaveRequest.output.leaveSubTypeName maps to ReviewLeaveRequest.input.leaveSubTypeName using direct mapping."}, {"id": "GO1.LO1.MP1.IT9", "lo_data_mapping_item_id": "GO1.LO1.MP1.IT9", "mapping_stack": "GO1.LO1.MP1", "source_output_stack_item_id": "GO1.LO1.OP.IT9", "source_entity": "LeaveApplication", "source_attribute": "LeaveApplication.allowedNumberOfDays", "target_input_stack_item_id": "GO1.LO3.IP1.IT4", "target_entity": "ReviewLeaveRequest", "target_attribute": "ReviewLeaveRequest.allowedNumberOfDays", "mapping_type": "direct", "natural_language": "* SubmitLeaveRequest.output.allowedNumberOfDays maps to ReviewLeaveRequest.input.allowedNumberOfDays using direct mapping."}], "lo_nested_functions": [{"id": "GO1.LO1.NF1", "lo_id": "GO1.LO1", "function_name": "generate_id", "function_type": "standard", "function_parameters": ["prefix"], "description": "Generates a unique ID with the given prefix followed by a year and sequence number", "returns": "string", "outputs_to": "LeaveApplication.leaveId", "natural_language": "* Function generate_id(prefix) for LeaveApplication.leaveId"}, {"id": "GO1.LO1.NF2", "lo_id": "GO1.LO1", "function_name": "subtract_days", "function_type": "standard", "function_parameters": ["startDate", "endDate"], "description": "Calculates the number of days between two dates, inclusive of both start and end dates", "returns": "number", "outputs_to": "LeaveApplication.numDays", "natural_language": "* Function subtract_days(startDate, endDate) for LeaveApplication.numDays"}, {"id": "GO1.LO1.NF3", "lo_id": "GO1.LO1", "function_name": "fetch_leave_subtypes", "function_type": "standard", "function_parameters": ["leaveTypeName"], "description": "Retrieves available sub-types for the selected leave type", "returns": "array of objects", "outputs_to": "LeaveApplication.leaveSubTypeName", "natural_language": "* Function fetch_leave_subtypes(leaveTypeName) for LeaveApplication.leaveSubTypeName"}, {"id": "GO1.LO1.NF4", "lo_id": "GO1.LO1", "function_name": "current_timestamp", "function_type": "standard", "function_parameters": [], "description": "Returns the current date and time", "returns": "datetime", "outputs_to": "LeaveApplication.submissionDate", "natural_language": "* Function current_timestamp() for LeaveApplication.submissionDate"}, {"id": "GO1.LO1.NF5", "lo_id": "GO1.LO1", "function_name": "format_enum_value", "function_type": "standard", "function_parameters": ["value"], "description": "Formats an enum value for display", "returns": "string", "outputs_to": "LeaveApplication.leaveTypeName", "natural_language": "* Function format_enum_value(value) for LeaveApplication.leaveTypeName"}, {"id": "GO1.LO1.NF6", "lo_id": "GO1.LO1", "function_name": "fetch_records", "function_type": "standard", "function_parameters": ["entityName", "filterCondition"], "description": "Retrieves relevant instructions and guidelines based on leave type and sub-type", "returns": "string", "outputs_to": "LeaveApplication.instructions", "natural_language": "* Function fetch_records(entityName, filterCondition) for LeaveApplication.instructions"}, {"id": "GO1.LO1.NF7", "lo_id": "GO1.LO1", "function_name": "fetch_records", "function_type": "standard", "function_parameters": ["entityName", "attribute"], "description": "Retrieves the maximum allowed days for leave from Constants entity", "returns": "number", "outputs_to": "LeaveApplication.allowedNumberOfDays", "natural_language": "* Function fetch_records(entityName, attribute) for LeaveApplication.allowedNumberOfDays"}], "lo_nested_function_input_stacks": [{"id": "GO1.LO1.NFIS1", "lo_id": "GO1.LO1", "function_name": "* Function generate_id(prefix) for LeaveApplication.leaveId\n  - Description", "stack_name": "* Function generate_id(prefix) for LeaveApplication.leaveId\n  - Description_input_stack", "stack_description": "Input stack for nested function * Function generate_id(prefix) for LeaveApplication.leaveId\n  - Description", "stack_type": "input", "is_active": true, "created_at": null, "created_by": null, "updated_at": null, "updated_by": null}, {"id": "GO1.LO1.NFIS2", "lo_id": "GO1.LO1", "function_name": "Function subtract_days(startDate, endDate) for LeaveApplication.numDays\n  - Description", "stack_name": "Function subtract_days(startDate, endDate) for LeaveApplication.numDays\n  - Description_input_stack", "stack_description": "Input stack for nested function Function subtract_days(startDate, endDate) for LeaveApplication.numDays\n  - Description", "stack_type": "input", "is_active": true, "created_at": null, "created_by": null, "updated_at": null, "updated_by": null}, {"id": "GO1.LO1.NFIS3", "lo_id": "GO1.LO1", "function_name": "Function fetch_leave_subtypes(leaveTypeName) for LeaveApplication.leaveSubTypeName\n  - Description", "stack_name": "Function fetch_leave_subtypes(leaveTypeName) for LeaveApplication.leaveSubTypeName\n  - Description_input_stack", "stack_description": "Input stack for nested function Function fetch_leave_subtypes(leaveTypeName) for LeaveApplication.leaveSubTypeName\n  - Description", "stack_type": "input", "is_active": true, "created_at": null, "created_by": null, "updated_at": null, "updated_by": null}, {"id": "GO1.LO1.NFIS4", "lo_id": "GO1.LO1", "function_name": "Function fetch_records(entityName, filterCondition) for LeaveApplication.instructions\n  - Description", "stack_name": "Function fetch_records(entityName, filterCondition) for LeaveApplication.instructions\n  - Description_input_stack", "stack_description": "Input stack for nested function Function fetch_records(entityName, filterCondition) for LeaveApplication.instructions\n  - Description", "stack_type": "input", "is_active": true, "created_at": null, "created_by": null, "updated_at": null, "updated_by": null}, {"id": "GO1.LO1.NFIS5", "lo_id": "GO1.LO1", "function_name": "Function fetch_records(entityName, attribute) for LeaveApplication.allowedNumberOfDays\n  - Description", "stack_name": "Function fetch_records(entityName, attribute) for LeaveApplication.allowedNumberOfDays\n  - Description_input_stack", "stack_description": "Input stack for nested function Function fetch_records(entityName, attribute) for LeaveApplication.allowedNumberOfDays\n  - Description", "stack_type": "input", "is_active": true, "created_at": null, "created_by": null, "updated_at": null, "updated_by": null}, {"id": "GO1.LO1.NFIS6", "lo_id": "GO1.LO1", "function_name": "Function current_timestamp() for LeaveApplication.submissionDate\n  - Description", "stack_name": "Function current_timestamp() for LeaveApplication.submissionDate\n  - Description_input_stack", "stack_description": "Input stack for nested function Function current_timestamp() for LeaveApplication.submissionDate\n  - Description", "stack_type": "input", "is_active": true, "created_at": null, "created_by": null, "updated_at": null, "updated_by": null}, {"id": "GO1.LO1.NFIS7", "lo_id": "GO1.LO1", "function_name": "Function format_enum_value(value) for LeaveApplication.leaveTypeName\n  - Description", "stack_name": "Function format_enum_value(value) for LeaveApplication.leaveTypeName\n  - Description_input_stack", "stack_description": "Input stack for nested function Function format_enum_value(value) for LeaveApplication.leaveTypeName\n  - Description", "stack_type": "input", "is_active": true, "created_at": null, "created_by": null, "updated_at": null, "updated_by": null}], "lo_nested_function_output_items": [{"id": "GO1.LO1.NFO1", "lo_id": "GO1.LO1", "function_name": "Function current_timestamp() for LeaveApplication.submissionDate\n  - Description", "output_name": "Function current_timestamp() for LeaveApplication.submissionDate\n  - Description_return", "output_description": "Return value from Function current_timestamp() for LeaveApplication.submissionDate\n  - Description: the current date and time", "data_type": "date", "is_required": true, "format": null, "created_at": null, "created_by": null, "updated_at": null, "updated_by": null}], "lo_nested_function_output_stacks": [{"id": "GO1.LO1.NFOS1", "lo_id": "GO1.LO1", "function_name": "* Function generate_id(prefix) for LeaveApplication.leaveId\n  - Description", "stack_name": "* Function generate_id(prefix) for LeaveApplication.leaveId\n  - Description_output_stack", "stack_description": "Output stack for nested function * Function generate_id(prefix) for LeaveApplication.leaveId\n  - Description", "stack_type": "output", "is_active": true, "created_at": null, "created_by": null, "updated_at": null, "updated_by": null}, {"id": "GO1.LO1.NFOS2", "lo_id": "GO1.LO1", "function_name": "Function subtract_days(startDate, endDate) for LeaveApplication.numDays\n  - Description", "stack_name": "Function subtract_days(startDate, endDate) for LeaveApplication.numDays\n  - Description_output_stack", "stack_description": "Output stack for nested function Function subtract_days(startDate, endDate) for LeaveApplication.numDays\n  - Description", "stack_type": "output", "is_active": true, "created_at": null, "created_by": null, "updated_at": null, "updated_by": null}, {"id": "GO1.LO1.NFOS3", "lo_id": "GO1.LO1", "function_name": "Function fetch_leave_subtypes(leaveTypeName) for LeaveApplication.leaveSubTypeName\n  - Description", "stack_name": "Function fetch_leave_subtypes(leaveTypeName) for LeaveApplication.leaveSubTypeName\n  - Description_output_stack", "stack_description": "Output stack for nested function Function fetch_leave_subtypes(leaveTypeName) for LeaveApplication.leaveSubTypeName\n  - Description", "stack_type": "output", "is_active": true, "created_at": null, "created_by": null, "updated_at": null, "updated_by": null}, {"id": "GO1.LO1.NFOS4", "lo_id": "GO1.LO1", "function_name": "Function fetch_records(entityName, filterCondition) for LeaveApplication.instructions\n  - Description", "stack_name": "Function fetch_records(entityName, filterCondition) for LeaveApplication.instructions\n  - Description_output_stack", "stack_description": "Output stack for nested function Function fetch_records(entityName, filterCondition) for LeaveApplication.instructions\n  - Description", "stack_type": "output", "is_active": true, "created_at": null, "created_by": null, "updated_at": null, "updated_by": null}, {"id": "GO1.LO1.NFOS5", "lo_id": "GO1.LO1", "function_name": "Function fetch_records(entityName, attribute) for LeaveApplication.allowedNumberOfDays\n  - Description", "stack_name": "Function fetch_records(entityName, attribute) for LeaveApplication.allowedNumberOfDays\n  - Description_output_stack", "stack_description": "Output stack for nested function Function fetch_records(entityName, attribute) for LeaveApplication.allowedNumberOfDays\n  - Description", "stack_type": "output", "is_active": true, "created_at": null, "created_by": null, "updated_at": null, "updated_by": null}, {"id": "GO1.LO1.NFOS6", "lo_id": "GO1.LO1", "function_name": "Function current_timestamp() for LeaveApplication.submissionDate\n  - Description", "stack_name": "Function current_timestamp() for LeaveApplication.submissionDate\n  - Description_output_stack", "stack_description": "Output stack for nested function Function current_timestamp() for LeaveApplication.submissionDate\n  - Description", "stack_type": "output", "is_active": true, "created_at": null, "created_by": null, "updated_at": null, "updated_by": null}, {"id": "GO1.LO1.NFOS7", "lo_id": "GO1.LO1", "function_name": "Function format_enum_value(value) for LeaveApplication.leaveTypeName\n  - Description", "stack_name": "Function format_enum_value(value) for LeaveApplication.leaveTypeName\n  - Description_output_stack", "stack_description": "Output stack for nested function Function format_enum_value(value) for LeaveApplication.leaveTypeName\n  - Description", "stack_type": "output", "is_active": true, "created_at": null, "created_by": null, "updated_at": null, "updated_by": null}]}, "validation_errors": null, "parsed_gos": {}, "parsed_los": {"name": "Submit Leave Request", "version": "1.0", "status": "Active", "workflow_source": "origin", "function_type": "Create", "agent_type": "HUMAN", "execution_rights": "Employee", "natural_language": "name: \"Submit Leave Request\"\nversion: \"1.0\"\nstatus: \"Active\"\nworkflow_source: \"origin\"\nfunction_type: \"Create\"\nagent_type: \"HUMAN\"\n*Employee has execution rights*", "go_id": "GO1", "lo_id": "GO1.LO1"}}