{"conversational_ui_information_hierarchy": {"version": "1.0", "description": "Complete 6-level information hierarchy for Leave Analytics Dashboard", "implementation_date": "2025-01-15", "user_context": {"current_user": "<PERSON>", "role": "Senior Developer", "session_id": "SES-20250115-102430", "request_timestamp": "2025-01-15T10:24:31Z"}, "spatial_organization": {"layout_structure": {"chat_area": {"width_percentage": "60-70%", "primary_purpose": "Action-focused interaction", "information_levels": ["Level 0 (hidden)", "Level 1 (primary)", "Level 2 (critical)"]}, "side_panel": {"width_percentage": "30-40%", "primary_purpose": "Contextual decision support", "information_levels": ["Level 2 (extended)", "Level 3 (reference)", "Level 4 (analytics)", "Level 5 (system)"]}}}, "level_0_intent_context": {"hierarchy_position": "Foundation layer - invisible to user", "processing_location": "Background system", "cognitive_load": "Zero", "processing_time": "<100ms", "intent_recognition": {"user_input": "I need to take some time off next week", "system_interpretation": {"parsed_intent": "LEAVE_REQUEST", "confidence_score": 0.92, "entity_extraction": {"action": "take time off", "timeframe": "next week → Jan 15-19, 2025", "type": "unspecified → inferred annual leave"}}, "context_preparation": {"user_patterns": "3.5 day average, Friday-Monday preference", "team_context": "<PERSON> conflict detected", "system_decisions": ["Load leave form", "Apply smart defaults", "Check team availability"]}}}, "level_1_primary_actions": {"hierarchy_position": "Essential interaction - always visible", "display_location": "Chat area embedded form", "cognitive_load": "High - active decision making", "visibility": "Prominent above fold", "textual_hierarchy": {"primary_heading": {"text": "🏖️ LEAVE REQUEST", "typography": "18px, font-weight: 600", "purpose": "Form identification", "hierarchy_level": "H1"}, "field_labels": {"hierarchy_level": "H2 equivalent", "typography": "14px, font-weight: 500, color: #333", "examples": [{"label": "Leave Type", "purpose": "Primary categorization", "required_indicator": "Visual emphasis"}, {"label": "Duration", "purpose": "Date range selection", "helper_text": "Start date to end date"}, {"label": "💬 Reason (optional)", "purpose": "Context gathering", "optional_indicator": "Parenthetical notation"}]}, "action_buttons": {"hierarchy_level": "Primary CTA", "primary_action": {"text": "Submit Request", "typography": "14px, font-weight: 500", "visual_hierarchy": "Highest prominence, blue background"}, "secondary_actions": {"typography": "14px, normal weight", "visual_hierarchy": "Lower prominence, secondary styling", "actions": ["Save as Draft", "Cancel"]}}}, "essential_inputs": {"leave_type_dropdown": {"selected_value": "Annual Leave (15 days remaining)", "smart_default_applied": true, "validation": "Real-time balance checking"}, "date_selection": {"start_date": "Jan 15, 2024", "end_date": "Jan 17, 2024", "calculated_duration": "3 days", "calendar_integration": "Interactive widget"}, "reason_field": {"pre_filled": "Family vacation to Orlando", "character_limit": 500, "optional": true}}}, "level_2_contextual_information": {"hierarchy_position": "Decision influencing - dual placement", "cognitive_load": "Medium - peripheral awareness", "placement_strategy": "Critical in chat, extended in panel", "chat_area_critical": {"textual_hierarchy": {"warning_headers": {"typography": "14px, font-weight: 500", "icon_integration": "Leading emoji/icon", "hierarchy_level": "Alert H3", "examples": ["⚠️ 2-day overlap with <PERSON> (Lead Developer)", "💡 Manager is available for approval all week", "ℹ️ You'll have 12 days remaining after this request"]}, "info_box_structure": {"container_styling": "Background: #FFF3CD, border: #FFE69C", "item_hierarchy": {"icon": "Leading visual indicator", "message": "Primary information text", "typography": "14px, normal weight"}}}, "ai_suggestion_critical": {"textual_hierarchy": {"suggestion_header": {"text": "💡 AI Suggestion", "typography": "16px, font-weight: 600", "hierarchy_level": "H2"}, "recommendation_text": {"text": "Consider Jan 21-23 instead - no conflicts and 95% approval probability", "typography": "14px, normal weight", "hierarchy_level": "Body text"}, "reasoning_list": {"hierarchy_level": "Supporting details", "typography": "13px, color: #666", "items": ["No team conflicts", "Project Alpha completed", "95% approval probability"]}}}}, "side_panel_extended": {"textual_hierarchy_structure": {"card_headers": {"typography": "14px, font-weight: 600", "icon_integration": "Emoji prefix", "hierarchy_level": "Card H1", "color": "#333", "margin_bottom": "16px"}, "card_subheaders": {"typography": "13px, font-weight: 500", "hierarchy_level": "Card H2", "color": "#666", "margin_bottom": "8px"}, "body_text": {"typography": "13px, normal weight", "hierarchy_level": "Card body", "color": "#333", "line_height": "1.4"}, "metadata_text": {"typography": "12px, normal weight", "hierarchy_level": "Card metadata", "color": "#888", "font_style": "Secondary information"}}, "leave_analytics_card": {"card_structure": {"card_header": {"primary_title": {"text": "📊 LEAVE ANALYTICS", "typography": "14px, font-weight: 600", "hierarchy_level": "Card H1"}, "secondary_indicator": {"text": "Auto-refresh ⟳", "typography": "12px, color: #888", "hierarchy_level": "Card metadata"}}, "balance_section": {"section_label": {"text": "Your Balance", "typography": "13px, color: #666, margin-bottom: 8px", "hierarchy_level": "Section H2"}, "progress_indicator": {"visual_element": "Progress bar", "text_label": {"text": "15/20 days", "typography": "13px, text-align: right, margin-top: 4px", "hierarchy_level": "Data label"}}}, "optimal_days_section": {"section_label": {"text": "Optimal Days (High Approval ✓)", "typography": "13px, color: #666, margin-bottom: 8px", "hierarchy_level": "Section H2"}, "calendar_grid": {"day_headers": {"typography": "12px, color: #888, font-weight: 500", "hierarchy_level": "Grid headers", "items": ["Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>"]}, "date_cells": {"typography": "12px, padding: 4px", "hierarchy_level": "Grid data", "color_coding": "Background colors indicate approval probability"}}, "legend": {"typography": "12px, display: flex, gap: 16px", "hierarchy_level": "Legend items", "items": [{"label": "Best", "color": "#4CAF50"}, {"label": "Good", "color": "#FFE69C"}, {"label": "Busy", "color": "#e0e0e0"}]}}}}, "team_availability_card": {"card_structure": {"card_header": {"primary_title": {"text": "👥 TEAM AVAILABILITY", "typography": "14px, font-weight: 600", "hierarchy_level": "Card H1"}, "time_context": {"text": "Week of Jan 15-19:", "typography": "13px, color: #666, margin-bottom: 12px", "hierarchy_level": "Card H2"}}, "team_member_entries": {"entry_structure": {"member_name": {"typography": "font-weight: 500, margin-bottom: 4px", "hierarchy_level": "Member H3", "examples": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, "member_role": {"typography": "13px, color: #666", "hierarchy_level": "Member role", "examples": ["Lead Developer", "Designer", "Project Manager", "Your Manager"]}, "availability_bar": {"visual_element": "Progress bar with color coding", "typography": "Background colors indicate availability status"}, "status_description": {"typography": "12px, colored by status", "hierarchy_level": "Status text", "color_mapping": {"available": "#4CAF50", "conflict": "#F44336", "future_conflict": "#FF9800"}, "examples": ["Jan 14-16 (3 days) - Annual Leave", "Available all week ✓", "In office all week ✓"]}}}}}, "conflicts_warnings_card": {"card_structure": {"card_header": {"primary_title": {"text": "⚠️ CONFLICTS & WARNINGS", "typography": "14px, font-weight: 600", "hierarchy_level": "Card H1"}}, "high_priority_section": {"section_header": {"text": "🚨 High Priority Conflict:", "typography": "font-weight: 500, color: #d32f2f, margin-bottom: 8px", "hierarchy_level": "Alert H2"}, "conflict_list": {"typography": "14px, margin: 0, padding-left: 20px", "hierarchy_level": "Conflict items", "list_style": "Bulleted list", "items": ["<PERSON> (<PERSON> Dev) overlap 2 days", "Project Alpha deadline Jan 18"]}}, "ai_suggestion_section": {"suggestion_container": {"background": "#E3F2FD", "border": "1px solid #90CAF9", "padding": "12px"}, "suggestion_header": {"text": "💡 AI Suggestion:", "typography": "font-weight: 500, margin-bottom: 8px", "hierarchy_level": "Suggestion H2"}, "recommendation_text": {"text": "Consider Jan 21-23 instead:", "typography": "14px, margin-bottom: 8px", "hierarchy_level": "Recommendation body"}, "benefits_list": {"typography": "13px, margin: 0 0 12px 0, padding-left: 20px", "hierarchy_level": "Benefits items", "items": ["No team conflicts", "Project Alpha completed", "95% approval probability"]}, "action_buttons": {"button_group": "display: flex, gap: 8px", "primary_button": {"text": "Apply Suggestion", "typography": "13px, background: #4A90E2, color: white", "hierarchy_level": "Primary CTA"}, "secondary_button": {"text": "Keep Current", "typography": "13px, background: white, border: 1px solid #ddd", "hierarchy_level": "Secondary CTA"}}}}}, "recent_patterns_card": {"card_structure": {"card_header": {"primary_title": {"text": "📋 RECENT PATTERNS", "typography": "14px, font-weight: 600", "hierarchy_level": "Card H1"}, "view_all_link": {"text": "See All →", "typography": "12px, color: #4A90E2, text-decoration: none", "hierarchy_level": "Card action link"}}, "personal_history_section": {"section_header": {"text": "Your Leave History:", "typography": "font-weight: 500, margin-bottom: 8px", "hierarchy_level": "Section H2"}, "pattern_list": {"typography": "13px, color: #666, margin: 0, padding-left: 20px", "hierarchy_level": "Pattern items", "items": ["Usually take Fri-Mon (60%)", "Average duration: 3.5 days", "Last leave: Nov 23-24 (2 months ago)"]}}, "team_patterns_section": {"section_header": {"text": "Team Patterns:", "typography": "font-weight: 500, margin-bottom: 8px", "hierarchy_level": "Section H2"}, "truncated_content": {"typography": "13px, color: #666", "hierarchy_level": "Pattern items", "note": "Content appears to be cut off in HTML"}}}}}}, "level_3_related_information": {"hierarchy_position": "Reference material - collapsed by default", "cognitive_load": "Low - optional viewing", "display_location": "Side panel collapsible sections", "textual_hierarchy": {"expandable_headers": {"collapsed_state": {"typography": "14px, font-weight: 500", "expand_indicator": "▶", "hierarchy_level": "Section H1"}, "expanded_state": {"typography": "14px, font-weight: 500", "collapse_indicator": "▼", "hierarchy_level": "Section H1"}}, "content_hierarchy": {"subsection_headers": {"typography": "13px, font-weight: 500", "hierarchy_level": "Subsection H2", "margin": "16px 0 8px 0"}, "policy_text": {"typography": "12px, line-height: 1.4", "hierarchy_level": "Policy body", "color": "#444"}, "list_items": {"typography": "12px, padding-left: 16px", "hierarchy_level": "Policy details", "list_style": "Bulleted or numbered"}}}, "expandable_sections": [{"section_id": "leave_policy_reference", "header": "📚 Leave Policy Reference", "default_state": "collapsed", "content_preview": "View detailed policy including special cases..."}, {"section_id": "request_best_practices", "header": "💡 Request Best Practices", "default_state": "collapsed", "content_preview": "Tips for successful leave planning..."}, {"section_id": "faq_section", "header": "❓ Frequently Asked Questions", "default_state": "collapsed", "content_preview": "Common questions and answers..."}]}, "level_4_historical_analytical": {"hierarchy_position": "Deep insights - tab navigation", "cognitive_load": "Variable - data intensive", "display_location": "Side panel analytics tab", "textual_hierarchy": {"tab_navigation": {"typography": "13px, font-weight: 500", "hierarchy_level": "Tab labels", "active_state": "Highlighted/underlined", "inactive_state": "Muted colors"}, "dashboard_headers": {"main_headers": {"typography": "16px, font-weight: 600", "hierarchy_level": "Dashboard H1", "margin_bottom": "20px"}, "chart_titles": {"typography": "14px, font-weight: 500", "hierarchy_level": "Chart H2", "margin_bottom": "12px"}, "metric_labels": {"typography": "12px, font-weight: 500", "hierarchy_level": "Metric labels", "color": "#666"}}, "data_visualization_text": {"axis_labels": {"typography": "10px, color: #888", "hierarchy_level": "Chart annotations"}, "data_points": {"typography": "11px, font-weight: 500", "hierarchy_level": "Chart data", "context_dependent": "Color coded by value"}, "trend_descriptions": {"typography": "12px, color: #666", "hierarchy_level": "Analysis text", "style": "Descriptive paragraphs"}}}, "analytics_tabs": [{"tab_id": "personal_analytics", "label": "Personal Analytics", "content_type": "Individual patterns and trends"}, {"tab_id": "team_analytics", "label": "Team Analytics", "content_type": "Team patterns and availability"}, {"tab_id": "organizational_insights", "label": "Org Insights", "content_type": "Company-wide benchmarks"}]}, "level_5_system_meta": {"hierarchy_position": "Technical details - hidden by default", "cognitive_load": "Minimal - rarely accessed", "access_frequency": "<5% of interactions", "display_location": "Hidden/advanced mode", "textual_hierarchy": {"technical_headers": {"typography": "12px, font-weight: 600, font-family: monospace", "hierarchy_level": "Technical section H1", "color": "#333"}, "system_data": {"field_labels": {"typography": "11px, font-weight: 500, font-family: monospace", "hierarchy_level": "Technical field labels", "color": "#666"}, "field_values": {"typography": "11px, font-family: monospace", "hierarchy_level": "Technical data", "color": "#333"}, "timestamps": {"typography": "10px, font-family: monospace", "hierarchy_level": "Technical timestamps", "color": "#888"}}, "debug_information": {"log_entries": {"typography": "10px, font-family: monospace, line-height: 1.2", "hierarchy_level": "Log text", "color": "#444", "background": "#f8f8f8"}, "error_messages": {"typography": "11px, font-family: monospace", "hierarchy_level": "Error text", "color": "#d32f2f"}}}, "access_controls": {"visibility": "hidden_by_default", "access_methods": ["Advanced toggle", "Debug mode", "Support request"], "user_permissions": "Limited based on role"}}, "cross_level_interactions": {"elevation_rules": {"description": "How information moves between levels based on context", "triggers": [{"from": "Level 3", "to": "Level 2", "trigger": "Policy violation detected", "example": "Policy reference becomes critical warning"}, {"from": "Level 2", "to": "Level 1", "trigger": "Blocking validation error", "example": "Context warning becomes form error"}]}, "textual_consistency": {"typography_scale": {"h1_equivalent": "16-18px, font-weight: 600", "h2_equivalent": "14px, font-weight: 500-600", "h3_equivalent": "13px, font-weight: 500", "body_text": "12-14px, normal weight", "metadata": "10-12px, color: #666-#888", "technical": "10-11px, monospace font"}, "color_hierarchy": {"primary_text": "#333", "secondary_text": "#666", "metadata_text": "#888", "success_text": "#4CAF50", "warning_text": "#FF9800", "error_text": "#F44336", "info_text": "#4A90E2"}}}, "implementation_notes": {"responsive_typography": {"desktop": "Base font sizes as specified", "tablet": "Scale down 10-15%", "mobile": "Scale down 20%, increase line height"}, "accessibility_considerations": {"contrast_ratios": "WCAG AA compliant", "font_scaling": "Supports browser zoom up to 200%", "screen_readers": "Proper heading hierarchy maintained"}, "performance_considerations": {"progressive_loading": "Higher levels load first", "text_rendering": "Optimized font loading", "content_streaming": "Long text streams progressively"}}}}