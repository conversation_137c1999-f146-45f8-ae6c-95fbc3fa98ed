{"lead_creation_lo2_information_hierarchy": {"version": "2.0", "description": "LO 2: Lead Qualification & Opportunity Development - Deep qualification with enhanced intelligence", "implementation_date": "2025-01-15", "learning_objective": {"title": "Lead Qualification & Opportunity Development", "description": "Comprehensive lead qualification with detailed opportunity assessment and strategic next steps planning", "primary_goals": ["Conduct thorough BANT/MEDDIC qualification", "Size and scope the opportunity accurately", "Identify decision-making process and stakeholders", "Develop strategic engagement plan with AI insights"], "success_metrics": ["Qualification completion rate >90%", "Opportunity accuracy score >85%", "Follow-up activity completion rate >80%", "Lead-to-opportunity conversion rate >70%"]}, "user_context": {"current_user": "<PERSON>", "role": "Sales Representative", "territory": "West Coast Enterprise", "session_id": "SES-20250115-091530-LO2", "request_timestamp": "2025-01-15T09:22:15Z", "workflow_stage": "detailed_qualification", "previous_stage": "initial_lead_capture", "inherited_context": {"company_name": "TechCorp Inc.", "initial_contact": "To be specified", "lead_score": 78, "product_interest": "Enterprise Solution", "lead_source": "Inbound Inquiry"}, "current_pipeline": {"active_leads": 23, "pipeline_value": "$2.4M", "monthly_quota": "$500K"}}, "spatial_organization": {"layout_structure": {"chat_area": {"width_percentage": "60%", "primary_purpose": "Detailed qualification form with AI assistance", "information_levels": ["Level 0 (hidden)", "Level 1 (primary)", "Level 2 (critical)"]}, "side_panel": {"width_percentage": "40%", "primary_purpose": "Enhanced intelligence and recommendations", "information_levels": ["Level 2 (extended)", "Level 3 (reference)", "Level 4 (analytics)"], "tab_structure": ["Opportunity Intelligence", "Stakeholder Mapping", "Competitive Strategy", "Next Steps Planning"]}}}, "level_0_intent_context": {"hierarchy_position": "Foundation layer - invisible to user", "processing_location": "Background system", "cognitive_load": "Zero", "processing_time": "<150ms", "intent_recognition": {"user_progression": "Advancing from LO1 to LO2 qualification", "system_interpretation": {"parsed_intent": "LEAD_QUALIFICATION_DETAILED", "confidence_score": 0.95, "context_inheritance": {"company_data": "Fully enriched from LO1", "initial_contact_info": "Partial - needs completion", "opportunity_context": "Basic parameters established", "workflow_stage": "lo2_detailed_qualification"}}, "enhanced_context_preparation": {"opportunity_intelligence": "Enhanced deal sizing based on LO1 profile", "stakeholder_mapping": "Initiated based on company size and structure", "competitive_analysis": "Deepened based on industry and company profile", "system_decisions": ["Load LO2 form", "Enhance scoring model", "Prepare stakeholder templates", "Generate opportunity insights"]}}, "enhanced_intelligence_processing": {"opportunity_sizing": {"refined_estimate": "$125,000", "confidence_range": "$85K - $165K", "sizing_factors": ["Company size", "Product tier", "Implementation complexity"], "benchmark_comparison": "Similar companies: $90K-$180K range"}, "stakeholder_prediction": {"likely_stakeholders": ["CTO", "VP Engineering", "CISO", "Procurement"], "decision_maker_profile": "Technical executive with budget authority", "influence_network": "Engineering-driven organization", "buying_committee_size": "4-6 people estimated"}, "competitive_intelligence": {"active_competitors": 2, "competitive_risk": "Medium-Low", "differentiation_opportunities": ["Integration capabilities", "Implementation speed", "Support quality"], "battle_cards_prepared": ["vs CompetitorA", "vs CompetitorB"]}, "engagement_strategy": {"recommended_approach": "Technical-first with business value reinforcement", "optimal_timeline": "4-5 month sales cycle predicted", "key_milestones": ["Technical evaluation", "Security review", "Budget approval", "Legal review"], "risk_factors": ["Budget timing", "Competitive evaluation", "Change management"]}}}, "level_1_primary_actions": {"hierarchy_position": "Essential interaction - detailed form", "display_location": "Chat area comprehensive form", "cognitive_load": "High - detailed qualification", "visibility": "Structured sections with progress indication", "textual_hierarchy": {"primary_heading": {"text": "LEAD QUALIFICATION & OPPORTUNITY DEVELOPMENT", "text_style": {"fontsize": 18, "fontweight": "w600", "color": "black", "fontfamily": "TiemposText"}, "purpose": "LO2 identification", "hierarchy_level": "H1", "stage_indicator": "Step 2 of 2", "progress_bar": "Visual progress indicator"}, "section_headers": {"contact_stakeholders": {"text": "👥 Contact & Stakeholder Details", "text_style": {"fontsize": 16, "fontweight": "w500", "color": "black"}, "hierarchy_level": "Section H2", "description": "Key contacts and decision-making structure"}, "opportunity_qualification": {"text": "💼 Opportunity Qualification (BANT/MEDDIC)", "text_style": {"fontsize": 16, "fontweight": "w500", "color": "black"}, "hierarchy_level": "Section H2", "description": "Budget, Authority, Need, Timeline assessment"}, "technical_requirements": {"text": "⚙️ Technical & Business Requirements", "text_style": {"fontsize": 16, "fontweight": "w500", "color": "black"}, "hierarchy_level": "Section H2", "description": "Solution requirements and evaluation criteria"}, "next_steps_planning": {"text": "📅 Next Steps & Follow-up Planning", "text_style": {"fontsize": 16, "fontweight": "w500", "color": "black"}, "hierarchy_level": "Section H2", "description": "Immediate actions and engagement strategy"}}, "qualification_framework_indicators": {"bant_progress": "Visual checklist showing BANT completion", "meddic_progress": "Progress bars for MEDDIC criteria", "completion_percentage": "Overall qualification percentage"}, "action_buttons": {"primary_action": {"text": "Create Qualified Lead", "text_style": {"fontsize": 14, "fontweight": "w500", "color": "white"}, "visual_hierarchy": "Highest prominence, green background", "purpose": "Complete lead creation process"}, "secondary_actions": {"save_continue": "Save & Continue Later", "create_opportunity": "Create Opportunity", "schedule_discovery": "Schedule Discovery Call", "visual_hierarchy": "Lower prominence, various styling"}}}, "detailed_qualification_inputs": {"contact_stakeholder_details": {"primary_contact_details": {"contact_name": {"field_id": "primary_contact_name", "value": "Inherited from LO1 or manual entry", "validation": "Required field", "enrichment": "LinkedIn profile suggestions"}, "contact_title": {"field_id": "primary_contact_title", "placeholder": "Chief Technology Officer", "suggestions": "Role-based on company profile", "influence_indicator": "Auto-calculated influence score"}, "contact_role_in_decision": {"field_id": "decision_role", "options": ["Decision Maker", "Influencer", "End User", "Gatekeeper", "Champion"], "multiple_select": true, "helper_text": "What role do they play in the buying process?"}, "contact_pain_points": {"field_id": "primary_pain_points", "placeholder": "What specific challenges are they facing?", "max_length": 500, "ai_suggestions": "Based on role and industry"}}, "additional_stakeholders": {"stakeholder_mapping": {"field_type": "dynamic_list", "add_stakeholder_button": "➕ Add Stakeholder", "stakeholder_template": {"name": "Stakeholder name", "title": "Job title", "department": "Department/Function", "role_in_decision": "Decision role", "influence_level": "High/Medium/Low", "relationship_status": "Identified/Contacted/Engaged"}, "smart_suggestions": "AI-suggested stakeholders based on company profile"}, "buying_committee_size": {"field_id": "committee_size", "options": ["1-2 people", "3-5 people", "6-10 people", "10+ people", "Unknown"], "helper_text": "Estimated decision-making committee size"}, "decision_process": {"field_id": "decision_process", "placeholder": "Describe their decision-making process...", "max_length": 750, "helper_text": "How do they typically evaluate and purchase solutions?"}}}, "bant_meddic_qualification": {"budget_qualification": {"budget_allocated": {"field_id": "budget_status", "options": ["Budget Approved", "Budget Identified", "Budget in Planning", "No Budget Yet", "Unknown"], "required": true}, "budget_range": {"field_id": "budget_range", "options": ["<$50K", "$50K-$100K", "$100K-$200K", "$200K-$500K", "$500K+", "Undisclosed"], "smart_default": "$100K-$200K (based on company profile)"}, "budget_timeline": {"field_id": "budget_timing", "options": ["Current Quarter", "Next Quarter", "Next Fiscal Year", "To Be Determined"], "helper_text": "When is budget available for this initiative?"}, "budget_holder": {"field_id": "budget_owner", "placeholder": "Who controls the budget for this purchase?", "suggestions": "Based on stakeholder mapping"}}, "authority_qualification": {"decision_maker_identified": {"field_id": "decision_maker_known", "options": ["Yes - Identified", "Yes - Engaged", "Partially Identified", "Unknown"], "required": true}, "approval_process": {"field_id": "approval_steps", "placeholder": "What approval steps are required?", "examples": ["Technical evaluation → Budget approval → Legal review → Final sign-off"]}, "procurement_involvement": {"field_id": "procurement_required", "options": ["Required", "Likely Required", "Not Required", "Unknown"], "helper_text": "Will procurement team be involved?"}}, "need_qualification": {"business_pain": {"field_id": "business_pain", "placeholder": "What business problems are they trying to solve?", "max_length": 750, "required": true, "ai_prompts": "Industry-specific pain point suggestions"}, "current_solution": {"field_id": "current_state", "placeholder": "How are they handling this today?", "max_length": 500, "helper_text": "Current tools, processes, or workarounds"}, "success_criteria": {"field_id": "success_metrics", "placeholder": "How will they measure success?", "max_length": 500, "examples": ["Reduced processing time", "Improved accuracy", "Cost savings"]}, "urgency_drivers": {"field_id": "urgency_factors", "options": ["Compliance deadline", "Growth demands", "Cost pressure", "Competitive threat", "Executive mandate", "Other"], "multiple_select": true}}, "timeline_qualification": {"decision_timeline": {"field_id": "decision_timeframe", "options": ["< 1 month", "1-3 months", "3-6 months", "6-12 months", "> 12 months"], "required": true}, "implementation_timeline": {"field_id": "implementation_timeframe", "options": ["< 1 month", "1-3 months", "3-6 months", "6-12 months", "> 12 months"], "helper_text": "Expected implementation duration"}, "key_milestones": {"field_id": "project_milestones", "placeholder": "Key dates or milestones driving the timeline...", "examples": ["Q1 budget approval", "Summer implementation", "End-of-year compliance"]}}}, "technical_business_requirements": {"solution_requirements": {"primary_use_cases": {"field_id": "use_cases", "placeholder": "Primary use cases for our solution...", "max_length": 750, "ai_suggestions": "Based on product interest and industry"}, "integration_needs": {"field_id": "integrations", "placeholder": "Required integrations with existing systems...", "tech_stack_reference": "Reference LO1 tech stack data", "suggestions": "Based on discovered tech stack"}, "user_count": {"field_id": "user_volume", "options": ["1-10 users", "11-50 users", "51-200 users", "201-1000 users", "1000+ users"], "helper_text": "Expected number of solution users"}, "technical_constraints": {"field_id": "constraints", "placeholder": "Any technical constraints or requirements...", "examples": ["On-premises only", "SOC2 compliance", "Single sign-on required"]}}, "evaluation_criteria": {"decision_criteria": {"field_id": "evaluation_factors", "placeholder": "How will they evaluate solutions?", "max_length": 500, "examples": ["Feature functionality", "Price", "Implementation ease", "Support quality"]}, "evaluation_process": {"field_id": "evaluation_steps", "placeholder": "What's their evaluation process?", "examples": ["Demo → Pilot → Security review → Reference calls → Decision"]}, "competitor_evaluation": {"field_id": "other_vendors", "placeholder": "Other solutions being considered...", "competitive_intelligence": "Auto-suggest known competitors"}}}, "next_steps_planning": {"immediate_actions": {"next_meeting": {"field_id": "next_meeting_type", "options": ["Discovery Call", "Demo/Presentation", "Technical Deep Dive", "Stakeholder Introduction", "Proposal Review"], "required": true}, "meeting_timeline": {"field_id": "next_meeting_when", "options": ["This week", "Next week", "Within 2 weeks", "Within month", "TBD"], "calendar_integration": "Link to calendar booking"}, "meeting_attendees": {"field_id": "meeting_participants", "placeholder": "Who should attend the next meeting?", "stakeholder_suggestions": "Based on stakeholder mapping"}}, "follow_up_strategy": {"information_needed": {"field_id": "info_requests", "placeholder": "What information do you need to gather?", "examples": ["Technical requirements", "Budget details", "Timeline confirmation"]}, "materials_to_send": {"field_id": "follow_up_materials", "options": ["Product overview", "Case studies", "ROI calculator", "Security documentation", "Implementation plan"], "multiple_select": true, "smart_recommendations": "Based on qualification responses"}, "internal_actions": {"field_id": "internal_next_steps", "placeholder": "Internal actions and preparation needed...", "examples": ["Prepare demo environment", "Get pricing approval", "Engage technical support"]}}, "opportunity_notes": {"qualification_summary": {"field_id": "qualification_notes", "placeholder": "Key qualification insights and next steps...", "max_length": 1000, "ai_summary": "AI-generated summary based on responses"}, "risk_factors": {"field_id": "potential_risks", "placeholder": "Potential risks or challenges...", "ai_suggestions": "Risk assessment based on responses"}, "win_strategies": {"field_id": "win_strategy", "placeholder": "Key strategies to win this opportunity...", "ai_recommendations": "Based on competitive analysis and qualification"}}}}}, "level_2_contextual_information": {"hierarchy_position": "Enhanced decision support - prominent placement", "cognitive_load": "Medium-High - actionable insights", "placement_strategy": "AI insights in chat, detailed intelligence in tabs", "chat_area_critical": {"textual_hierarchy": {"ai_coaching_alerts": {"text_style": {"fontsize": 14, "fontweight": "w500", "color": "blue"}, "styling": "Background: #E3F2FD, border: 1px solid #90CAF9", "hierarchy_level": "AI Coach H3", "examples": ["💡 Similar companies typically have 4-6 stakeholders", "⚠️ Competitor activity detected - consider expedited timeline", "✅ Strong BANT fit - recommend aggressive pursuit", "📊 Budget range aligns with our pricing - good opportunity"]}, "qualification_progress": {"bant_completion": "Visual progress bars for each BANT element", "meddic_scoring": "Real-time MEDDIC qualification score", "overall_readiness": "Opportunity readiness percentage"}}, "ai_qualification_coaching": {"title": "🤖 AI Qualification Coach", "title_style": {"fontsize": 16, "fontweight": "w600", "color": "black"}, "coaching_insights": ["Based on company size, expect 5-7 stakeholders in decision process", "SaaS companies this size typically budget $100K-$200K for enterprise tools", "Technical evaluation phase usually takes 2-3 weeks for this profile", "Recent funding indicates budget availability - prioritize timeline discussion"], "question_suggestions": ["Who else would be involved in evaluating a solution like this?", "What's driving the timing for this initiative?", "How are you handling [specific use case] today?", "What would success look like for your team?"]}, "opportunity_health_check": {"title": "📈 Opportunity Health", "title_style": {"fontsize": 16, "fontweight": "w600", "color": "black"}, "health_score": "82/100 - Strong Opportunity", "health_factors": ["✅ Budget: Likely allocated (based on funding)", "⚠️ Authority: Partial - need to identify final decision maker", "✅ Need: Strong pain points identified", "✅ Timeline: Reasonable 3-6 month window"], "improvement_recommendations": ["Identify CFO or budget holder", "Map complete stakeholder network", "Quantify business impact/ROI"]}}}, "level_3_related_information": {"hierarchy_position": "Deep reference material - expandable sections", "cognitive_load": "Low-Medium - contextual reference", "display_location": "Collapsible sections within tabs", "expandable_sections": [{"section_id": "qualification_frameworks", "parent_tab": "opportunity_intelligence", "header": "📋 Qualification Framework Deep Dive", "header_style": {"fontsize": 14, "fontweight": "w600", "color": "black"}, "default_state": "collapsed", "content": {"bant_detailed": {"budget_questions": ["Is there budget specifically allocated for this type of solution?", "What's the budget range you're working within?", "When does your budget year start/end?", "Who needs to approve expenditures in this range?"], "authority_questions": ["Who else would be involved in making this decision?", "What's your typical approval process for software purchases?", "Do you need board or executive approval?", "Who has the final sign-off authority?"], "need_questions": ["What's prompting you to look for a solution now?", "How are you handling this today?", "What would happen if you don't solve this?", "How will you measure success?"], "timeline_questions": ["When do you need this implemented?", "What's driving that timeline?", "Are there any key dates or deadlines?", "How long do you think evaluation will take?"]}, "meddic_detailed": {"metrics_discovery": "Specific KPIs and measurement criteria", "economic_buyer_identification": "Budget holder and financial authority", "decision_criteria_mapping": "Evaluation factors and weighting", "decision_process_steps": "Buying process and approval workflow", "pain_identification": "Business and technical pain points", "champion_development": "Internal advocate identification and cultivation"}}}, {"section_id": "industry_playbooks", "parent_tab": "competitive_strategy", "header": "📖 SaaS Industry Sales Playbook", "header_style": {"fontsize": 14, "fontweight": "w600", "color": "black"}, "default_state": "collapsed", "content": {"saas_buying_patterns": {"typical_timeline": "3-6 months for enterprise sales", "common_stakeholders": ["CTO", "VP Eng", "DevOps", "Security", "Finance"], "decision_factors": ["Technical fit", "Scalability", "Security", "Support", "ROI"], "evaluation_process": ["Demo → Pilot → Security review → Business case → Contract"]}, "common_objections": {"technical_objections": ["Integration complexity", "Performance concerns", "Security requirements"], "business_objections": ["Budget constraints", "Timing issues", "Change management"], "competitive_objections": ["Incumbent relationships", "Feature comparisons", "Price sensitivity"]}, "success_strategies": {"technical_validation": "Hands-on proof of concept", "business_case": "Clear ROI and efficiency gains", "risk_mitigation": "References and pilot programs", "relationship_building": "Multi-stakeholder engagement"}}}, {"section_id": "competitive_battlecards", "parent_tab": "competitive_strategy", "header": "⚔️ Detailed Competitive Battlecards", "header_style": {"fontsize": 14, "fontweight": "w600", "color": "black"}, "default_state": "collapsed", "content": {"competitor_a_profile": {"strengths": ["Market presence", "Feature breadth", "Brand recognition"], "weaknesses": ["Implementation complexity", "Support quality", "Pricing model"], "positioning_against": "Faster, simpler, better supported alternative", "trap_questions": ["How important is rapid implementation?", "What's your experience with support?"], "proof_points": ["Customer testimonials", "Implementation timelines", "Support ratings"]}, "competitor_b_profile": {"strengths": ["Lower price point", "Simple interface", "Quick setup"], "weaknesses": ["Scalability limits", "Integration gaps", "Feature limitations"], "positioning_against": "Enterprise-grade solution for growth", "trap_questions": ["What are your growth plans?", "How important are integrations?"], "proof_points": ["Scalability demos", "Integration library", "Enterprise features"]}}}, {"section_id": "discovery_questions", "parent_tab": "next_steps_planning", "header": "❓ Discovery Question Bank", "header_style": {"fontsize": 14, "fontweight": "w600", "color": "black"}, "default_state": "collapsed", "content": {"opening_questions": ["What prompted you to start looking for a solution like this?", "How are you handling [specific process] today?", "What would success look like for this project?", "Who else is involved in this initiative?"], "pain_discovery": ["What's the biggest challenge you're facing with your current approach?", "How is this impacting your team's productivity?", "What would happen if you don't solve this in the next 6 months?", "Where do you see the biggest opportunity for improvement?"], "technical_discovery": ["What systems would this need to integrate with?", "What are your security and compliance requirements?", "How many users would be on the platform?", "What's your infrastructure setup like?"], "business_discovery": ["How do you typically evaluate new tools?", "What's your budget planning process like?", "When would you ideally want this implemented?", "How do you measure ROI on technology investments?"]}}]}, "level_4_historical_analytical": {"hierarchy_position": "Advanced analytics - separate view", "cognitive_load": "High - data analysis", "display_location": "Analytics dashboard modal", "access_trigger": "Advanced Analytics button", "analytics_sections": {"opportunity_analytics": {"title": "Opportunity Intelligence Dashboard", "title_style": {"fontsize": 18, "fontweight": "w600", "color": "black"}, "deal_progression_analysis": {"similar_deals_progression": "Timeline analysis of similar opportunities", "stage_conversion_rates": "Conversion rates at each sales stage", "velocity_metrics": "Time spent in each stage", "bottleneck_identification": "Common sticking points and delays"}, "predictive_modeling": {"close_probability_model": "AI-driven probability scoring", "deal_size_prediction": "Revenue forecasting with confidence intervals", "timeline_prediction": "Expected close date modeling", "risk_factor_analysis": "Probability impact of various risk factors"}}, "stakeholder_analytics": {"title": "Stakeholder Network Analysis", "title_style": {"fontsize": 18, "fontweight": "w600", "color": "black"}, "influence_mapping": "Stakeholder influence and relationship networks", "engagement_patterns": "Communication and meeting patterns", "decision_maker_profiles": "Historical analysis of decision makers in similar companies", "champion_success_factors": "Characteristics of successful champions"}, "competitive_analytics": {"title": "Competitive Intelligence Dashboard", "title_style": {"fontsize": 18, "fontweight": "w600", "color": "black"}, "win_loss_analysis": "Historical performance against competitors", "battle_outcome_patterns": "Factors that determine competitive wins", "market_share_trends": "Competitive positioning over time", "differentiation_effectiveness": "Success rate of various positioning strategies"}, "territory_performance": {"title": "Territory & Rep Performance", "title_style": {"fontsize": 18, "fontweight": "w600", "color": "black"}, "jennifer_performance_trends": "Personal performance metrics and trends", "territory_benchmarks": "Comparison to territory and company averages", "opportunity_pipeline": "Pipeline health and progression analysis", "quota_attainment_projection": "Forecast to quota based on current pipeline"}}}, "level_5_system_meta": {"hierarchy_position": "System information - debug mode", "cognitive_load": "Minimal - technical details", "access_frequency": "<1% of interactions", "display_location": "Hidden technical panel", "technical_information": {"session_details": {"session_id": "SES-20250115-091530-LO2-7483921", "workflow_stage": "lo2_detailed_qualification", "previous_stage": "lo1_basic_capture", "form_version": "v2.1.0", "qualification_framework": "BANT + MEDDIC hybrid", "ai_model_version": "qualification_assistant_v1.4"}, "data_processing": {"opportunity_sizing_model": "deal_size_predictor_v2.3", "stakeholder_prediction": "stakeholder_mapper_v1.8", "competitive_analysis": "competitive_intel_v1.5", "timeline_prediction": "sales_cycle_predictor_v2.1"}, "integration_status": {"crm_sync": "Real-time Salesforce integration active", "calendar_integration": "Google Calendar connected", "email_tracking": "Email engagement tracking enabled", "linkedin_sales_nav": "Profile enrichment active"}}}, "completion_and_progression": {"completion_requirements": {"minimum_qualification": ["At least 3 of 4 BANT elements confirmed", "Primary stakeholder identified", "Basic pain points documented", "Next meeting scheduled"], "ideal_qualification": ["Complete BANT qualification", "Stakeholder map with 3+ contacts", "Detailed pain points and success criteria", "Budget range confirmed", "Timeline and decision process understood"]}, "output_generation": {"lead_record_creation": "Complete CRM lead record with all qualification data", "opportunity_creation": "Convert to opportunity if highly qualified", "task_generation": "Automatic follow-up task creation", "email_templates": "Personalized follow-up email suggestions", "meeting_prep": "Discovery call preparation materials"}, "success_metrics": {"qualification_score": "Overall qualification completeness percentage", "opportunity_grade": "A/B/C grading based on qualification quality", "next_step_clarity": "Clear action items and timeline defined", "stakeholder_engagement": "Multiple touchpoints identified and planned"}}, "hardcoded_texts": {"collection_data": {"name": {"text": "Solution Widgets", "text_style": {"fontsize": 13, "fontweight": "w400", "color": "black"}}}, "option_buttons": {"verify_company": {"text": "Verify Company", "text_style": {"fontsize": 14, "fontweight": "w400", "color": "black"}}, "check_territory": {"text": "Check Territory", "text_style": {"fontsize": 14, "fontweight": "w400", "color": "black"}}, "ai_enrichment": {"text": "AI Enrichment", "text_style": {"fontsize": 14, "fontweight": "w400", "color": "black"}}}, "action_buttons": {"continue_details": {"text": "Continue to Details →", "text_style": {"fontsize": 14, "fontweight": "w500", "color": "white"}}, "save_draft": {"text": "Save as Draft", "text_style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}, "cancel": {"text": "Cancel", "text_style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}}, "progress_indicators": {"lo_title": {"text": "LO TITLE", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "grey"}}, "step_progress": {"text_template": "Step {current}/{total}", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "grey"}}}, "lo_titles": {"lo1": {"text": "Lead Basics & Discovery", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "grey"}}, "lo2": {"text": "Lead Qualification & Opportunity Development", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "grey"}}}, "chat_messages": {"default_message": {"text": "I need to create a lead for TechCorp - they inquired about our enterprise solution", "text_style": {"fontsize": 14, "fontweight": "w400", "color": "black"}}}, "recommendation_section": {"title": {"text": "Recommendation in Context", "text_style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}}, "form_sections": {"company_information": {"title": {"text": "Company Information", "text_style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}, "fields": {"company_name_placeholder": "Company name", "industry_default": "Software/SaaS", "company_size_default": "500-1000 employees", "location_default": "San Francisco, CA", "enrichment_indicator": "✓ Company found & enriched", "territory_indicator": "✓ In your territory"}}, "primary_contact": {"title": {"text": "Primary Contact", "text_style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}, "fields": {"contact_name_placeholder": "Primary contact name", "email_placeholder": "<EMAIL>", "job_title_options": ["CTO", "VP Engineering", "Director IT", "CISO"], "phone_placeholder": "+****************"}}, "initial_opportunity": {"title": {"text": "Initial Opportunity", "text_style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}, "fields": {"lead_source_default": "Inbound Inquiry", "product_interest_default": "Enterprise Solution", "urgency_level_default": "Medium-term (3-6 months)", "inquiry_summary_placeholder": "Brief summary of their inquiry or interest..."}}}, "ui_controls": {"switch_labels": {"nsl": "NSL", "normal": "Normal"}}}, "extra_details": {"tabs": [{"id": "opportunity_intel", "label": "Opportunity Intel", "iconPath": "assets/images/my_business/solutions/recommendation_ai.svg", "text_style": {"default": {"fontsize": 12, "fontweight": "w500", "color": "black"}, "selected": {"fontsize": 12, "fontweight": "w600", "color": "black"}}, "content": {"deal_sizing_intelligence": {"title": "Deal Sizing Intelligence", "title_style": {"fontsize": 14, "fontweight": "w600", "color": "black"}, "predicted_deal_size": {"value": "$125,000", "confidence": "78% confidence", "range": "Range: $85K - $165K", "value_style": {"fontsize": 24, "fontweight": "w600", "color": "blue"}, "confidence_style": {"fontsize": 12, "fontweight": "w500", "color": "grey"}, "range_style": {"fontsize": 12, "fontweight": "w400", "color": "grey"}}, "sizing_factors": [{"label": "Company size factor:", "value": "Enterprise tier", "label_style": {"fontsize": 12, "fontweight": "w500", "color": "grey"}, "value_style": {"fontsize": 12, "fontweight": "w500", "color": "black"}}, {"label": "Product interest:", "value": "Full platform", "label_style": {"fontsize": 12, "fontweight": "w500", "color": "grey"}, "value_style": {"fontsize": 12, "fontweight": "w500", "color": "black"}}, {"label": "Implementation:", "value": "Medium complexity", "label_style": {"fontsize": 12, "fontweight": "w500", "color": "grey"}, "value_style": {"fontsize": 12, "fontweight": "w500", "color": "black"}}]}, "sales_cycle_prediction": {"title": "Sales Cycle Prediction", "title_style": {"fontsize": 14, "fontweight": "w600", "color": "black"}, "predicted_timeline": {"value": "4.1 months", "confidence": "84% confidence", "value_style": {"fontsize": 24, "fontweight": "w600", "color": "blue"}, "confidence_style": {"fontsize": 12, "fontweight": "w500", "color": "grey"}}, "phase_breakdown": [{"phase": "Discovery & Qualification:", "duration": "2-3 weeks", "phase_style": {"fontsize": 12, "fontweight": "w500", "color": "black"}, "duration_style": {"fontsize": 12, "fontweight": "w600", "color": "grey"}}, {"phase": "Technical Evaluation:", "duration": "3-4 weeks", "phase_style": {"fontsize": 12, "fontweight": "w500", "color": "black"}, "duration_style": {"fontsize": 12, "fontweight": "w600", "color": "grey"}}, {"phase": "Business Case & Approval:", "duration": "4-6 weeks", "phase_style": {"fontsize": 12, "fontweight": "w500", "color": "black"}, "duration_style": {"fontsize": 12, "fontweight": "w600", "color": "grey"}}, {"phase": "Contract & Legal:", "duration": "2-3 weeks", "phase_style": {"fontsize": 12, "fontweight": "w500", "color": "black"}, "duration_style": {"fontsize": 12, "fontweight": "w600", "color": "grey"}}]}, "win_probability": {"title": "Win Probability Analysis", "title_style": {"fontsize": 14, "fontweight": "w600", "color": "black"}, "current_probability": {"value": "76%", "label": "Current probability", "value_style": {"fontsize": 24, "fontweight": "w600", "color": "green"}, "label_style": {"fontsize": 12, "fontweight": "w500", "color": "grey"}}, "probability_factors": [{"factor": "Inbound lead:", "impact": "+15%", "factor_style": {"fontsize": 12, "fontweight": "w400", "color": "black"}, "impact_style": {"fontsize": 12, "fontweight": "w600", "color": "green"}}, {"factor": "Strong company fit:", "impact": "+12%", "factor_style": {"fontsize": 12, "fontweight": "w400", "color": "black"}, "impact_style": {"fontsize": 12, "fontweight": "w600", "color": "green"}}, {"factor": "Territory alignment:", "impact": "+8%", "factor_style": {"fontsize": 12, "fontweight": "w400", "color": "black"}, "impact_style": {"fontsize": 12, "fontweight": "w600", "color": "green"}}]}}}, {"id": "stakeholders", "label": "Stakeholders", "iconPath": "assets/images/my_business/solutions/solution_related.svg", "text_style": {"default": {"fontsize": 12, "fontweight": "w500", "color": "black"}, "selected": {"fontsize": 12, "fontweight": "w600", "color": "black"}}, "content": {"predicted_stakeholders": {"title": "Predicted Stakeholder Network", "title_style": {"fontsize": 14, "fontweight": "w600", "color": "black"}, "stakeholders": [{"role": "Chief Technology Officer", "influence": "High", "concerns": ["Technical fit", "Implementation complexity", "Team adoption"], "strategy": "Technical deep dive, reference calls", "role_style": {"fontsize": 14, "fontweight": "w600", "color": "black"}, "influence_style": {"fontsize": 12, "fontweight": "w500", "color": "orange"}, "concerns_style": {"fontsize": 12, "fontweight": "w400", "color": "grey"}, "strategy_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}, {"role": "VP Engineering", "influence": "High", "concerns": ["Development impact", "Integration effort", "Tool efficiency"], "strategy": "Developer experience demo", "role_style": {"fontsize": 14, "fontweight": "w600", "color": "black"}, "influence_style": {"fontsize": 12, "fontweight": "w500", "color": "orange"}, "concerns_style": {"fontsize": 12, "fontweight": "w400", "color": "grey"}, "strategy_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}, {"role": "CISO/Security Lead", "influence": "Medium-High", "concerns": ["Security compliance", "Data protection", "Access controls"], "strategy": "Security documentation, compliance overview", "role_style": {"fontsize": 14, "fontweight": "w600", "color": "black"}, "influence_style": {"fontsize": 12, "fontweight": "w500", "color": "green"}, "concerns_style": {"fontsize": 12, "fontweight": "w400", "color": "grey"}, "strategy_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}]}, "organizational_insights": {"title": "Organizational Context", "title_style": {"fontsize": 14, "fontweight": "w600", "color": "black"}, "insights": [{"label": "Company structure", "value": "Engineering-driven organization", "label_style": {"fontsize": 12, "fontweight": "w500", "color": "grey"}, "value_style": {"fontsize": 12, "fontweight": "w500", "color": "black"}}, {"label": "Decision style", "value": "Consensus-based with technical validation", "label_style": {"fontsize": 12, "fontweight": "w500", "color": "grey"}, "value_style": {"fontsize": 12, "fontweight": "w500", "color": "black"}}]}}}, {"id": "competition", "label": "Competition", "iconPath": "assets/images/my_business/solutions/solution_contextual.svg", "text_style": {"default": {"fontsize": 12, "fontweight": "w500", "color": "black"}, "selected": {"fontsize": 12, "fontweight": "w600", "color": "black"}}, "content": {"competitive_landscape": {"title": "Competitive Analysis", "title_style": {"fontsize": 14, "fontweight": "w600", "color": "black"}, "competitors": [{"name": "CompetitorA", "position": "Market leader", "threat": "Medium", "activity": "No recent activity detected", "weaknesses": ["Complex implementation", "Poor customer support", "Limited integration"], "advantages": ["Faster deployment", "Superior support", "Better APIs"], "name_style": {"fontsize": 14, "fontweight": "w600", "color": "black"}, "position_style": {"fontsize": 12, "fontweight": "w600", "color": "grey"}, "threat_style": {"fontsize": 12, "fontweight": "w500", "color": "orange"}, "activity_style": {"fontsize": 12, "fontweight": "w400", "color": "grey"}}, {"name": "CompetitorB", "position": "Growing challenger", "threat": "Low-Medium", "activity": "LinkedIn engagement 6 months ago", "weaknesses": ["Feature gaps", "Scaling issues", "Higher cost"], "advantages": ["Feature completeness", "Enterprise scalability", "Competitive pricing"], "name_style": {"fontsize": 14, "fontweight": "w600", "color": "black"}, "position_style": {"fontsize": 12, "fontweight": "w500", "color": "grey"}, "threat_style": {"fontsize": 12, "fontweight": "w500", "color": "green"}, "activity_style": {"fontsize": 12, "fontweight": "w400", "color": "grey"}}]}, "differentiation_strategy": {"title": "Differentiation Strategy", "title_style": {"fontsize": 14, "fontweight": "w600", "color": "black"}, "differentiators": [{"name": "Implementation Speed", "advantage": "2-3 weeks vs 2-3 months", "proof_points": ["Standardized deployment", "Pre-built integrations", "Dedicated support"], "name_style": {"fontsize": 12, "fontweight": "w600", "color": "black"}, "advantage_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}, "proof_style": {"fontsize": 11, "fontweight": "w400", "color": "grey"}}, {"name": "Integration Capabilities", "advantage": "300+ out-of-box integrations", "proof_points": ["API-first architecture", "Webhook support", "Real-time sync"], "name_style": {"fontsize": 12, "fontweight": "w600", "color": "black"}, "advantage_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}, "proof_style": {"fontsize": 11, "fontweight": "w400", "color": "grey"}}]}}}], "content_data": [{"id": "1", "title": "Related", "data": [{"type": "widget12", "title": {"value": "Deal Sizing Intelligence", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}, "subtitle": {"value": "$125,000", "style": {"fontsize": 20, "fontweight": "w600", "color": "blue"}}, "data": ["78% confidence", "Range: $85K - $165K"], "data_style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}, "tertiaryTitle": [{"title": {"value": "Company size factor:", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "Enterprise tier", "style": {"fontsize": 12, "fontweight": "w500", "color": "black"}}}, {"title": {"value": "Product interest:", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "Full platform", "style": {"fontsize": 12, "fontweight": "w500", "color": "black"}}}, {"title": {"value": "Implementation:", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "Medium complexity", "style": {"fontsize": 12, "fontweight": "w500", "color": "black"}}}]}, {"type": "widget12", "title": {"value": "Sales Cycle Prediction", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}, "subtitle": {"value": "4.1 months", "style": {"fontsize": 20, "fontweight": "w600", "color": "blue"}}, "data": ["84% confidence"], "data_style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}, "tertiaryTitle": [{"title": {"value": "Discovery & Qualification:", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "2-3 weeks", "style": {"fontsize": 12, "fontweight": "w500", "color": "black"}}}, {"title": {"value": "Technical Evaluation:", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "3-4 weeks", "style": {"fontsize": 12, "fontweight": "w500", "color": "black"}}}, {"title": {"value": "Business Case & Approval:", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "4-6 weeks", "style": {"fontsize": 12, "fontweight": "w500", "color": "black"}}}, {"title": {"value": "Contract & Legal:", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "2-3 weeks", "style": {"fontsize": 12, "fontweight": "w500", "color": "black"}}}]}, {"type": "widget12", "title": {"value": "Win Probability Analysis", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}, "subtitle": {"value": "76%", "style": {"fontsize": 20, "fontweight": "w600", "color": "green"}}, "data": ["Current probability"], "data_style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}, "tertiaryTitle": [{"title": {"value": "Inbound lead:", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "+15%", "style": {"fontsize": 12, "fontweight": "w500", "color": "black"}}}, {"title": {"value": "Strong company fit:", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "+12%", "style": {"fontsize": 12, "fontweight": "w500", "color": "black"}}}, {"title": {"value": "Territory alignment:", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "+8%", "style": {"fontsize": 12, "fontweight": "w500", "color": "black"}}}]}]}, {"id": "2", "title": "Contextual", "data": [{"type": "widget13", "title": {"value": "Predicted Stakeholder Network", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}, "subtitle": [{"title": {"value": "Chief Technology Officer", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}, "subtitle": {"value": "High Influence", "style": {"fontsize": 12, "fontweight": "w500", "color": "red"}}, "tertiaryTitle": [{"title": {"value": "Concerns:", "style": {"fontsize": 12, "fontweight": "w600", "color": "black"}}, "subtitle": {"value": "Technical fit, Implementation complexity", "style": {"fontsize": 12, "fontweight": "w400", "color": "#757575"}}}, {"title": {"value": "Strategy:", "style": {"fontsize": 12, "fontweight": "w600", "color": "black"}}, "subtitle": {"value": "Technical deep dive, reference calls", "style": {"fontsize": 12, "fontweight": "w400", "color": "#757575"}}}]}, {"title": {"value": "VP Engineering", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}, "subtitle": {"value": "High Influence", "style": {"fontsize": 12, "fontweight": "w500", "color": "red"}}, "tertiaryTitle": [{"title": {"value": "Concerns:", "style": {"fontsize": 12, "fontweight": "w600", "color": "black"}}, "subtitle": {"value": "Development impact, Tool efficiency", "style": {"fontsize": 12, "fontweight": "w400", "color": "#757575"}}}, {"title": {"value": "Strategy:", "style": {"fontsize": 12, "fontweight": "w600", "color": "black"}}, "subtitle": {"value": "Developer experience demo", "style": {"fontsize": 12, "fontweight": "w400", "color": "#757575"}}}]}, {"title": {"value": "CISO/Security Lead", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}, "subtitle": {"value": "Medium-High", "style": {"fontsize": 12, "fontweight": "w500", "color": "orange"}}, "tertiaryTitle": [{"title": {"value": "Concerns:", "style": {"fontsize": 12, "fontweight": "w600", "color": "black"}}, "subtitle": {"value": "Security compliance, Data protection", "style": {"fontsize": 12, "fontweight": "w400", "color": "#757575"}}}, {"title": {"value": "Strategy:", "style": {"fontsize": 12, "fontweight": "w600", "color": "black"}}, "subtitle": {"value": "Security docs, compliance overview", "style": {"fontsize": 12, "fontweight": "w400", "color": "#757575"}}}]}, {"title": {"value": "CFO/Finance", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}, "subtitle": {"value": "High Influence", "style": {"fontsize": 12, "fontweight": "w500", "color": "red"}}, "tertiaryTitle": [{"title": {"value": "Concerns:", "style": {"fontsize": 12, "fontweight": "w600", "color": "black"}}, "subtitle": {"value": "RO<PERSON>, Cost justification", "style": {"fontsize": 12, "fontweight": "w400", "color": "#757575"}}}, {"title": {"value": "Strategy:", "style": {"fontsize": 12, "fontweight": "w600", "color": "black"}}, "subtitle": {"value": "Business case, ROI analysis", "style": {"fontsize": 12, "fontweight": "w400", "color": "#757575"}}}]}]}, {"type": "widget14", "title": {"value": "Organizational Context", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}, "subtitle": [{"title": {"value": "Company Structure", "style": {"fontsize": 12, "fontweight": "w400", "color": "#757575"}}, "subtitle": {"value": "Engineering-driven", "style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}}, {"title": {"value": "Decision Style", "style": {"fontsize": 12, "fontweight": "w400", "color": "#757575"}}, "subtitle": {"value": "Consensus-based", "style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}}], "tertiaryTitle": "Cultural Factors:", "style": {"fontsize": 12, "fontweight": "w600", "color": "black"}, "data": ["Innovation-focused culture", "Data-driven decision making", "Strong technical team autonomy"], "data_style": {"fontsize": 12, "fontweight": "w500", "color": "black"}}, {"type": "widget15", "title": {"value": "Competitive Analysis", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}, "subData": [{"subtitle": [{"title": {"value": "Competitor-A", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}, "subtitle": {"value": "Medium", "style": {"fontsize": 12, "fontweight": "w500", "color": "orange"}}}], "tertiaryTitle": [{"title": {"value": "Market leader", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}}, {"title": {"value": "No recent activity", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}}], "extraData": {"value": "Our Advantages:", "style": {"fontsize": 12, "fontweight": "w600", "color": "black"}, "data": ["Faster deployment", "Superior support", "Better APIs"], "data_style": {"fontsize": 12, "fontweight": "w500", "color": "black"}}}, {"subtitle": [{"title": {"value": "Competitor-B", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}, "subtitle": {"value": "Low", "style": {"fontsize": 12, "fontweight": "w500", "color": "green"}}}], "tertiaryTitle": [{"title": {"value": "Growing challenger", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}}, {"title": {"value": "LinkedIn activity 6mo ago", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}}], "extraData": {"value": "Our Advantages:", "style": {"fontsize": 12, "fontweight": "w600", "color": "black"}, "data": ["Feature completeness", "Enterprise scalability", "Competitive pricing"], "data_style": {"fontsize": 12, "fontweight": "w500", "color": "black"}}}]}, {"type": "widget16", "title": {"value": "Key Differentiators", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}, "subtitle": [{"title": {"value": "Implementation Speed", "style": {"fontsize": 12, "fontweight": "w500", "color": "black"}}, "subtitle": {"value": "2-3 weeks vs 2-3 months", "style": {"fontsize": 12, "fontweight": "w600", "color": "blue"}}}, {"title": {"value": "Integrations", "style": {"fontsize": 12, "fontweight": "w500", "color": "black"}}, "subtitle": {"value": "300+ out-of-box", "style": {"fontsize": 12, "fontweight": "w600", "color": "blue"}}}, {"title": {"value": "Customer Satisfaction", "style": {"fontsize": 12, "fontweight": "w500", "color": "black"}}, "subtitle": {"value": "97% CSAT", "style": {"fontsize": 12, "fontweight": "w600", "color": "blue"}}}]}, {"type": "widget17", "title": {"value": "Battle Plan", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}, "subtitle": [{"title": {"value": "Key Messages:", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}, "data": ["Emphasize rapid time-to-value", "Showcase integration ecosystem", "Highlight customer success stories"], "data_style": {"fontsize": 12, "fontweight": "w500", "color": "black"}}}, {"title": {"value": "Trap Questions:", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}, "data": ["Ask about implementation timeline concerns", "Discuss integration complexity challenges"], "data_style": {"fontsize": 12, "fontweight": "w500", "color": "black"}}}]}]}], "data": {}}}}