{"lead_creation_lo1_information_hierarchy": {"version": "2.0", "description": "LO 1: Lead Basics & Context Discovery - Initial lead capture with company intelligence", "implementation_date": "2025-01-15", "learning_objective": {"title": "Lead Basics & Context Discovery", "description": "Capture essential lead information and provide comprehensive company context for informed qualification decisions", "primary_goals": ["Establish lead identity and basic contact information", "Determine initial product fit and lead source", "Provide comprehensive company intelligence for context", "Calculate preliminary lead score for prioritization"], "success_metrics": ["Lead creation completion rate >95%", "Data enrichment success rate >85%", "User progression to LO 2 rate >70%"]}, "user_context": {"current_user": "<PERSON>", "role": "Sales Representative", "territory": "West Coast Enterprise", "session_id": "SES-20250115-091530-LO1", "request_timestamp": "2025-01-15T09:15:30Z", "workflow_stage": "initial_lead_capture", "next_stage": "detailed_qualification", "current_pipeline": {"active_leads": 23, "pipeline_value": "$2.4M", "monthly_quota": "$500K"}}, "spatial_organization": {"layout_structure": {"chat_area": {"width_percentage": "65%", "primary_purpose": "Basic lead capture form and AI assistance", "information_levels": ["Level 0 (hidden)", "Level 1 (primary)", "Level 2 (critical)"]}, "side_panel": {"width_percentage": "35%", "primary_purpose": "Company intelligence and context tabs", "information_levels": ["Level 2 (extended)", "Level 3 (reference)", "Level 4 (analytics)"], "tab_structure": ["Company Profile", "Market Intelligence", "Territory Context", "Lead Scoring"]}}}, "level_0_intent_context": {"hierarchy_position": "Foundation layer - invisible to user", "processing_location": "Background system", "cognitive_load": "Zero", "processing_time": "<100ms", "intent_recognition": {"user_input": "I need to create a lead for TechCorp - they inquired about our enterprise solution", "system_interpretation": {"parsed_intent": "LEAD_CREATION_BASIC", "confidence_score": 0.96, "entity_extraction": {"action": "create lead - basic capture", "company_name": "TechCorp", "product_interest": "enterprise solution", "lead_source": "inbound inquiry", "workflow_stage": "lo1_basic_capture"}}, "context_preparation": {"user_patterns": "<PERSON> prefers company-first approach", "territory_context": "West Coast Enterprise, TechCorp in target segment", "company_enrichment": "TechCorp profile found, enrichment initiated", "system_decisions": ["Load LO1 form", "Enrich company data", "Prepare context tabs", "Calculate preliminary score"]}}, "company_intelligence_processing": {"data_enrichment": {"company_found": true, "data_sources": ["Clearbit", "LinkedIn Sales Navigator", "ZoomInfo", "6sense"], "enrichment_confidence": 0.92, "processing_time": "187ms", "enriched_fields": ["industry", "size", "revenue", "location", "tech_stack", "news"]}, "duplicate_detection": {"existing_leads": 0, "existing_contacts": 0, "similar_companies": 2, "conflict_risk": "Low", "suggestions": []}, "preliminary_scoring": {"initial_score": 78, "score_tier": "High", "confidence": 0.87, "key_factors": ["Company size", "Industry fit", "Geographic alignment", "Inbound interest"], "next_stage_recommendations": ["Proceed to detailed qualification", "High priority for follow-up"]}}}, "level_1_primary_actions": {"hierarchy_position": "Essential interaction - always visible", "display_location": "Chat area embedded form", "cognitive_load": "Medium - focused data entry", "visibility": "Prominent above fold", "textual_hierarchy": {"primary_heading": {"text": "🎯 LEAD BASICS & DISCOVERY", "text_style": {"fontsize": 16, "fontweight": "w600", "color": "black"}, "purpose": "LO1 identification", "hierarchy_level": "H1", "stage_indicator": "Step 1 of 2"}, "section_headers": {"company_section": {"text": "🏢 Company Information", "text_style": {"fontsize": 16, "fontweight": "w500", "color": "black"}, "hierarchy_level": "Section H2", "description": "Basic company identification and context"}, "contact_section": {"text": "👤 Primary Contact", "text_style": {"fontsize": 16, "fontweight": "w500", "color": "black"}, "hierarchy_level": "Section H2", "description": "Initial contact person details"}, "opportunity_section": {"text": "💼 Initial Opportunity", "text_style": {"fontsize": 16, "fontweight": "w500", "color": "black"}, "hierarchy_level": "Section H2", "description": "Basic opportunity context"}}, "field_labels": {"hierarchy_level": "Field labels", "text_style": {"fontsize": 14, "fontweight": "w500", "color": "black"}, "required_indicator": "Red asterisk for required fields", "helper_text_style": {"fontsize": 12, "fontweight": "w400", "color": "grey"}}, "action_buttons": {"primary_action": {"text": "Continue to Details →", "text_style": {"fontsize": 14, "fontweight": "w500", "color": "white"}, "visual_hierarchy": "Highest prominence, blue background", "purpose": "Progress to LO2"}, "secondary_actions": {"save_draft": "Save as Draft", "disqualify": "<PERSON> Unqualified", "text_style": {"fontsize": 14, "fontweight": "w500", "color": "black"}, "visual_hierarchy": "Lower prominence, outline buttons"}}}, "essential_inputs": {"company_information": {"company_name": {"field_id": "company_name", "value": "TechCorp Inc.", "auto_enriched": true, "validation": "Real-time duplicate checking", "enrichment_indicator": "✓ Company found & enriched", "required": true}, "industry": {"field_id": "industry", "value": "Software/SaaS", "auto_populated": true, "source": "Company enrichment", "readonly": true}, "company_size": {"field_id": "company_size", "value": "500-1000 employees", "auto_populated": true, "confidence": "High", "readonly": true}, "company_location": {"field_id": "company_location", "value": "San Francisco, CA", "auto_populated": true, "territory_indicator": "✓ In your territory", "readonly": true}}, "primary_contact": {"contact_name": {"field_id": "contact_name", "placeholder": "Primary contact name", "required": true, "validation": "Name format validation", "helper_text": "Main person inquiring about our solution"}, "contact_email": {"field_id": "contact_email", "placeholder": "<EMAIL>", "validation": "Email format + domain verification", "domain_suggestion": "Suggests @techcorp.com based on company", "required": true}, "job_title": {"field_id": "job_title", "placeholder": "Job title", "suggestions": ["CTO", "VP Engineering", "Director IT", "CISO"], "smart_suggestions": "Based on company profile and inquiry type"}, "contact_phone": {"field_id": "contact_phone", "placeholder": "+****************", "format": "Auto-formatting enabled", "international": true, "optional": true}}, "initial_opportunity": {"lead_source": {"field_id": "lead_source", "selected_value": "Inbound Inquiry", "smart_default_applied": true, "options": ["Inbound Inquiry", "Website Form", "Demo Request", "Sales Development", "Referral", "Event/Conference", "Cold Outreach", "Partner Referral"], "required": true}, "product_interest": {"field_id": "product_interest", "selected_value": "Enterprise Solution", "smart_default_applied": true, "options": ["Enterprise Solution", "Professional Plan", "Starter Package", "Custom Integration", "API/Developer Tools", "Multiple Products"], "required": true}, "urgency_level": {"field_id": "urgency_level", "options": ["Immediate (< 1 month)", "Short-term (1-3 months)", "Medium-term (3-6 months)", "Long-term (6+ months)", "Exploring/Unknown"], "default": "Medium-term (3-6 months)", "helper_text": "Based on initial conversation"}, "inquiry_summary": {"field_id": "inquiry_summary", "placeholder": "Brief summary of their inquiry or interest...", "max_length": 500, "helper_text": "Key points from initial conversation", "auto_resize": true, "smart_suggestions": "AI-suggested based on product interest"}}}}, "level_2_contextual_information": {"hierarchy_position": "Decision influencing - dual placement", "cognitive_load": "Medium - contextual awareness", "placement_strategy": "Critical in chat, extended in tabs", "chat_area_critical": {"textual_hierarchy": {"intelligence_alerts": {"text_style": {"fontsize": 14, "fontweight": "w500", "color": "black"}, "icon_integration": "Status icons", "hierarchy_level": "Alert H3", "examples": ["🎯 Lead Score: 78/100 (High Priority)", "✅ Company verified and enriched", "🗺️ Perfect territory fit for West Coast", "📈 Strong growth indicators detected"]}, "context_cards": {"container_styling": "Background: #F8F9FA, border: 1px solid #E3E3E3", "card_hierarchy": {"icon": "Leading visual indicator", "title_style": {"fontsize": 14, "fontweight": "w500", "color": "black"}, "content_style": {"fontsize": 13, "fontweight": "w400", "color": "black"}, "action_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}}}, "company_verification_card": {"title": "✅ Company Verified", "content": ["TechCorp Inc. confirmed in database", "500-1000 employees, Software/SaaS", "Recent funding: $15M Series B", "Strong financial health indicators"], "action": "View full profile →"}, "territory_fit_card": {"title": "🗺️ Territory Alignment", "content": ["Perfect fit for West Coast Enterprise", "San Francisco, CA location", "No territory conflicts detected", "<PERSON> - Primary rep assigned"], "action": "View territory details →"}}}, "progression_logic": {"completion_requirements": {"required_fields": ["company_name", "contact_name", "contact_email", "lead_source", "product_interest"], "optional_fields": ["job_title", "contact_phone", "urgency_level", "inquiry_summary"], "validation_rules": ["Email format validation", "Company verification successful", "No duplicate lead detected"]}, "next_stage_preparation": {"data_passed_to_lo2": ["All LO1 form data", "Company enrichment results", "Initial lead score", "Territory assignment", "Preliminary insights"], "context_preservation": ["User preferences", "Conversation history", "Company intelligence cache", "Scoring factors"]}, "success_indicators": {"form_completion": "All required fields filled", "data_quality": "High confidence enrichment", "user_engagement": "Tabs explored or insights viewed", "time_efficiency": "Completed in <5 minutes"}}, "hardcoded_texts": {"collection_data": {"name": {"text": "Solution Widgets", "text_style": {"fontsize": 13, "fontweight": "w400", "color": "black"}}}, "option_buttons": {"verify_company": {"text": "Verify Company", "text_style": {"fontsize": 14, "fontweight": "w400", "color": "black"}}, "check_territory": {"text": "Check Territory", "text_style": {"fontsize": 14, "fontweight": "w400", "color": "black"}}, "ai_enrichment": {"text": "AI Enrichment", "text_style": {"fontsize": 14, "fontweight": "w400", "color": "black"}}}, "action_buttons": {"continue_details": {"text": "Continue to Details →", "text_style": {"fontsize": 14, "fontweight": "w500", "color": "white"}}, "save_draft": {"text": "Save as Draft", "text_style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}, "cancel": {"text": "Cancel", "text_style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}}, "progress_indicators": {"lo_title": {"text": "LO TITLE", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "grey"}}, "step_progress": {"text_template": "Step {current}/{total}", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "grey"}}}, "lo_titles": {"lo1": {"text": "Lead Basics & Discovery", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "grey"}}, "lo2": {"text": "Lead Qualification & Opportunity Development", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "grey"}}}, "chat_messages": {"default_message": {"text": "I need to create a lead for TechCorp - they inquired about our enterprise solution", "text_style": {"fontsize": 14, "fontweight": "w400", "color": "black"}}}, "recommendation_section": {"title": {"text": "Recommendation in Context", "text_style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}}, "form_sections": {"company_information": {"title": {"text": "Company Information", "text_style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}, "fields": {"company_name_placeholder": "Company name", "industry_default": "Software/SaaS", "company_size_default": "500-1000 employees", "location_default": "San Francisco, CA", "enrichment_indicator": "✓ Company found & enriched", "territory_indicator": "✓ In your territory"}}, "primary_contact": {"title": {"text": "Primary Contact", "text_style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}, "fields": {"contact_name_placeholder": "Primary contact name", "email_placeholder": "<EMAIL>", "job_title_options": ["CTO", "VP Engineering", "Director IT", "CISO"], "phone_placeholder": "+****************"}}, "initial_opportunity": {"title": {"text": "Initial Opportunity", "text_style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}, "fields": {"lead_source_default": "Inbound Inquiry", "product_interest_default": "Enterprise Solution", "urgency_level_default": "Medium-term (3-6 months)", "inquiry_summary_placeholder": "Brief summary of their inquiry or interest..."}}}, "lo2_sections": {"contact_stakeholder_details": {"title": {"text": "Contact & Stakeholder Details", "text_style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}, "fields": {"contact_title_placeholder": "Chief Technology Officer", "pain_points_placeholder": "What specific challenges are they facing?", "additional_stakeholders_title": "Additional Stakeholders", "buying_committee_size_default": "3-5 people", "buying_committee_options": ["1-2 people", "3-5 people", "6-10 people", "10+ people", "Unknown"], "decision_process_placeholder": "Describe their decision-making process..."}}, "bant_meddic_qualification": {"title": {"text": "Opportunity Qualification (BANT/MEDDIC)", "text_style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}, "budget_qualification": {"title": "Budget Qualification", "budget_status_default": "Budget Identified", "budget_status_options": ["Budget Approved", "Budget Identified", "Budget in Planning", "No Budget Yet", "Unknown"], "budget_range_default": "$100K-$200K", "budget_range_options": ["<$50K", "$50K-$100K", "$100K-$200K", "$200K-$500K", "$500K+", "Undisclosed"]}, "need_qualification": {"title": "Need Qualification", "business_pain_placeholder": "What business problems are they trying to solve?", "success_criteria_placeholder": "How will they measure success?"}, "timeline_qualification": {"title": "Timeline Qualification", "decision_timeline_default": "3-6 months", "implementation_timeline_default": "1-3 months"}}, "technical_business_requirements": {"title": {"text": "Technical & Business Requirements", "text_style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}, "fields": {"primary_use_cases_placeholder": "Primary use cases for our solution...", "integration_needs_placeholder": "Required integrations with existing systems...", "user_count_default": "51-200 users", "user_count_options": ["1-10 users", "11-50 users", "51-200 users", "201-1000 users", "1000+ users"], "decision_criteria_placeholder": "How will they evaluate solutions?"}}, "next_steps_planning": {"title": {"text": "Next Steps & Follow-up Planning", "text_style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}, "fields": {"next_meeting_type_default": "Discovery Call", "next_meeting_options": ["Discovery Call", "Demo/Presentation", "Technical Deep Dive", "Stakeholder Introduction", "Proposal Review"], "meeting_timeline_default": "Next week", "meeting_timeline_options": ["This week", "Next week", "Within 2 weeks", "Within month", "TBD"], "information_needed_placeholder": "What information do you need to gather?", "qualification_summary_placeholder": "Key qualification insights and next steps..."}}}, "stakeholder_data": {"predicted_stakeholders": [{"role": "Chief Technology Officer", "influence": "High", "concerns": ["Technical fit", "Implementation complexity"], "strategy": "Technical deep dive, reference calls"}, {"role": "VP Engineering", "influence": "High", "concerns": ["Development impact", "Tool efficiency"], "strategy": "Developer experience demo"}, {"role": "CISO/Security Lead", "influence": "Medium-High", "concerns": ["Security compliance", "Data protection"], "strategy": "Security docs, compliance overview"}, {"role": "CFO/Finance", "influence": "High", "concerns": ["ROI", "Cost justification"], "strategy": "Business case, ROI analysis"}], "organizational_context": {"company_structure": "Engineering-driven", "decision_style": "Consensus-based", "cultural_factors": ["Innovation-focused culture", "Data-driven decision making", "Strong technical team autonomy"]}}, "competitive_data": {"key_differentiators": [{"name": "Implementation Speed", "value": "2-3 weeks vs 2-3 months"}, {"name": "Integrations", "value": "300+ out-of-box"}, {"name": "Customer Satisfaction", "value": "97% CSAT"}], "battle_plan": {"key_messages": ["Emphasize rapid time-to-value", "Showcase integration ecosystem", "Highlight customer success stories", "Demonstrate scalability for growth"], "trap_questions": ["Ask about implementation timeline concerns", "Discuss integration complexity challenges"]}}, "ui_controls": {"switch_labels": {"nsl": "NSL", "normal": "Normal"}}}, "extra_details": {"tabs": [{"id": "company_profile", "label": "Company Profile", "iconPath": "assets/images/my_business/solutions/solution_related.svg", "text_style": {"default": {"fontsize": 12, "fontweight": "w500", "color": "black"}, "selected": {"fontsize": 12, "fontweight": "w600", "color": "black"}}, "content": {"header": {"company_name": "TechCorp Inc.", "company_type": "Software/SaaS Company", "company_name_style": {"fontsize": 14, "fontweight": "w600", "color": "black"}, "company_type_style": {"fontsize": 12, "fontweight": "w400", "color": "grey"}}, "metrics": [{"label": "Team Size", "value": "500-1000 employees", "trend": "Growing (40% increase)", "color": "blue", "label_style": {"fontsize": 12, "fontweight": "w500", "color": "grey"}, "value_style": {"fontsize": 14, "fontweight": "w600", "color": "black"}, "trend_style": {"fontsize": 10, "fontweight": "w400", "color": "blue"}}, {"label": "Est. Revenue", "value": "$50-100M annually", "trend": "Medium confidence", "color": "green", "label_style": {"fontsize": 12, "fontweight": "w500", "color": "grey"}, "value_style": {"fontsize": 14, "fontweight": "w600", "color": "black"}, "trend_style": {"fontsize": 10, "fontweight": "w400", "color": "green"}}, {"label": "Recent Funding", "value": "$15M Series B", "trend": "2 weeks ago", "color": "purple", "label_style": {"fontsize": 12, "fontweight": "w500", "color": "grey"}, "value_style": {"fontsize": 14, "fontweight": "w600", "color": "black"}, "trend_style": {"fontsize": 10, "fontweight": "w400", "color": "purple"}}, {"label": "Compatibility", "value": "85% compatible", "trend": "Low integration complexity", "color": "orange", "label_style": {"fontsize": 12, "fontweight": "w500", "color": "grey"}, "value_style": {"fontsize": 14, "fontweight": "w600", "color": "black"}, "trend_style": {"fontsize": 10, "fontweight": "w400", "color": "orange"}}], "technology_stack": {"title": "Technology Stack", "title_style": {"fontsize": 14, "fontweight": "w600", "color": "black"}, "technologies": ["Salesforce", "AWS", "React", "PostgreSQL", "<PERSON>er", "Kubernetes"], "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}, "recent_news": {"title": "Recent Company News", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}, "news_items": [{"headline": "TechCorp raises $15M Series B funding", "headline_style": {"fontsize": 12, "fontweight": "w600", "color": "black"}, "date": "2 weeks ago", "date_style": {"fontsize": 12, "fontweight": "w500", "color": "grey"}, "relevance": "High - indicates growth and budget availability", "relevance_style": {"fontsize": 12, "fontweight": "w500", "color": "grey"}}, {"headline": "Expands engineering team by 40%", "headline_style": {"fontsize": 12, "fontweight": "w600", "color": "black"}, "date": "1 month ago", "date_style": {"fontsize": 12, "fontweight": "w500", "color": "grey"}, "relevance": "Medium - scaling tech team suggests need for tools", "relevance_style": {"fontsize": 12, "fontweight": "w500", "color": "grey"}}]}}}, {"id": "market_intel", "label": "Market Intel", "iconPath": "assets/images/my_business/solutions/solution_contextual.svg", "text_style": {"default": {"fontsize": 12, "fontweight": "w500", "color": "black"}, "selected": {"fontsize": 12, "fontweight": "w600", "color": "black"}}, "content": {"industry_context": {"title": "SaaS Industry Context", "icon": "📊", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}, "metrics": [{"label": "Market Size", "value": "$195B global", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}, {"label": "Growth Rate", "value": "12% YoY", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}], "trends": ["AI/ML integration demand", "Security compliance focus", "Multi-cloud strategies"]}, "competitive_environment": {"title": "Competitive Environment", "icon": "⚔️", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}, "competitors": [{"name": "CompetitorA (24% market share)", "threat": "Medium", "color": "orange", "activity": "No recent activity detected", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}, {"name": "CompetitorB (19% market share)", "threat": "Low", "color": "green", "activity": "LinkedIn engagement 6 months ago", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}], "our_position": "Strong challenger with 18% market share"}, "buying_signals": {"title": "Digital Buying Signals", "icon": "📈", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}, "signals": [{"label": "Website Activity", "value": "5 page visits", "subtitle": "Last 30 days", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}, {"label": "Content Engagement", "value": "2 whitepapers", "subtitle": "Downloaded", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}], "intent_score": {"label": "Intent Score", "value": "72/100 - High Intent", "color": "#388E3C", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}}}}, {"id": "territory", "label": "Territory", "iconPath": "assets/images/my_business/solutions/solution_related.svg", "text_style": {"default": {"fontsize": 12, "fontweight": "w500", "color": "black"}, "selected": {"fontsize": 12, "fontweight": "w600", "color": "black"}}, "content": {"territory_overview": {"title": "West Coast Enterprise", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}, "rep_info": {"name": "<PERSON> (Primary Rep)", "coverage": "CA, OR, WA - Enterprise accounts", "quota": {"current": "$340K", "target": "$500K", "percentage": 68}, "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}}, "account_metrics": [{"label": "Total Accounts", "value": "127", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}, {"label": "Active Opportunities", "value": "23", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}, {"label": "Closed Won QTD", "value": "8", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}, {"label": "Pipeline Health", "value": "$2.4M", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}], "regional_performance": {"title": "Regional Performance", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}, "metrics": [{"label": "Avg <PERSON> Size", "value": "$125K", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}, {"label": "Avg Sales Cycle", "value": "4.2 months", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}, {"label": "Win Rate", "value": "73%", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}, {"label": "Performance", "value": "Above Avg", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}]}}}, {"id": "lead_score", "label": "Lead Score", "iconPath": "assets/images/my_business/solutions/recommendation_ai.svg", "text_style": {"default": {"fontsize": 12, "fontweight": "w500", "color": "black"}, "selected": {"fontsize": 12, "fontweight": "w600", "color": "black"}}, "content": {"score_overview": {"title": "LEAD SCORING", "confidence": "87% Confidence", "main_score": "78/100", "priority": "High Priority", "percentile": "Top 15% of leads", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}, "score_breakdown": [{"title": "Company Fit (25/30)", "points": "+25 points", "details": "Employee count: 500-1000 (+8)\nIndustry: SaaS (+10)\nRevenue: $50-100M (+7)", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}, {"title": "Geographic Fit (15/15)", "points": "+15 points", "details": "Territory alignment: Perfect (+15)", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}, {"title": "Engagement Quality (18/25)", "points": "+18 points", "details": "Lead source: Inbound (+12) \nProduct interest: Enterprise (+6)", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}, {"title": "Timing & Intent (20/30)", "points": "+20 points", "details": "Recent funding: Positive (+8) \nGrowth signals: Strong (+7) \nCompetitive activity: Low (+5)", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}], "improvements": {"title": "Potential Improvements", "text_style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}, "items": ["Get budget confirmation (+5-10 points)", "Identify decision maker (+5-8 points)", "Define timeline (+3-7 points)"]}}}], "content_data": [{"id": "1", "title": "Related", "data": [{"type": "widget1", "title": {"value": "TechCorp Inc", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}, "subtitle": {"value": "Software/SaaS Company", "style": {"fontsize": 12, "fontweight": "w400", "color": "#757575"}}}, {"type": "widget2", "data": [{"type": "widget2", "title": {"value": "Team Size", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "500-1000 Employees", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}, "tertiaryTitle": {"value": "Growing (40%) increase", "style": {"fontsize": 10, "fontweight": "w400", "color": "blue"}}}, {"type": "widget2", "title": {"value": "Est. Revenue", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "$15M Series B", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}, "tertiaryTitle": {"value": "2 weeks ago", "style": {"fontsize": 10, "fontweight": "w400", "color": "green"}}}, {"type": "widget2", "title": {"value": "Recent Funding", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "$15M Series B", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}, "tertiaryTitle": {"value": "2 weeks ago", "style": {"fontsize": 10, "fontweight": "w400", "color": "purple"}}}, {"type": "widget2", "title": {"value": "Compatibility", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "85% compatible", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}, "tertiaryTitle": {"value": "Low integration complexity", "style": {"fontsize": 10, "fontweight": "w400", "color": "orange"}}}]}, {"type": "widget3", "title": {"value": "Technology Stack", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}, "technologies": {"data": ["Salesforce", "AWS", "React", "PostgreSQL", "<PERSON>er", "Kubernetes"], "style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}}, {"type": "widget4", "title": {"value": "Recent Company News", "style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}, "subtitle": [{"title": {"value": "TechCorp raises $15M Series B funding", "style": {"fontsize": 12, "fontweight": "w600", "color": "black"}}, "subtitle": {"value": "2 weeks ago", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "tertiaryTitle": {"value": "High - indicates growth and budget availability", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}}, {"title": {"value": "Expands engineering team by 40%", "style": {"fontsize": 12, "fontweight": "w600", "color": "black"}}, "subtitle": {"value": "1 month ago", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "tertiaryTitle": {"value": "Medium - scaling tech team suggests need for tools", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}}]}, {"type": "widget5", "title": {"value": "SaaS Industry Context", "style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}, "subtitle": [{"title": {"value": "Market Size", "style": {"fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "$195B global", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}}, {"title": {"value": "Growth Rate", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "12% YoY", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}}], "tertiaryTitle": {"value": "Key Trends", "style": {"fontsize": 12, "fontweight": "w600", "color": "black"}, "data": ["AI/ML integration demand", "Security compliance focus", "Multi-cloud strategies"], "data_style": {"fontsize": 12, "fontweight": "w500", "color": "black"}}}, {"type": "widget6", "title": {"value": "Competitive Environment", "style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}, "subtitle": [{"title": {"value": "CompetitorA (24% market share)", "style": {"fontsize": 12, "fontweight": "w400", "color": "black"}}, "subtitle": {"value": "Medium", "style": {"fontsize": 10, "fontweight": "w500", "color": "orange"}}, "tertiaryTitle": {"value": "No recent activity detected", "style": {"fontsize": 12, "fontweight": "w400", "color": "blue"}}}, {"title": {"value": "CompetitorB (19% market share)", "style": {"fontsize": 12, "fontweight": "w400", "color": "black"}}, "subtitle": {"value": "Low", "style": {"fontsize": 10, "fontweight": "w500", "color": "green"}}, "tertiaryTitle": {"value": "LinkedIn engagement 6 months ago", "style": {"fontsize": 12, "fontweight": "w400", "color": "blue"}}}], "tertiaryTitle": {"title": {"value": "Our Position", "style": {"fontsize": 12, "fontweight": "w400", "color": "black"}}, "subtitle": {"value": "Strong challenger with 18% market share", "style": {"fontsize": 12, "fontweight": "w500", "color": "blue"}}}}, {"type": "widget7", "title": {"value": " Digital Buying Signals", "style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}, "subtitle": [{"title": {"value": "Website Activity", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "5 page visits", "style": {"fontsize": 14, "fontweight": "w600", "color": "blue"}}, "tertiaryTitle": {"value": "Last 30 days", "style": {"fontsize": 10, "fontweight": "w400", "color": "#757575"}}}, {"title": {"value": "Content Engagement", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "2 whitepapers", "style": {"fontsize": 14, "fontweight": "w600", "color": "blue"}}, "tertiaryTitle": {"value": "Downloaded", "style": {"fontsize": 10, "fontweight": "w400", "color": "#757575"}}}], "tertiaryTitle": {"title": {"value": "Intent Score", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "72/100 - High Intent", "style": {"fontsize": 16, "fontweight": "w600", "color": "green"}}}}]}, {"id": "2", "title": "Contextual", "data": [{"type": "widget8", "hasExtraData": true, "title": {"value": "West Coast Enterprise", "style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}, "extra": {"title": {"value": "<PERSON> (Primary Rep)", "style": {"fontsize": 14, "fontweight": "w600", "color": "orange"}}, "subtitle": {"value": "CA, OR, WA - Enterprise accounts", "style": {"fontsize": 12, "fontweight": "w400", "color": "#757575"}}, "tertiaryTitle": {"value": "Quota: $340K of $500K (68%)", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "extraData": {"type": "progressBar", "value": "68%", "totalValue": "100%", "color": "green", "backgroundColor": "#757575"}}, "subtitle": [{"title": {"value": "Total Accounts", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "127", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}}, {"title": {"value": "Active Opportunities", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "23", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}}, {"title": {"value": "Closed Won QTD", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "8", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}}, {"title": {"value": "Pipeline Health", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "$2.4M", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}}]}, {"type": "widget8", "hasExtraData": false, "title": {"value": "Regional Performance", "style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}, "subtitle": [{"title": {"value": "Avg <PERSON> Size", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "$125K", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}}, {"title": {"value": "Avg Sales Cycle", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "4.2 months", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}}, {"title": {"value": "Win Rate", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "73%", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}}, {"title": {"value": "Performance", "style": {"fontsize": 12, "fontweight": "w500", "color": "#757575"}}, "subtitle": {"value": "Above Avg", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}}]}, {"type": "widget9", "title": {"value": "LEAD SCORING", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}, "subtitle": {"value": "87% Confidence", "style": {"fontsize": 14, "fontweight": "w500", "color": "#4CAF50"}}, "tertiaryTitle": {"value": "78/100", "style": {"fontsize": 28, "fontweight": "w600", "color": "#4CAF50"}}, "extraData": {"title": {"value": "High Priority", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}, "subtitle": {"value": "Top 15% of leads", "style": {"fontsize": 10, "fontweight": "w400", "color": "blue"}}}}, {"type": "widget10", "title": {"value": "Score Breakdown", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}, "subtitle": [{"title": {"value": "Company Fit (25/30)", "style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}, "subtitle": {"value": "+25 points", "style": {"fontsize": 12, "fontweight": "w500", "color": "#4CAF50"}}, "data": ["Employee count: 500-1000 (+8)", "Industry: SaaS (+10)", "Revenue: $50-100M (+7)"], "data_style": {"fontsize": 10, "fontweight": "w400", "color": "#757575"}}, {"title": {"value": "Geographic Fit (15/15)", "style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}, "subtitle": {"value": "+15 points", "style": {"fontsize": 12, "fontweight": "w500", "color": "#4CAF50"}}, "data": ["Territory alignment: Perfect (+15)"], "data_style": {"fontsize": 10, "fontweight": "w400", "color": "#757575"}}, {"title": {"value": "Engagement Quality (18/25)", "style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}, "subtitle": {"value": "+18 points", "style": {"fontsize": 12, "fontweight": "w500", "color": "#4CAF50"}}, "data": ["Lead source: Inbound (+12)", "Product interest: Enterprise (+6)"], "data_style": {"fontsize": 10, "fontweight": "w400", "color": "#757575"}}, {"title": {"value": "Timing & Intent (20/30)", "style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}, "subtitle": {"value": "+20 points", "style": {"fontsize": 12, "fontweight": "w500", "color": "#4CAF50"}}, "data": ["Recent funding: Positive (+8)", "Growth signals: Strong (+7)", "Competitive activity: Low (+5)"], "data_style": {"fontsize": 10, "fontweight": "w400", "color": "#757575"}}]}, {"type": "widget11", "title": {"value": "Potential Improvements", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}, "data": ["Get budget confirmation (+5-10 points)", "Identify decision maker (+5-8 points)", "Define timeline (+3-7 points)"], "data_style": {"fontsize": 12, "fontweight": "w400", "color": "black"}}]}], "cards": []}, "physical_layer": [{"header": {"title": {"value": "LEAD BASICS & DISCOVERY", "style": {"fontsize": 14, "fontweight": "w600", "color": "black"}}}, "user_input": [{"id": 1, "item_id": "GO1.LO1.IP1.IT2", "input_stack_id": "GO1.LO1.IP1", "slot_id": "LeaveApplication.employeeId", "source_type": "user", "source_description": "Source: user, Agent: HUMAN, Type: input", "required": true, "lo_id": "GO1.LO1", "data_type": "string", "ui_control": "text", "nested_function_id": null, "is_visible": null, "name": null, "type": null, "read_only": false, "agent_type": "HUMAN", "dependent_attribute": false, "dependent_attribute_value": null, "enum_values": null, "default_value": null, "information_field": false, "constant_field": false, "entity_id": "E7", "attribute_id": "E7.At2", "entity_name": "LeaveApplication", "attribute_name": "Company Name", "display_name": "Company Name", "input_value": "TechCorp Inc.", "allowed_values": null, "validations": null, "has_dropdown_source": false, "dropdown_options": null, "dependencies": null, "dependency_type": null, "needs_parent_value": false, "parent_ids": null, "metadata": null, "contextual_id": "GO1.LO1.IP1.IT2", "size": 2}, {"id": 2, "item_id": "GO1.LO1.IP1.IT2", "input_stack_id": "GO1.LO1.IP1", "slot_id": "LeaveApplication.employeeId", "source_type": "user", "source_description": "Source: user, Agent: HUMAN, Type: input", "required": true, "lo_id": "GO1.LO1", "data_type": "string", "ui_control": "text", "nested_function_id": null, "is_visible": null, "name": null, "type": null, "read_only": true, "agent_type": "HUMAN", "dependent_attribute": false, "dependent_attribute_value": null, "enum_values": null, "default_value": null, "information_field": false, "constant_field": false, "entity_id": "E7", "attribute_id": "E7.At2", "entity_name": "LeaveApplication", "attribute_name": "Industry", "display_name": "Industry", "input_value": "Software/SaaS", "allowed_values": null, "validations": null, "has_dropdown_source": false, "dropdown_options": null, "dependencies": null, "dependency_type": null, "needs_parent_value": false, "parent_ids": null, "metadata": null, "contextual_id": "GO1.LO1.IP1.IT2", "size": 2}, {"id": 3, "item_id": "GO1.LO1.IP1.IT2", "input_stack_id": "GO1.LO1.IP1", "slot_id": "LeaveApplication.employeeId", "source_type": "user", "source_description": "Source: user, Agent: HUMAN, Type: input", "required": true, "lo_id": "GO1.LO1", "data_type": "string", "ui_control": "text", "nested_function_id": null, "is_visible": null, "name": null, "type": null, "read_only": true, "agent_type": "HUMAN", "dependent_attribute": false, "dependent_attribute_value": null, "enum_values": null, "default_value": null, "information_field": false, "constant_field": false, "entity_id": "E7", "attribute_id": "E7.At2", "entity_name": "LeaveApplication", "attribute_name": "Company Size", "display_name": "Company Size", "input_value": "500-1000 employees", "allowed_values": null, "validations": null, "has_dropdown_source": false, "dropdown_options": null, "dependencies": null, "dependency_type": null, "needs_parent_value": false, "parent_ids": null, "metadata": null, "contextual_id": "GO1.LO1.IP1.IT2", "size": 2}, {"id": 4, "item_id": "GO1.LO1.IP1.IT2", "input_stack_id": "GO1.LO1.IP1", "slot_id": "LeaveApplication.employeeId", "source_type": "user", "source_description": "Source: user, Agent: HUMAN, Type: input", "required": true, "lo_id": "GO1.LO1", "data_type": "string", "ui_control": "text", "nested_function_id": null, "is_visible": null, "name": null, "type": null, "read_only": true, "agent_type": "HUMAN", "dependent_attribute": false, "dependent_attribute_value": null, "enum_values": null, "default_value": null, "information_field": false, "constant_field": false, "entity_id": "E7", "attribute_id": "E7.At2", "entity_name": "LeaveApplication", "attribute_name": "Location", "display_name": "Location", "input_value": "San Francisco, CA", "allowed_values": null, "validations": null, "has_dropdown_source": false, "dropdown_options": null, "dependencies": null, "dependency_type": null, "needs_parent_value": false, "parent_ids": null, "metadata": null, "contextual_id": "GO1.LO1.IP1.IT2", "size": 2}, {"id": 5, "item_id": "GO1.LO1.IP1.IT2", "input_stack_id": "GO1.LO1.IP1", "slot_id": "LeaveApplication.employeeId", "source_type": "user", "source_description": "Source: user, Agent: HUMAN, Type: input", "required": true, "lo_id": "GO1.LO1", "data_type": "string", "ui_control": "text", "nested_function_id": null, "is_visible": null, "name": null, "type": null, "read_only": false, "agent_type": "HUMAN", "dependent_attribute": false, "dependent_attribute_value": null, "enum_values": null, "default_value": null, "information_field": false, "constant_field": false, "entity_id": "E7", "attribute_id": "E7.At2", "entity_name": "LeaveApplication", "attribute_name": "Contact Name", "display_name": "Contact Name", "input_value": null, "allowed_values": null, "validations": null, "has_dropdown_source": false, "dropdown_options": null, "dependencies": null, "dependency_type": null, "needs_parent_value": false, "parent_ids": null, "metadata": null, "contextual_id": "GO1.LO1.IP1.IT2", "size": 2}, {"id": 6, "item_id": "GO1.LO1.IP1.IT2", "input_stack_id": "GO1.LO1.IP1", "slot_id": "LeaveApplication.employeeId", "source_type": "user", "source_description": "Source: user, Agent: HUMAN, Type: input", "required": true, "lo_id": "GO1.LO1", "data_type": "string", "ui_control": "text", "nested_function_id": null, "is_visible": null, "name": null, "type": null, "read_only": false, "agent_type": "HUMAN", "dependent_attribute": false, "dependent_attribute_value": null, "enum_values": null, "default_value": null, "information_field": false, "constant_field": false, "entity_id": "E7", "attribute_id": "E7.At2", "entity_name": "LeaveApplication", "attribute_name": "Email", "display_name": "Email", "input_value": null, "allowed_values": null, "validations": null, "has_dropdown_source": false, "dropdown_options": null, "dependencies": null, "dependency_type": null, "needs_parent_value": false, "parent_ids": null, "metadata": null, "contextual_id": "GO1.LO1.IP1.IT2", "size": 2}, {"id": 7, "item_id": "GO1.LO1.IP1.IT7", "input_stack_id": "GO1.LO1.IP1", "slot_id": "LeaveApplication.leaveTypeName", "source_type": "user", "source_description": "Source: user, Agent: HUMAN, Type: input", "required": true, "lo_id": "GO1.LO1", "data_type": "string", "ui_control": "Dropdown", "nested_function_id": null, "is_visible": null, "name": null, "type": null, "read_only": false, "agent_type": "HUMAN", "dependent_attribute": false, "dependent_attribute_value": null, "enum_values": ["CTO", "VP Engineering", "Director IT", "CISO"], "default_value": null, "information_field": false, "constant_field": false, "entity_id": "E7", "attribute_id": "E7.At7", "entity_name": "LeaveApplication", "attribute_name": "Job Title", "display_name": "Job Title", "input_value": "CTO", "allowed_values": null, "validations": null, "has_dropdown_source": false, "dropdown_options": null, "dependencies": null, "dependency_type": null, "needs_parent_value": false, "parent_ids": null, "metadata": null, "contextual_id": "GO1.LO1.IP1.IT7", "size": 1}, {"id": 8, "item_id": "GO1.LO1.IP1.IT2", "input_stack_id": "GO1.LO1.IP1", "slot_id": "LeaveApplication.employeeId", "source_type": "user", "source_description": "Source: user, Agent: HUMAN, Type: input", "required": true, "lo_id": "GO1.LO1", "data_type": "string", "ui_control": "text", "nested_function_id": null, "is_visible": null, "name": null, "type": null, "read_only": false, "agent_type": "HUMAN", "dependent_attribute": false, "dependent_attribute_value": null, "enum_values": null, "default_value": null, "information_field": false, "constant_field": false, "entity_id": "E7", "attribute_id": "E7.At2", "entity_name": "LeaveApplication", "attribute_name": "Phone Number", "display_name": "phone Number", "input_value": null, "allowed_values": null, "validations": null, "has_dropdown_source": false, "dropdown_options": null, "dependencies": null, "dependency_type": null, "needs_parent_value": false, "parent_ids": null, "metadata": null, "contextual_id": "GO1.LO1.IP1.IT2", "size": 2}, {"id": 9, "item_id": "GO1.LO1.IP1.IT7", "input_stack_id": "GO1.LO1.IP1", "slot_id": "LeaveApplication.leaveTypeName", "source_type": "user", "source_description": "Source: user, Agent: HUMAN, Type: input", "required": true, "lo_id": "GO1.LO1", "data_type": "string", "ui_control": "Dropdown", "nested_function_id": null, "is_visible": null, "name": null, "type": null, "read_only": false, "agent_type": "HUMAN", "dependent_attribute": false, "dependent_attribute_value": null, "enum_values": ["Inbound Inquiry", "Website Form", "Demo Request", "Sales Development", "Referral", "Event/Conference", "Cold Outreach", "Partner Referral"], "default_value": null, "information_field": false, "constant_field": false, "entity_id": "E7", "attribute_id": "E7.At7", "entity_name": "LeaveApplication", "attribute_name": "Lead Source", "display_name": "Lead Source", "input_value": null, "allowed_values": null, "validations": null, "has_dropdown_source": false, "dropdown_options": null, "dependencies": null, "dependency_type": null, "needs_parent_value": false, "parent_ids": null, "metadata": null, "contextual_id": "GO1.LO1.IP1.IT7", "size": 1}, {"id": 10, "item_id": "GO1.LO1.IP1.IT7", "input_stack_id": "GO1.LO1.IP1", "slot_id": "LeaveApplication.leaveTypeName", "source_type": "user", "source_description": "Source: user, Agent: HUMAN, Type: input", "required": true, "lo_id": "GO1.LO1", "data_type": "string", "ui_control": "Dropdown", "nested_function_id": null, "is_visible": null, "name": null, "type": null, "read_only": false, "agent_type": "HUMAN", "dependent_attribute": false, "dependent_attribute_value": null, "enum_values": ["Enterprise Solution", "Professional Plan", "Starter Package", "Custom Integration", "API/Developer Tools", "Multiple Products"], "default_value": null, "information_field": false, "constant_field": false, "entity_id": "E7", "attribute_id": "E7.At7", "entity_name": "LeaveApplication", "attribute_name": "Product Intrest", "display_name": "Product Intrest", "input_value": null, "allowed_values": null, "validations": null, "has_dropdown_source": false, "dropdown_options": null, "dependencies": null, "dependency_type": null, "needs_parent_value": false, "parent_ids": null, "metadata": null, "contextual_id": "GO1.LO1.IP1.IT7", "size": 1}, {"id": 11, "item_id": "GO1.LO1.IP1.IT7", "input_stack_id": "GO1.LO1.IP1", "slot_id": "LeaveApplication.leaveTypeName", "source_type": "user", "source_description": "Source: user, Agent: HUMAN, Type: input", "required": true, "lo_id": "GO1.LO1", "data_type": "string", "ui_control": "Dropdown", "nested_function_id": null, "is_visible": null, "name": null, "type": null, "read_only": false, "agent_type": "HUMAN", "dependent_attribute": false, "dependent_attribute_value": null, "enum_values": ["Immediate (< 1 month)", "Short-term (1-3 months)", "Medium-term (3-6 months)", "Long-term (6+ months)", "Exploring/Unknown"], "default_value": null, "information_field": false, "constant_field": false, "entity_id": "E7", "attribute_id": "E7.At7", "entity_name": "LeaveApplication", "attribute_name": "Urgency Level", "display_name": "Urgency Level", "input_value": "Medium-term (3-6 months)", "allowed_values": null, "validations": null, "has_dropdown_source": false, "dropdown_options": null, "dependencies": null, "dependency_type": null, "needs_parent_value": false, "parent_ids": null, "metadata": null, "contextual_id": "GO1.LO1.IP1.IT7", "size": 1}, {"id": 12, "item_id": "GO1.LO1.IP1.IT2", "input_stack_id": "GO1.LO1.IP1", "slot_id": "LeaveApplication.employeeId", "source_type": "user", "source_description": "Source: user, Agent: HUMAN, Type: input", "required": true, "lo_id": "GO1.LO1", "data_type": "string", "ui_control": "MultiLine", "nested_function_id": null, "is_visible": null, "name": null, "type": null, "read_only": false, "agent_type": "HUMAN", "dependent_attribute": false, "dependent_attribute_value": null, "enum_values": null, "default_value": null, "information_field": false, "constant_field": false, "entity_id": "E7", "attribute_id": "E7.At2", "entity_name": "LeaveApplication", "attribute_name": "Inquiry Summary", "display_name": "Inquiry Summary", "input_value": null, "allowed_values": null, "validations": null, "has_dropdown_source": false, "dropdown_options": null, "dependencies": null, "dependency_type": null, "needs_parent_value": false, "parent_ids": null, "metadata": null, "contextual_id": "GO1.LO1.IP1.IT2", "size": 1}]}, {"type": "widget_1", "header": {"title": {"value": "Recommendation", "style": {"fontsize": 12, "fontweight": "w600", "color": "black"}}, "recommendation": {"image": "assets/images/my_business/box_add.svg", "data": ["Verify Company", "Check Territory", "AI Enrichment"], "style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}}}, {"type": "widget_2", "header": {"options": [{"title": {"value": "Company Verified", "style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}, "data": ["TechCorp Inc. confirmed in database", "500-1000 employees, Software/SaaS", "Recent funding: $15M Series B", "Strong financial health indicators"], "style": {"fontsize": 12, "fontweight": "w400", "color": "black"}, "subtitle": {"value": "View full profile →", "style": {"fontsize": 14, "fontweight": "w500", "color": "blue"}}}, {"title": {"value": "Territory Alignment", "style": {"fontsize": 14, "fontweight": "w500", "color": "black"}}, "data": ["Perfect fit for West Coast Enterprise", "San Francisco, CA location", "No territory conflicts detected", "<PERSON> - Primary rep assigned"], "style": {"fontsize": 12, "fontweight": "w400", "color": "black"}, "subtitle": {"value": "View territory details →", "style": {"fontsize": 14, "fontweight": "w500", "color": "blue"}}}]}}]}}