[{"id": "owner", "title": "CEO", "level": "M4", "consolidated": {"total_gos": "1285", "internal_elimination": "-4", "effeciency": "82%", "team_coordination": "98%"}}, {"id": "8eaf2952-7975-48a3-b50e-42f456d3a74d", "title": "Office Administration", "level": "M3", "consolidated": {"total_gos": "16", "internal_elimination": "-1", "effeciency": "89%", "team_coordination": "90%"}}, {"id": "caee0916-3701-43fc-8be0-a315fe1b3832", "title": "Human Resources", "level": "M3", "consolidated": {"total_gos": "34", "internal_elimination": "-2", "effeciency": "88%", "team_coordination": "95%"}}, {"id": "0c536fb6-e4e9-44e2-8a28-03ddab48afab", "title": "Hire to Retire", "level": "M3", "consolidated": {"total_gos": "44", "internal_elimination": "-4", "effeciency": "82%", "team_coordination": "94%"}}, {"id": "0bdc6650-3940-4158-a7d2-b2384eb3e434", "title": "Business Intelligence & Analytics", "level": "M3", "consolidated": {"total_gos": "24", "internal_elimination": "-1", "effeciency": "89%", "team_coordination": "92%"}}, {"id": "1beb3ec5-6e0e-499f-b24f-37200054a1ce", "title": "Warehouse Management", "level": "M3", "consolidated": {"total_gos": "56", "internal_elimination": "-1", "effeciency": "82%", "team_coordination": "91%"}}, {"id": "e5d84c6a-e389-45b7-9b5e-62a5232f91df", "title": "O2C", "level": "M3", "consolidated": {"total_gos": "75", "internal_elimination": "-3", "effeciency": "83%", "team_coordination": "99%"}}, {"id": "f7e305bd-9943-4296-a7c1-b7d7cd659f2a", "title": "Procurement and Supply Chain", "level": "M3", "consolidated": {"total_gos": "124", "internal_elimination": "-2", "effeciency": "81%", "team_coordination": "91%"}}, {"id": "564cba08-7bcb-4c7e-b3e9-9462e2534d21", "title": "Legal and Compliance", "level": "M3", "consolidated": {"total_gos": "96", "internal_elimination": "-4", "effeciency": "85%", "team_coordination": "99%"}}, {"id": "1bb485b1-7a5e-4ac9-8ce7-009409f4e049", "title": "Finance & Accounting", "level": "M3", "consolidated": {"total_gos": "246", "internal_elimination": "-2", "effeciency": "84%", "team_coordination": "96%"}}, {"id": "aee14861-5dba-43e4-aa54-99c47428cef1", "title": "Admin & Facilities", "level": "M3", "consolidated": {"total_gos": "16", "internal_elimination": "-5", "effeciency": "81%", "team_coordination": "96%"}}, {"id": "f3bab73a-fcce-441b-9bf7-d4e5cad5d6b5", "title": "Facilities Management", "level": "M3", "consolidated": {"total_gos": "45", "internal_elimination": "-3", "effeciency": "80%", "team_coordination": "93%"}}, {"id": "11ec5de4-bc7d-4d86-917c-53953a796450", "title": "Fixed Assets Management", "level": "M3", "consolidated": {"total_gos": "43", "internal_elimination": "-1", "effeciency": "89%", "team_coordination": "94%"}}, {"id": "88d88155-2ef5-44ec-865a-ba0d51a795a2", "title": "Customer Management", "level": "M3", "consolidated": {"total_gos": "21", "internal_elimination": "-2", "effeciency": "83%", "team_coordination": "95%"}}, {"id": "7bc4db62-b229-4d97-b1e7-cd9689ad3dee", "title": "Software Development Lifecycle", "level": "M3", "consolidated": {"total_gos": "23", "internal_elimination": "-5", "effeciency": "90%", "team_coordination": "93%"}}, {"id": "374e1dc3-f528-43e6-b2da-de1a97f473a4", "title": "Product & Technology(Solution Delivery)", "level": "M3", "consolidated": {"total_gos": "100", "internal_elimination": "-2", "effeciency": "95%", "team_coordination": "93%"}}, {"id": "2e9701c3-ea9d-41d2-993b-2494f4cca63f", "title": "Engineering & QA (T&V)", "level": "M3", "consolidated": {"total_gos": "75", "internal_elimination": "-3", "effeciency": "89%", "team_coordination": "95%"}}, {"id": "2eb2c5f4-0b0f-4d12-b9a0-504f51a02610", "title": "Product & UX (CDUI)", "level": "M3", "consolidated": {"total_gos": "65", "internal_elimination": "-3", "effeciency": "82%", "team_coordination": "99%"}}, {"id": "882da0ca-b546-4297-8e64-774fc5d36532", "title": "Innovation & Strategy(Darwin Center)", "level": "M3", "consolidated": {"total_gos": "82", "internal_elimination": "-3", "effeciency": "91%", "team_coordination": "99%"}}, {"id": "ed6ec35c-4caa-45ad-8cd4-b658824c4657", "title": "Treasury management", "level": "M3", "consolidated": {"total_gos": "100", "internal_elimination": "-1", "effeciency": "80%", "team_coordination": "91%"}}, {"id": "NH0037", "title": "NH0037", "level": "M1", "consolidated": {"total_gos": "2", "internal_elimination": "-3", "effeciency": "84%", "team_coordination": "97%"}}, {"id": "NH0086", "title": "NH0086", "level": "M1", "consolidated": {"total_gos": "3", "internal_elimination": "-4", "effeciency": "80%", "team_coordination": "90%"}}, {"id": "NH0088", "title": "NH0088", "level": "M1", "consolidated": {"total_gos": "5", "internal_elimination": "-5", "effeciency": "91%", "team_coordination": "94%"}}, {"id": "NH0100", "title": "NH0100", "level": "M1", "consolidated": {"total_gos": "3", "internal_elimination": "-2", "effeciency": "87%", "team_coordination": "91%"}}, {"id": "NH0113", "title": "NH0113", "level": "M1", "consolidated": {"total_gos": "4", "internal_elimination": "-2", "effeciency": "83%", "team_coordination": "92%"}}, {"id": "NH0170", "title": "NH0170", "level": "M1", "consolidated": {"total_gos": "3", "internal_elimination": "-4", "effeciency": "93%", "team_coordination": "97%"}}, {"id": "NH0176", "title": "NH0176", "level": "M1", "consolidated": {"total_gos": "2", "internal_elimination": "-4", "effeciency": "89%", "team_coordination": "91%"}}, {"id": "NH0177", "title": "NH0177", "level": "M1", "consolidated": {"total_gos": "5", "internal_elimination": "-2", "effeciency": "87%", "team_coordination": "99%"}}, {"id": "NH0198", "title": "NH0198", "level": "M1", "consolidated": {"total_gos": "1", "internal_elimination": "-3", "effeciency": "84%", "team_coordination": "99%"}}, {"id": "NH0209", "title": "NH0209", "level": "M1", "consolidated": {"total_gos": "4", "internal_elimination": "-1", "effeciency": "87%", "team_coordination": "91%"}}, {"id": "NH0225", "title": "NH0225", "level": "M1", "consolidated": {"total_gos": "3", "internal_elimination": "-3", "effeciency": "90%", "team_coordination": "94%"}}, {"id": "NH0501", "title": "NH0501", "level": "M1", "consolidated": {"total_gos": "3", "internal_elimination": "-4", "effeciency": "92%", "team_coordination": "97%"}}, {"id": "NH0514", "title": "NH0514", "level": "M1", "consolidated": {"total_gos": "2", "internal_elimination": "-5", "effeciency": "83%", "team_coordination": "94%"}}, {"id": "NH0685", "title": "NH0685", "level": "M1", "consolidated": {"total_gos": "2", "internal_elimination": "-2", "effeciency": "81%", "team_coordination": "97%"}}, {"id": "NH0788", "title": "NH0788", "level": "M1", "consolidated": {"total_gos": "1", "internal_elimination": "-4", "effeciency": "82%", "team_coordination": "95%"}}, {"id": "NH0864", "title": "NH0864", "level": "M1", "consolidated": {"total_gos": "4", "internal_elimination": "-5", "effeciency": "82%", "team_coordination": "92%"}}, {"id": "NH0868", "title": "NH0868", "level": "M1", "consolidated": {"total_gos": "5", "internal_elimination": "-2", "effeciency": "81%", "team_coordination": "94%"}}, {"id": "NH0892", "title": "NH0892", "level": "M1", "consolidated": {"total_gos": "5", "internal_elimination": "-4", "effeciency": "82%", "team_coordination": "90%"}}, {"id": "NH0971", "title": "NH0971", "level": "M1", "consolidated": {"total_gos": "3", "internal_elimination": "-3", "effeciency": "92%", "team_coordination": "99%"}}, {"id": "NH0982", "title": "NH0982", "level": "M1", "consolidated": {"total_gos": "5", "internal_elimination": "-2", "effeciency": "92%", "team_coordination": "99%"}}, {"id": "NH0983", "title": "NH0983", "level": "M1", "consolidated": {"total_gos": "5", "internal_elimination": "-5", "effeciency": "80%", "team_coordination": "93%"}}, {"id": "NH1015", "title": "NH1015", "level": "M1", "consolidated": {"total_gos": "2", "internal_elimination": "-3", "effeciency": "85%", "team_coordination": "92%"}}, {"id": "NH1083", "title": "NH1083", "level": "M1", "consolidated": {"total_gos": "1", "internal_elimination": "-5", "effeciency": "85%", "team_coordination": "92%"}}, {"id": "NH1128", "title": "NH1128", "level": "M1", "consolidated": {"total_gos": "2", "internal_elimination": "-3", "effeciency": "91%", "team_coordination": "95%"}}, {"id": "NH1167", "title": "NH1167", "level": "M1", "consolidated": {"total_gos": "2", "internal_elimination": "-4", "effeciency": "91%", "team_coordination": "93%"}}, {"id": "NH1290", "title": "NH1290", "level": "M1", "consolidated": {"total_gos": "5", "internal_elimination": "-3", "effeciency": "80%", "team_coordination": "97%"}}, {"id": "NH1310", "title": "NH1310", "level": "M1", "consolidated": {"total_gos": "4", "internal_elimination": "-2", "effeciency": "91%", "team_coordination": "90%"}}, {"id": "NH1417", "title": "NH1417", "level": "M1", "consolidated": {"total_gos": "4", "internal_elimination": "-1", "effeciency": "88%", "team_coordination": "99%"}}, {"id": "NH1455", "title": "NH1455", "level": "M1", "consolidated": {"total_gos": "4", "internal_elimination": "-2", "effeciency": "81%", "team_coordination": "98%"}}, {"id": "NH1719", "title": "NH1719", "level": "M1", "consolidated": {"total_gos": "3", "internal_elimination": "-5", "effeciency": "81%", "team_coordination": "91%"}}, {"id": "NH1797", "title": "NH1797", "level": "M1", "consolidated": {"total_gos": "5", "internal_elimination": "-5", "effeciency": "89%", "team_coordination": "98%"}}, {"id": "NH1801", "title": "NH1801", "level": "M1", "consolidated": {"total_gos": "1", "internal_elimination": "-1", "effeciency": "81%", "team_coordination": "98%"}}, {"id": "NH1829", "title": "NH1829", "level": "M1", "consolidated": {"total_gos": "1", "internal_elimination": "-4", "effeciency": "88%", "team_coordination": "97%"}}, {"id": "NH1833", "title": "NH1833", "level": "M1", "consolidated": {"total_gos": "1", "internal_elimination": "-1", "effeciency": "92%", "team_coordination": "96%"}}, {"id": "NH1835", "title": "NH1835", "level": "M1", "consolidated": {"total_gos": "3", "internal_elimination": "-4", "effeciency": "82%", "team_coordination": "95%"}}, {"id": "NH1839", "title": "NH1839", "level": "M1", "consolidated": {"total_gos": "2", "internal_elimination": "-2", "effeciency": "92%", "team_coordination": "97%"}}, {"id": "NH1901", "title": "NH1901", "level": "M1", "consolidated": {"total_gos": "3", "internal_elimination": "-1", "effeciency": "83%", "team_coordination": "98%"}}, {"id": "NH1967", "title": "NH1967", "level": "M1", "consolidated": {"total_gos": "3", "internal_elimination": "-5", "effeciency": "83%", "team_coordination": "90%"}}, {"id": "NH2041", "title": "NH2041", "level": "M1", "consolidated": {"total_gos": "3", "internal_elimination": "-1", "effeciency": "80%", "team_coordination": "97%"}}, {"id": "NH2314", "title": "NH2314", "level": "M1", "consolidated": {"total_gos": "2", "internal_elimination": "-5", "effeciency": "88%", "team_coordination": "99%"}}, {"id": "NH2324", "title": "NH2324", "level": "M1", "consolidated": {"total_gos": "1", "internal_elimination": "-5", "effeciency": "80%", "team_coordination": "96%"}}, {"id": "NH2436", "title": "NH2436", "level": "M1", "consolidated": {"total_gos": "1", "internal_elimination": "-4", "effeciency": "84%", "team_coordination": "94%"}}, {"id": "NH2446", "title": "NH2446", "level": "M1", "consolidated": {"total_gos": "2", "internal_elimination": "-1", "effeciency": "80%", "team_coordination": "92%"}}, {"id": "NH2457", "title": "NH2457", "level": "M1", "consolidated": {"total_gos": "4", "internal_elimination": "-1", "effeciency": "92%", "team_coordination": "94%"}}, {"id": "NH2477", "title": "NH2477", "level": "M1", "consolidated": {"total_gos": "3", "internal_elimination": "-2", "effeciency": "91%", "team_coordination": "94%"}}, {"id": "NH2493", "title": "NH2493", "level": "M1", "consolidated": {"total_gos": "5", "internal_elimination": "-3", "effeciency": "85%", "team_coordination": "91%"}}, {"id": "NH2518", "title": "NH2518", "level": "M1", "consolidated": {"total_gos": "5", "internal_elimination": "-3", "effeciency": "93%", "team_coordination": "97%"}}, {"id": "NH2539", "title": "NH2539", "level": "M1", "consolidated": {"total_gos": "1", "internal_elimination": "-4", "effeciency": "81%", "team_coordination": "93%"}}, {"id": "NH2589", "title": "NH2589", "level": "M1", "consolidated": {"total_gos": "5", "internal_elimination": "-2", "effeciency": "87%", "team_coordination": "99%"}}, {"id": "NH2624", "title": "NH2624", "level": "M1", "consolidated": {"total_gos": "1", "internal_elimination": "-4", "effeciency": "93%", "team_coordination": "96%"}}, {"id": "NH2635", "title": "NH2635", "level": "M1", "consolidated": {"total_gos": "2", "internal_elimination": "-1", "effeciency": "80%", "team_coordination": "94%"}}, {"id": "NH2709", "title": "NH2709", "level": "M1", "consolidated": {"total_gos": "2", "internal_elimination": "-2", "effeciency": "85%", "team_coordination": "95%"}}, {"id": "NH2717", "title": "NH2717", "level": "M1", "consolidated": {"total_gos": "2", "internal_elimination": "-5", "effeciency": "88%", "team_coordination": "90%"}}, {"id": "NH2750", "title": "NH2750", "level": "M1", "consolidated": {"total_gos": "1", "internal_elimination": "-4", "effeciency": "83%", "team_coordination": "90%"}}, {"id": "NH2780", "title": "NH2780", "level": "M1", "consolidated": {"total_gos": "2", "internal_elimination": "-3", "effeciency": "92%", "team_coordination": "94%"}}, {"id": "NH2802", "title": "NH2802", "level": "M1", "consolidated": {"total_gos": "3", "internal_elimination": "-5", "effeciency": "93%", "team_coordination": "92%"}}, {"id": "NH2843", "title": "NH2843", "level": "M1", "consolidated": {"total_gos": "1", "internal_elimination": "-1", "effeciency": "82%", "team_coordination": "92%"}}, {"id": "NH3046", "title": "NH3046", "level": "M1", "consolidated": {"total_gos": "3", "internal_elimination": "-4", "effeciency": "94%", "team_coordination": "98%"}}, {"id": "NH3084", "title": "NH3084", "level": "M1", "consolidated": {"total_gos": "2", "internal_elimination": "-4", "effeciency": "84%", "team_coordination": "97%"}}, {"id": "NH3097", "title": "NH3097", "level": "M1", "consolidated": {"total_gos": "5", "internal_elimination": "-3", "effeciency": "83%", "team_coordination": "95%"}}, {"id": "NH3373", "title": "NH3373", "level": "M1", "consolidated": {"total_gos": "3", "internal_elimination": "-5", "effeciency": "91%", "team_coordination": "94%"}}, {"id": "NH3385", "title": "NH3385", "level": "M1", "consolidated": {"total_gos": "2", "internal_elimination": "-4", "effeciency": "83%", "team_coordination": "96%"}}, {"id": "NH3387", "title": "NH3387", "level": "M1", "consolidated": {"total_gos": "2", "internal_elimination": "-5", "effeciency": "91%", "team_coordination": "98%"}}, {"id": "NH3415", "title": "NH3415", "level": "M1", "consolidated": {"total_gos": "3", "internal_elimination": "-3", "effeciency": "84%", "team_coordination": "98%"}}, {"id": "NH3456", "title": "NH3456", "level": "M1", "consolidated": {"total_gos": "3", "internal_elimination": "-3", "effeciency": "80%", "team_coordination": "99%"}}, {"id": "NH3483", "title": "NH3483", "level": "M1", "consolidated": {"total_gos": "1", "internal_elimination": "-4", "effeciency": "94%", "team_coordination": "90%"}}, {"id": "NH3539", "title": "NH3539", "level": "M1", "consolidated": {"total_gos": "5", "internal_elimination": "-1", "effeciency": "91%", "team_coordination": "93%"}}, {"id": "NH3703", "title": "NH3703", "level": "M1", "consolidated": {"total_gos": "4", "internal_elimination": "-3", "effeciency": "88%", "team_coordination": "96%"}}, {"id": "NHC092", "title": "NHC092", "level": "M1", "consolidated": {"total_gos": "3", "internal_elimination": "-5", "effeciency": "92%", "team_coordination": "98%"}}, {"id": "NHC221", "title": "NHC221", "level": "M1", "consolidated": {"total_gos": "4", "internal_elimination": "-3", "effeciency": "93%", "team_coordination": "92%"}}, {"id": "NHC314", "title": "NHC314", "level": "M1", "consolidated": {"total_gos": "3", "internal_elimination": "-2", "effeciency": "80%", "team_coordination": "92%"}}, {"id": "6b9c2d87-4c52-4cef-96f8-12287fb0e6b9", "title": "Access Management", "level": "M2", "consolidated": {"total_gos": "4", "internal_elimination": "-2", "effeciency": "95%", "team_coordination": "95%"}}, {"id": "2dc19c40-671d-4265-9bf4-64bbd1e29e46", "title": "Event & Meeting Management", "level": "M2", "consolidated": {"total_gos": "3", "internal_elimination": "-5", "effeciency": "89%", "team_coordination": "97%"}}, {"id": "13a1fb74-198a-415e-afeb-fee4fed4e059", "title": "Visitor Management", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-5", "effeciency": "91%", "team_coordination": "90%"}}, {"id": "bc9e126c-4a0e-46f2-a455-fb919f68b194", "title": "Access Card Management", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-1", "effeciency": "81%", "team_coordination": "96%"}}, {"id": "7c5bf101-a30d-4aac-a54c-4b2f79513d35", "title": "Bank Account Master Data Management", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-1", "effeciency": "93%", "team_coordination": "99%"}}, {"id": "234b38f7-5fcb-4f63-89c9-ae6740c4f32f", "title": "Payment Run Management", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-4", "effeciency": "83%", "team_coordination": "95%"}}, {"id": "a7c2a37b-3df5-49c8-a81f-ccf5a72e32da", "title": "Vendor Bank Details Management", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-1", "effeciency": "83%", "team_coordination": "99%"}}, {"id": "ce034c90-0eb6-4eb7-badc-551baf0a3465", "title": "Vendor Payment Processing (Various Methods)", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-4", "effeciency": "85%", "team_coordination": "91%"}}, {"id": "7c258244-a2a6-4120-bc94-bf781e93d09c", "title": "Customer Payment Processing (Various Methods)", "level": "M2", "consolidated": {"total_gos": "9", "internal_elimination": "-3", "effeciency": "80%", "team_coordination": "99%"}}, {"id": "6565dc5a-bed3-4976-9583-6c6f4926356d", "title": "Bank Statement Processing (Import, Reconciliation)", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-1", "effeciency": "91%", "team_coordination": "99%"}}, {"id": "01f53909-9307-44fa-840f-816157828b19", "title": "Bank Reconciliation", "level": "M2", "consolidated": {"total_gos": "5", "internal_elimination": "-5", "effeciency": "86%", "team_coordination": "94%"}}, {"id": "c4f29f9d-9f74-4be9-9597-adfb7b82e09a", "title": "Electronic Funds Transfer (EFT) / ACH", "level": "M2", "consolidated": {"total_gos": "7", "internal_elimination": "-4", "effeciency": "92%", "team_coordination": "97%"}}, {"id": "92c84e3a-8468-49b4-bf8b-219134a2f19b", "title": "Payment Reports and Analytics", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-5", "effeciency": "94%", "team_coordination": "91%"}}, {"id": "9c477337-4622-46ac-8382-7fddf7cc6146", "title": "Cheque Management", "level": "M2", "consolidated": {"total_gos": "9", "internal_elimination": "-3", "effeciency": "82%", "team_coordination": "93%"}}, {"id": "2a094a63-4b8d-4a62-aff7-6f8f52512738", "title": "Credit Card Processing", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-3", "effeciency": "82%", "team_coordination": "93%"}}, {"id": "82be1a5a-e484-44d0-bfbb-a7d03c1adf56", "title": "Tokenization/Vaulting", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-1", "effeciency": "90%", "team_coordination": "98%"}}, {"id": "16ab0e85-7585-4d4e-a45f-1d93708cb3ee", "title": "Direct Debit / Collections", "level": "M2", "consolidated": {"total_gos": "9", "internal_elimination": "-3", "effeciency": "85%", "team_coordination": "92%"}}, {"id": "da194552-9c6d-44cd-abb4-afc2acfb29fe", "title": "Payment Reconciliation (Advanced)", "level": "M2", "consolidated": {"total_gos": "9", "internal_elimination": "-2", "effeciency": "92%", "team_coordination": "93%"}}, {"id": "4ec8ba24-aa90-49e6-9e85-8d13c86d805e", "title": "Business Intelligence", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-5", "effeciency": "88%", "team_coordination": "96%"}}, {"id": "1093901d-333c-4606-b9ad-5cec9871908f", "title": "Dashboards", "level": "M2", "consolidated": {"total_gos": "7", "internal_elimination": "-3", "effeciency": "89%", "team_coordination": "96%"}}, {"id": "49acbdd0-20ec-4ef4-b390-526a1954de44", "title": "Report Scheduling", "level": "M2", "consolidated": {"total_gos": "2", "internal_elimination": "-1", "effeciency": "94%", "team_coordination": "98%"}}, {"id": "3ae860d4-a4f8-45a7-bb65-8533f8ae0477", "title": "Data Warehouse", "level": "M2", "consolidated": {"total_gos": "7", "internal_elimination": "-2", "effeciency": "94%", "team_coordination": "92%"}}, {"id": "9e750c66-ac36-45de-8194-375caf653dbf", "title": "Customer Lifecycle Management", "level": "M2", "consolidated": {"total_gos": "7", "internal_elimination": "-4", "effeciency": "90%", "team_coordination": "93%"}}, {"id": "81897a1e-e4e0-4e33-9499-9749e59f9079", "title": "Customer Service & Support Management", "level": "M2", "consolidated": {"total_gos": "7", "internal_elimination": "-1", "effeciency": "87%", "team_coordination": "93%"}}, {"id": "6f569bdf-2856-467c-8ad4-667b1e5c9094", "title": "Customer Contract & Account Management", "level": "M2", "consolidated": {"total_gos": "7", "internal_elimination": "-4", "effeciency": "85%", "team_coordination": "94%"}}, {"id": "64f09386-f0f1-461f-9d64-338f5c7f6598", "title": "Code Repository Center", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-5", "effeciency": "89%", "team_coordination": "97%"}}, {"id": "dbab4bfa-4af1-4bec-8218-b76c26c756c6", "title": "Build & Deploy Hub", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-4", "effeciency": "87%", "team_coordination": "99%"}}, {"id": "46b26fe1-1ca0-4a4b-beed-318440938d75", "title": "Automated Testing Center", "level": "M2", "consolidated": {"total_gos": "9", "internal_elimination": "-5", "effeciency": "90%", "team_coordination": "95%"}}, {"id": "2e4dc8b6-dee9-498c-87f3-9ec7d0b148a4", "title": "Bug Tracking System", "level": "M2", "consolidated": {"total_gos": "9", "internal_elimination": "-1", "effeciency": "82%", "team_coordination": "95%"}}, {"id": "7937ee74-4630-4676-a609-646de2aa9f80", "title": "Code Review Management", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-5", "effeciency": "80%", "team_coordination": "98%"}}, {"id": "841a9884-dc7d-46a6-81bf-481f4dcf1860", "title": "Performance Testing Lab", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-4", "effeciency": "83%", "team_coordination": "98%"}}, {"id": "8bf34659-fde0-475d-bd5c-9316f0f43d56", "title": "Test Case Management", "level": "M2", "consolidated": {"total_gos": "5", "internal_elimination": "-4", "effeciency": "91%", "team_coordination": "95%"}}, {"id": "11e82220-190a-437e-9e1b-9ec3357916a3", "title": "Database Management", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-1", "effeciency": "94%", "team_coordination": "90%"}}, {"id": "4f7628a4-301d-4931-8df6-b6c21e202bea", "title": "Code Quality", "level": "M2", "consolidated": {"total_gos": "5", "internal_elimination": "-3", "effeciency": "86%", "team_coordination": "92%"}}, {"id": "5e15e390-de63-4700-9f35-87290661e8d8", "title": "Release Management Center", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-4", "effeciency": "95%", "team_coordination": "95%"}}, {"id": "313a768a-6513-48b7-a191-2e61362ea602", "title": "Mobile Testing Lab", "level": "M2", "consolidated": {"total_gos": "7", "internal_elimination": "-3", "effeciency": "85%", "team_coordination": "91%"}}, {"id": "500a1205-6815-4e2e-81be-fd6cfb528994", "title": "Space Allocation", "level": "M2", "consolidated": {"total_gos": "2", "internal_elimination": "-5", "effeciency": "91%", "team_coordination": "91%"}}, {"id": "b6a58ab3-66c2-43ed-84eb-431e482593dd", "title": "Room Bookings", "level": "M2", "consolidated": {"total_gos": "3", "internal_elimination": "-1", "effeciency": "90%", "team_coordination": "90%"}}, {"id": "786aca4c-fa92-4961-9735-18c75afa4630", "title": "Inventory & Supplies", "level": "M2", "consolidated": {"total_gos": "5", "internal_elimination": "-2", "effeciency": "94%", "team_coordination": "94%"}}, {"id": "50d335db-c44d-4df5-be19-246d1e9baaca", "title": "Cleaning & Janitorial Services", "level": "M2", "consolidated": {"total_gos": "7", "internal_elimination": "-2", "effeciency": "89%", "team_coordination": "90%"}}, {"id": "45437673-ad6b-4031-8073-9bd4b51b7d0a", "title": "Security & Access Control", "level": "M2", "consolidated": {"total_gos": "5", "internal_elimination": "-1", "effeciency": "95%", "team_coordination": "96%"}}, {"id": "ccee49f9-d09a-4e4f-a426-e3a99cc771a5", "title": "Preventive Maintenance", "level": "M2", "consolidated": {"total_gos": "5", "internal_elimination": "-3", "effeciency": "88%", "team_coordination": "96%"}}, {"id": "1d511f0d-5f7e-4c5b-ba09-ad88bd5db704", "title": "Environmental Health & Safety (EHS)", "level": "M2", "consolidated": {"total_gos": "7", "internal_elimination": "-1", "effeciency": "83%", "team_coordination": "90%"}}, {"id": "ba56f869-358b-4f6a-91a2-464dfab253af", "title": "Fleet Management", "level": "M2", "consolidated": {"total_gos": "6", "internal_elimination": "-5", "effeciency": "80%", "team_coordination": "91%"}}, {"id": "c253a542-b67a-4a1b-afb1-faf6cb1a1bee", "title": "Waste Management", "level": "M2", "consolidated": {"total_gos": "5", "internal_elimination": "-5", "effeciency": "82%", "team_coordination": "98%"}}, {"id": "f89506b7-37ac-4e7c-9cb5-454a6a64786d", "title": "Initial Funding", "level": "M2", "consolidated": {"total_gos": "6", "internal_elimination": "-2", "effeciency": "89%", "team_coordination": "96%"}}, {"id": "c3577066-c1e8-4336-8a26-7368b988695a", "title": "Accounting Operations", "level": "M2", "consolidated": {"total_gos": "6", "internal_elimination": "-5", "effeciency": "92%", "team_coordination": "92%"}}, {"id": "617cc713-87ff-4cb9-b686-1a4d8ba3a9a8", "title": "Financial Reporting and Analysis", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-2", "effeciency": "93%", "team_coordination": "96%"}}, {"id": "dca3a7ee-c608-46b5-96d9-ab6bb09be49f", "title": "Accounting", "level": "M2", "consolidated": {"total_gos": "6", "internal_elimination": "-2", "effeciency": "83%", "team_coordination": "99%"}}, {"id": "aa596ef0-d33a-49c4-a3d9-35f733f5637a", "title": "Finance", "level": "M2", "consolidated": {"total_gos": "6", "internal_elimination": "-5", "effeciency": "95%", "team_coordination": "92%"}}, {"id": "f64f6e8e-23ad-4023-804c-38ceed412a0c", "title": "Accounts Payable", "level": "M2", "consolidated": {"total_gos": "7", "internal_elimination": "-3", "effeciency": "83%", "team_coordination": "93%"}}, {"id": "b2fb629f-bc2b-4c2b-8a78-91d4f6ac7135", "title": "Accounts Receivable", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-1", "effeciency": "81%", "team_coordination": "94%"}}, {"id": "b7027032-c770-49af-8e07-d0ad963fe4a0", "title": "Payroll", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-3", "effeciency": "82%", "team_coordination": "98%"}}, {"id": "********-06e4-464f-bf0c-564aa682344e", "title": "Internal Audit", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-4", "effeciency": "84%", "team_coordination": "99%"}}, {"id": "43c519c7-78ce-4f2e-ba48-1cf4b6af6cd2", "title": "Deferred Revenue and Revenue Recognition", "level": "M2", "consolidated": {"total_gos": "12", "internal_elimination": "-4", "effeciency": "95%", "team_coordination": "93%"}}, {"id": "155b6434-7931-4b28-8947-352bfd3613bb", "title": "Intercompany Accounting", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-3", "effeciency": "81%", "team_coordination": "96%"}}, {"id": "bd46f2a5-d8f7-486a-a37c-0b4761626e36", "title": "Multi-Currency & Multi-Entity Management", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-3", "effeciency": "82%", "team_coordination": "90%"}}, {"id": "f838d0d7-2ec3-47fa-8fab-7f6fe9abf720", "title": "Accruals & Prepayments Management", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-1", "effeciency": "95%", "team_coordination": "99%"}}, {"id": "4948f3ef-1a02-4477-ab3a-0d606e08fae2", "title": "General Ledger (GL) Management", "level": "M2", "consolidated": {"total_gos": "11", "internal_elimination": "-4", "effeciency": "90%", "team_coordination": "90%"}}, {"id": "ec0f0d82-6fa1-4420-b8e8-dabb292a972b", "title": "Expense Management", "level": "M2", "consolidated": {"total_gos": "11", "internal_elimination": "-4", "effeciency": "89%", "team_coordination": "96%"}}, {"id": "82d8ca9d-3cf8-40c0-ae08-e070ea1d2f90", "title": "Lease Accounting (ASC 842 / IFRS 16)", "level": "M2", "consolidated": {"total_gos": "11", "internal_elimination": "-1", "effeciency": "89%", "team_coordination": "96%"}}, {"id": "f51b7efa-3633-4e84-b7c4-f6fb71d63b10", "title": "Close and Consolidation Workflow", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-5", "effeciency": "92%", "team_coordination": "92%"}}, {"id": "2dad2b92-73af-47c5-bf53-7680333b1b3d", "title": "Compensation Benefits", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-1", "effeciency": "95%", "team_coordination": "98%"}}, {"id": "a1606aec-7ac2-40d3-831f-efd3396f6d1c", "title": "Governance & Compliance", "level": "M2", "consolidated": {"total_gos": "7", "internal_elimination": "-4", "effeciency": "94%", "team_coordination": "96%"}}, {"id": "143b442f-e92c-492c-8451-97dc4cc6e4d1", "title": "Financial Planning", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-2", "effeciency": "92%", "team_coordination": "92%"}}, {"id": "25851ce0-278b-4d7d-b829-75ffd7961227", "title": "Financial Strategy Planning", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-4", "effeciency": "90%", "team_coordination": "97%"}}, {"id": "003eb69c-ea76-47d8-850c-bd9e65fd5d71", "title": "Capital Management", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-3", "effeciency": "95%", "team_coordination": "92%"}}, {"id": "6a576422-33d3-4f5a-9e35-50964511a020", "title": "Working Capital Management", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-4", "effeciency": "84%", "team_coordination": "90%"}}, {"id": "445069d8-17c9-40e5-9e83-74aca9d4f9fb", "title": "Cash Management", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-4", "effeciency": "92%", "team_coordination": "91%"}}, {"id": "ae3d21db-2899-4f7d-bab2-1063c74a50dc", "title": "Debt Management", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-4", "effeciency": "84%", "team_coordination": "92%"}}, {"id": "c6d6d070-d7a9-4a7e-b9dc-62ea05e5036b", "title": "Equity Management", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-3", "effeciency": "85%", "team_coordination": "90%"}}, {"id": "5f398147-a97d-4b11-a59d-f721f7849488", "title": "Capital Investment Analysis", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-1", "effeciency": "85%", "team_coordination": "99%"}}, {"id": "8681723b-cea9-4cac-9f35-219835fa9cce", "title": "Capital Structure Optimization", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-5", "effeciency": "94%", "team_coordination": "92%"}}, {"id": "ac87bc62-9f4e-4e47-9a19-ca6f49f7d706", "title": "Capital Performance Monitoring", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-2", "effeciency": "92%", "team_coordination": "97%"}}, {"id": "7a18f2f9-6413-40a5-ae5f-0c15b3690418", "title": "Cost Accounting", "level": "M2", "consolidated": {"total_gos": "7", "internal_elimination": "-1", "effeciency": "93%", "team_coordination": "90%"}}, {"id": "fbc0d11d-bc8a-43f9-949f-79599f397d0e", "title": "Treasury", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-2", "effeciency": "80%", "team_coordination": "99%"}}, {"id": "dd98d22b-e1b9-40d5-91de-ca2cc9620c1b", "title": "Consolidation", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-4", "effeciency": "83%", "team_coordination": "96%"}}, {"id": "cd57281d-7729-4666-80a7-2bed2115d67d", "title": "Budget Planning", "level": "M2", "consolidated": {"total_gos": "6", "internal_elimination": "-3", "effeciency": "80%", "team_coordination": "93%"}}, {"id": "7707a85f-88da-4978-b116-c1d79895617f", "title": "Financial Reporting Compliance", "level": "M2", "consolidated": {"total_gos": "7", "internal_elimination": "-1", "effeciency": "83%", "team_coordination": "95%"}}, {"id": "d3797464-fb1f-4ac2-abbc-4b419536ad33", "title": "Payroll Management", "level": "M2", "consolidated": {"total_gos": "7", "internal_elimination": "-5", "effeciency": "85%", "team_coordination": "90%"}}, {"id": "17001206-d6d2-4384-ac5b-1aa8f424300e", "title": "Payroll Processing", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-1", "effeciency": "83%", "team_coordination": "92%"}}, {"id": "52e3d36f-169f-49a1-93e4-82eaac90bc52", "title": "Revenue Management", "level": "M2", "consolidated": {"total_gos": "7", "internal_elimination": "-2", "effeciency": "90%", "team_coordination": "90%"}}, {"id": "8f48cf38-0e66-4e90-bcf4-ac12212390fc", "title": "Tax Compliance", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-4", "effeciency": "88%", "team_coordination": "95%"}}, {"id": "7f2c35fd-0450-4c6d-8edd-44aa6cb8cac9", "title": "ESG Compliance", "level": "M2", "consolidated": {"total_gos": "9", "internal_elimination": "-5", "effeciency": "86%", "team_coordination": "91%"}}, {"id": "0d75709f-bc26-454e-957f-c4f009a32a08", "title": "Asset Registration & Master Data Management", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-3", "effeciency": "82%", "team_coordination": "97%"}}, {"id": "e5e80b78-d9c2-4357-adec-704752ef9575", "title": "Asset Tracking & Location Management", "level": "M2", "consolidated": {"total_gos": "7", "internal_elimination": "-4", "effeciency": "95%", "team_coordination": "98%"}}, {"id": "d2ba77f2-cd59-4eba-874a-5d80ee7b0637", "title": "Maintenance Management", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-3", "effeciency": "92%", "team_coordination": "90%"}}, {"id": "25c648bd-52f1-4e9b-bf25-dec07541a6ef", "title": "Depreciation & Financial Management", "level": "M2", "consolidated": {"total_gos": "5", "internal_elimination": "-1", "effeciency": "90%", "team_coordination": "99%"}}, {"id": "6799b6f6-0cbb-4f25-a077-2bd86309514c", "title": "Asset Lifecycle Management", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-2", "effeciency": "89%", "team_coordination": "92%"}}, {"id": "f147a9fc-e9c3-4fec-8434-1b5a2170d950", "title": "Asset Acquisition Management", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-3", "effeciency": "81%", "team_coordination": "97%"}}, {"id": "bb5f2f06-78b4-4b2a-ab47-455738162728", "title": "Asset Deployment Management", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-1", "effeciency": "86%", "team_coordination": "96%"}}, {"id": "56ea014c-b2a8-46da-bd57-ffb4cfdcaa93", "title": "Asset Performance Monitoring", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-2", "effeciency": "91%", "team_coordination": "96%"}}, {"id": "d1a57d89-e044-446d-be1a-fa8156dcc215", "title": "Asset Optimization Management", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-3", "effeciency": "95%", "team_coordination": "96%"}}, {"id": "eb3ce227-098c-4c43-93c8-aea49940ace0", "title": "Asset Replacement Planning", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-5", "effeciency": "87%", "team_coordination": "96%"}}, {"id": "050838eb-7fa6-4f8d-90b9-d213ca674550", "title": "Asset End-of-Life Management", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-5", "effeciency": "95%", "team_coordination": "93%"}}, {"id": "39c0057f-829a-4fb4-96cb-70ecc944bac5", "title": "Lifecycle Analytics and Reporting", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-5", "effeciency": "80%", "team_coordination": "94%"}}, {"id": "ea105940-e033-4506-b47f-a99046eec109", "title": "Compliance & Audit Management", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-2", "effeciency": "95%", "team_coordination": "90%"}}, {"id": "ebd84f90-b57b-4618-86dc-44c5cd7c4687", "title": "Compliance Monitoring and Tracking", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-5", "effeciency": "82%", "team_coordination": "96%"}}, {"id": "6aa3b18c-f19f-4428-abde-67361bc5669a", "title": "Internal Audit Planning", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-2", "effeciency": "82%", "team_coordination": "99%"}}, {"id": "6f4551dd-06af-466d-9112-afec0f6d8feb", "title": "Audit Execution Management", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-3", "effeciency": "95%", "team_coordination": "97%"}}, {"id": "9aa69d97-38a7-4392-bc6b-356707e02b12", "title": "External Audit Coordination", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-4", "effeciency": "89%", "team_coordination": "98%"}}, {"id": "5e9c844e-b237-4e6b-a2cc-0434db651d52", "title": "Compliance Reporting Management", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-5", "effeciency": "89%", "team_coordination": "97%"}}, {"id": "4ea61174-9c6d-428d-aff9-5ea8f7c9a5e6", "title": "Risk Assessment and Management", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-5", "effeciency": "85%", "team_coordination": "93%"}}, {"id": "7e6cd14e-5055-4f7b-91e6-c34dbea5fefc", "title": "Onboarding Management", "level": "M2", "consolidated": {"total_gos": "11", "internal_elimination": "-3", "effeciency": "89%", "team_coordination": "99%"}}, {"id": "70384cb1-e5ce-4e1f-9eb4-c804f65b1dbe", "title": "Employee Life Cycle Management", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-2", "effeciency": "92%", "team_coordination": "92%"}}, {"id": "140987db-214b-4c90-a7e9-8870acd7dd35", "title": "Leave & Attendance Management", "level": "M2", "consolidated": {"total_gos": "12", "internal_elimination": "-2", "effeciency": "85%", "team_coordination": "99%"}}, {"id": "77053f16-e968-429e-bc18-e38227434a43", "title": "Employee Records Management", "level": "M2", "consolidated": {"total_gos": "11", "internal_elimination": "-4", "effeciency": "92%", "team_coordination": "96%"}}, {"id": "9ae28aa0-74a1-4185-87d5-b9f36846f3ed", "title": "Recruitment Management", "level": "M2", "consolidated": {"total_gos": "18", "internal_elimination": "-2", "effeciency": "94%", "team_coordination": "91%"}}, {"id": "48a7b23e-6823-4b3e-8558-ff4150075d23", "title": "Learning and Development", "level": "M2", "consolidated": {"total_gos": "16", "internal_elimination": "-2", "effeciency": "82%", "team_coordination": "90%"}}, {"id": "b3f7610d-7624-4a0f-96d2-149a73a00e7d", "title": "Innovation Pipeline Management", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-2", "effeciency": "90%", "team_coordination": "93%"}}, {"id": "140e595c-4b29-4d64-a5fa-502c7be512ee", "title": "Strategic Planning & Roadmapping", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-5", "effeciency": "94%", "team_coordination": "94%"}}, {"id": "64f9fead-5f7a-4bab-88a9-800eb57a9531", "title": "Market Research & Analysis", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-3", "effeciency": "85%", "team_coordination": "99%"}}, {"id": "c5a59d5e-39ab-4627-97e5-3656c79f6155", "title": "Technology Assessment", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-1", "effeciency": "95%", "team_coordination": "90%"}}, {"id": "d6f211bc-35a3-408e-992a-6749614a26fa", "title": "Innovation Lab Management", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-3", "effeciency": "91%", "team_coordination": "97%"}}, {"id": "aec7dea7-8ed2-450f-bf2e-75853523e591", "title": "Strategic Partnerships", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-4", "effeciency": "83%", "team_coordination": "90%"}}, {"id": "676964ed-f78d-4721-afff-a22244ee6b65", "title": "Competitive Intelligence", "level": "M2", "consolidated": {"total_gos": "7", "internal_elimination": "-5", "effeciency": "90%", "team_coordination": "98%"}}, {"id": "26cac774-58a8-4356-b9fb-df781526c0ac", "title": "Innovation Funding & Investment", "level": "M2", "consolidated": {"total_gos": "4", "internal_elimination": "-1", "effeciency": "95%", "team_coordination": "99%"}}, {"id": "c4ca03f0-a7d3-46c7-84e4-d6b7041fe629", "title": "Strategic Performance Tracking", "level": "M2", "consolidated": {"total_gos": "7", "internal_elimination": "-4", "effeciency": "90%", "team_coordination": "99%"}}, {"id": "99d8c06c-d4c4-49e2-8931-b5b702881c70", "title": "Innovation Process Management", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-4", "effeciency": "82%", "team_coordination": "96%"}}, {"id": "ef898bb0-3db4-435f-9819-4d73aced50b4", "title": "Contract Management and Review", "level": "M2", "consolidated": {"total_gos": "7", "internal_elimination": "-5", "effeciency": "94%", "team_coordination": "90%"}}, {"id": "cc5428bf-adda-4759-a324-e4498d996197", "title": "Regulatory Compliance", "level": "M2", "consolidated": {"total_gos": "6", "internal_elimination": "-4", "effeciency": "95%", "team_coordination": "95%"}}, {"id": "cefa8bae-21e3-4856-a31a-b2c6a21748b2", "title": "Risk Management and Assessment", "level": "M2", "consolidated": {"total_gos": "5", "internal_elimination": "-5", "effeciency": "86%", "team_coordination": "92%"}}, {"id": "d362aa44-2327-456a-9ab6-8f104eb6a3de", "title": "Corporate Governance", "level": "M2", "consolidated": {"total_gos": "4", "internal_elimination": "-2", "effeciency": "82%", "team_coordination": "95%"}}, {"id": "33dd1aa1-71ab-4686-9c70-553b0f4f8c96", "title": "Intellectual Property Management", "level": "M2", "consolidated": {"total_gos": "2", "internal_elimination": "-3", "effeciency": "91%", "team_coordination": "97%"}}, {"id": "c76a85d7-e194-4cec-91cf-1c9731f4fe5a", "title": "Litigation Management", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-2", "effeciency": "93%", "team_coordination": "98%"}}, {"id": "cc4ab64e-1e6b-45f3-9773-9f9800f240e3", "title": "Policy Development and Implementation", "level": "M2", "consolidated": {"total_gos": "6", "internal_elimination": "-2", "effeciency": "88%", "team_coordination": "90%"}}, {"id": "230dd19d-65ba-4ff5-bedc-b6cc4f8662b1", "title": "Data Privacy and Protection", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-3", "effeciency": "80%", "team_coordination": "95%"}}, {"id": "c00286d9-366d-4c52-a979-dc269632d0ee", "title": "Third-Party/Vendor Due Diligence", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-5", "effeciency": "89%", "team_coordination": "98%"}}, {"id": "cc70497d-0152-4fef-b0a4-18d9cb8515df", "title": "Whistleblower & Ethics Hotline Management", "level": "M2", "consolidated": {"total_gos": "7", "internal_elimination": "-2", "effeciency": "93%", "team_coordination": "93%"}}, {"id": "1c9df0e6-9fdf-42a1-9356-092ee2e82cd4", "title": "Legal Hold & E-Discovery", "level": "M2", "consolidated": {"total_gos": "9", "internal_elimination": "-3", "effeciency": "80%", "team_coordination": "90%"}}, {"id": "a8a6db0f-3a4e-47e3-93af-636af3334ade", "title": "Trade Compliance", "level": "M2", "consolidated": {"total_gos": "9", "internal_elimination": "-1", "effeciency": "83%", "team_coordination": "95%"}}, {"id": "848735c2-aa0d-4a3a-b27d-cfcea83d7dc0", "title": "License and Permit Management", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-5", "effeciency": "88%", "team_coordination": "98%"}}, {"id": "2e3abe7a-4544-4fad-8bf3-87aaa214ec28", "title": "Customer Master Management", "level": "M2", "consolidated": {"total_gos": "5", "internal_elimination": "-1", "effeciency": "88%", "team_coordination": "97%"}}, {"id": "8097e9d0-fa88-4905-9aed-7239311d6ba2", "title": "Pricing and Billing", "level": "M2", "consolidated": {"total_gos": "9", "internal_elimination": "-4", "effeciency": "89%", "team_coordination": "96%"}}, {"id": "3ba91dce-75c8-4440-bc30-93ad111ed70c", "title": "Credit Management", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-4", "effeciency": "88%", "team_coordination": "97%"}}, {"id": "e1f81625-7a0f-4f8d-a19b-78f785d058bb", "title": "Order Management", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-3", "effeciency": "92%", "team_coordination": "94%"}}, {"id": "bd819174-4925-44ec-9d56-d5a4cceafd4e", "title": "Customer Receivables", "level": "M2", "consolidated": {"total_gos": "9", "internal_elimination": "-1", "effeciency": "92%", "team_coordination": "92%"}}, {"id": "8eec5552-9d90-492e-a3c2-ef92207e3e04", "title": "Collections Management", "level": "M2", "consolidated": {"total_gos": "9", "internal_elimination": "-4", "effeciency": "91%", "team_coordination": "98%"}}, {"id": "4314bd0f-766e-4d8d-bd53-a8b8a1b2db9d", "title": "GST Compliance", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-3", "effeciency": "95%", "team_coordination": "99%"}}, {"id": "ce04ea39-00fc-4cc8-9c27-25d6f6633374", "title": "TDS Management", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-5", "effeciency": "84%", "team_coordination": "95%"}}, {"id": "e2f99a0e-af42-479f-99cb-4ca8992149c4", "title": "Statutory Reporting", "level": "M2", "consolidated": {"total_gos": "7", "internal_elimination": "-3", "effeciency": "88%", "team_coordination": "98%"}}, {"id": "6358b8db-4e61-41a0-b15c-7c83cf4aa988", "title": "Travel Reimbursement", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-3", "effeciency": "87%", "team_coordination": "95%"}}, {"id": "50e4f885-1c28-4746-a988-ad6399b0a548", "title": "Security Management", "level": "M2", "consolidated": {"total_gos": "6", "internal_elimination": "-3", "effeciency": "89%", "team_coordination": "93%"}}, {"id": "485cafbc-e72c-4840-8eda-d585912351e6", "title": "Purchase Requisition Management", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-3", "effeciency": "94%", "team_coordination": "91%"}}, {"id": "62c03d36-c3e7-4c75-9527-89ef831dfd4e", "title": "Request for Quotation (RFQ) Management", "level": "M2", "consolidated": {"total_gos": "9", "internal_elimination": "-3", "effeciency": "82%", "team_coordination": "99%"}}, {"id": "1350df94-490d-47ca-af35-a4f528a7da4e", "title": "Purchase Order (PO) Management", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-3", "effeciency": "85%", "team_coordination": "95%"}}, {"id": "95d43e9e-33f5-4b76-98e8-19437f73f63b", "title": "Goods Receipt & Inspection", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-1", "effeciency": "89%", "team_coordination": "90%"}}, {"id": "cee2ed75-815b-4aa2-a7bb-aa776892faff", "title": "3-Way / 2-Way Matching", "level": "M2", "consolidated": {"total_gos": "9", "internal_elimination": "-5", "effeciency": "84%", "team_coordination": "96%"}}, {"id": "6c7c227e-a42b-4b87-be44-4bd7fd3710be", "title": "Invoice Processing", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-1", "effeciency": "91%", "team_coordination": "91%"}}, {"id": "ecdee14a-df73-4b98-9130-7758fa5d4032", "title": "Payment Processing", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-4", "effeciency": "83%", "team_coordination": "96%"}}, {"id": "7957599e-f1d6-4fc3-bca0-23fc7e8a7670", "title": "Accounts Payable (AP) Integration", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-1", "effeciency": "93%", "team_coordination": "95%"}}, {"id": "fdad920d-025b-4eee-b6d9-90c359e846a1", "title": "P2P Workflow and Approvals", "level": "M2", "consolidated": {"total_gos": "5", "internal_elimination": "-2", "effeciency": "83%", "team_coordination": "98%"}}, {"id": "c114d1a1-9305-478c-8470-5c74e7bd0b9d", "title": "P2P Master Data Management", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-1", "effeciency": "90%", "team_coordination": "90%"}}, {"id": "8a2658e2-6121-4ef2-a2f0-9e9c7f193851", "title": "P2P Compliance and Audit Trail", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-5", "effeciency": "84%", "team_coordination": "98%"}}, {"id": "7ce1b7e1-20d0-499e-a3d7-5f308ab60018", "title": "Dispute and Exception Management", "level": "M2", "consolidated": {"total_gos": "20", "internal_elimination": "-3", "effeciency": "95%", "team_coordination": "93%"}}, {"id": "076bba5d-6782-43ce-9080-b4f13dbeeb80", "title": "Contract Administration", "level": "M2", "consolidated": {"total_gos": "5", "internal_elimination": "-1", "effeciency": "84%", "team_coordination": "99%"}}, {"id": "a1f21d54-ddc7-4e93-8318-8fae4401882c", "title": "Product Roadmap Planning", "level": "M2", "consolidated": {"total_gos": "7", "internal_elimination": "-3", "effeciency": "83%", "team_coordination": "93%"}}, {"id": "b8e50d4b-eb24-4fa5-9e9c-e5bd2c7fc54d", "title": "Feature Request Management", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-1", "effeciency": "93%", "team_coordination": "95%"}}, {"id": "5da6ba01-f61e-461e-b8ff-2ba0be5b2020", "title": "Product Launch Coordination", "level": "M2", "consolidated": {"total_gos": "7", "internal_elimination": "-3", "effeciency": "90%", "team_coordination": "99%"}}, {"id": "256a7367-bf01-49cb-92d0-111de61ce5e4", "title": "Customer Feedback Management", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-3", "effeciency": "84%", "team_coordination": "94%"}}, {"id": "92356034-3043-4f30-be0b-b3dc7b6cc07e", "title": "Product Analytics Dashboard", "level": "M2", "consolidated": {"total_gos": "9", "internal_elimination": "-3", "effeciency": "81%", "team_coordination": "95%"}}, {"id": "389c44f7-3b12-4f77-bc8f-80e8864ed8eb", "title": "API Management Center", "level": "M2", "consolidated": {"total_gos": "6", "internal_elimination": "-3", "effeciency": "85%", "team_coordination": "97%"}}, {"id": "caaed363-3265-4c53-9a44-58e5cf28c23a", "title": "Product Documentation Center", "level": "M2", "consolidated": {"total_gos": "9", "internal_elimination": "-2", "effeciency": "88%", "team_coordination": "94%"}}, {"id": "9f9608df-04cc-4977-aea1-32ad49585ada", "title": "Release Planning Board", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-5", "effeciency": "83%", "team_coordination": "92%"}}, {"id": "d838c32b-e078-4475-a264-3a467f71c103", "title": "Technical Specification Management", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-3", "effeciency": "88%", "team_coordination": "99%"}}, {"id": "72ee9bd8-7123-4a47-b0d0-3cbcd5aac5f7", "title": "Product Performance Monitoring", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-4", "effeciency": "87%", "team_coordination": "97%"}}, {"id": "1977f08a-a052-419d-a56f-a974d2e75e08", "title": "Cross-Platform Coordination", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-4", "effeciency": "85%", "team_coordination": "97%"}}, {"id": "d6e2594b-42e9-432a-9814-86360345313b", "title": "Product Configuration Center", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-4", "effeciency": "80%", "team_coordination": "92%"}}, {"id": "eb06afa6-070a-4792-accd-25c094b809bb", "title": "Design File Management", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-2", "effeciency": "89%", "team_coordination": "95%"}}, {"id": "450419a4-ccb7-460b-9f6e-847d628565e0", "title": "User Research Center", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-1", "effeciency": "95%", "team_coordination": "97%"}}, {"id": "e4dc90ac-69ec-416b-be62-e134519f1063", "title": "User Testing Lab", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-4", "effeciency": "83%", "team_coordination": "93%"}}, {"id": "fb89401f-0adb-4850-bf52-9f446286c5ac", "title": "Design Review & Approval", "level": "M2", "consolidated": {"total_gos": "9", "internal_elimination": "-1", "effeciency": "88%", "team_coordination": "94%"}}, {"id": "d112fca5-8945-45e6-b1c2-bab841494a30", "title": "A/B Testing Management", "level": "M2", "consolidated": {"total_gos": "8", "internal_elimination": "-5", "effeciency": "95%", "team_coordination": "93%"}}, {"id": "4d6420f8-ca58-42a3-9f0b-41d53376ef53", "title": "Accessibility Check Center", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-5", "effeciency": "94%", "team_coordination": "92%"}}, {"id": "bf290f5d-ebdc-4d1b-b65b-bbf9c8620ff5", "title": "User Behavior Analytics", "level": "M2", "consolidated": {"total_gos": "10", "internal_elimination": "-3", "effeciency": "81%", "team_coordination": "96%"}}, {"id": "9507f1f7-b9aa-4084-8888-eb8bf8aa7fbb", "title": "Project Management", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-3", "effeciency": "87%", "team_coordination": "92%"}}, {"id": "43d17f5a-9bbc-4ee7-b08d-ea1217228b14", "title": "Sprint Management", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-4", "effeciency": "87%", "team_coordination": "98%"}}, {"id": "eb640d46-1981-46cc-a205-2a1e23157099", "title": "Task Management", "level": "M2", "consolidated": {"total_gos": "1", "internal_elimination": "-3", "effeciency": "81%", "team_coordination": "91%"}}, {"id": "ed511d46-0af9-45da-b386-a2a57301de9a", "title": "Code Management", "level": "M2", "consolidated": {"total_gos": "3", "internal_elimination": "-5", "effeciency": "89%", "team_coordination": "95%"}}, {"id": "01e9bb08-e9bd-499c-b04c-c1cd0e035737", "title": "Testing Management", "level": "M2", "consolidated": {"total_gos": "2", "internal_elimination": "-3", "effeciency": "83%", "team_coordination": "99%"}}, {"id": "9652f74e-2aec-4ee9-9640-c4d4ddf5843f", "title": "Bug Management", "level": "M2", "consolidated": {"total_gos": "3", "internal_elimination": "-5", "effeciency": "91%", "team_coordination": "98%"}}, {"id": "087ab56c-19a0-4e61-b80c-173644005cff", "title": "Release Management", "level": "M2", "consolidated": {"total_gos": "4", "internal_elimination": "-1", "effeciency": "80%", "team_coordination": "97%"}}, {"id": "769df9a2-866d-4177-9706-3b67f844e3ce", "title": "Documentation Management", "level": "M2", "consolidated": {"total_gos": "3", "internal_elimination": "-2", "effeciency": "88%", "team_coordination": "95%"}}, {"id": "5361bb7d-986a-4532-8a5c-d0b9c650af6a", "title": "Performance Monitoring", "level": "M2", "consolidated": {"total_gos": "5", "internal_elimination": "-5", "effeciency": "83%", "team_coordination": "92%"}}, {"id": "cbd0b2aa-535a-4d36-98c9-c35e0dcb4b7f", "title": "Inbound Processing", "level": "M2", "consolidated": {"total_gos": "6", "internal_elimination": "-3", "effeciency": "93%", "team_coordination": "95%"}}, {"id": "a2e7c713-806f-4c30-a500-7ed1905be97f", "title": "Putaway Inventory Management", "level": "M2", "consolidated": {"total_gos": "12", "internal_elimination": "-2", "effeciency": "95%", "team_coordination": "90%"}}, {"id": "53dae9dd-4077-42db-bf07-2c9fa95dbe4e", "title": "Packing", "level": "M2", "consolidated": {"total_gos": "9", "internal_elimination": "-3", "effeciency": "81%", "team_coordination": "93%"}}, {"id": "9bbe078e-bde8-44f6-8911-39d4574354c5", "title": "Outbound Dispatch", "level": "M2", "consolidated": {"total_gos": "5", "internal_elimination": "-3", "effeciency": "88%", "team_coordination": "96%"}}, {"id": "4ca4413f-7acf-4000-8902-ef522e14bc01", "title": "Cycle Counting / Inventory Audit", "level": "M2", "consolidated": {"total_gos": "5", "internal_elimination": "-1", "effeciency": "82%", "team_coordination": "94%"}}, {"id": "3020a525-0cfa-4b2e-acc8-4c61678ecb12", "title": "Returns Processing", "level": "M2", "consolidated": {"total_gos": "4", "internal_elimination": "-5", "effeciency": "84%", "team_coordination": "94%"}}, {"id": "b793f571-f20e-400f-a407-6468ce1a6d18", "title": "Slotting Optimization", "level": "M2", "consolidated": {"total_gos": "4", "internal_elimination": "-3", "effeciency": "95%", "team_coordination": "94%"}}, {"id": "60c4d12f-27b4-4145-9133-24ef117f6b8d", "title": "Warehouse Transfer", "level": "M2", "consolidated": {"total_gos": "4", "internal_elimination": "-4", "effeciency": "84%", "team_coordination": "98%"}}, {"id": "4acde41b-5256-4935-afb4-e94d76cdefbf", "title": "Logistics Management", "level": "M2", "consolidated": {"total_gos": "7", "internal_elimination": "-1", "effeciency": "89%", "team_coordination": "90%"}}]