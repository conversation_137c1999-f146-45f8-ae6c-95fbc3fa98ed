{"app": {"name": "NSL", "version": "Version {version}"}, "common": {"loading": "Chargement...", "error": "<PERSON><PERSON><PERSON>", "success": "Su<PERSON>ès", "cancel": "Annuler", "save": "Enregistrer", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "close": "<PERSON><PERSON><PERSON>", "back": "Retour", "next": "Suivant", "submit": "So<PERSON><PERSON><PERSON>", "required": "Obligatoire", "optional": "Facultatif", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "sort": "<PERSON><PERSON>", "view": "Voir", "create": "<PERSON><PERSON><PERSON>", "update": "Mettre à jour", "details": "Détails", "noData": "<PERSON><PERSON><PERSON> donnée disponible", "retry": "<PERSON><PERSON><PERSON><PERSON>", "ok": "OK", "enterValue": "<PERSON><PERSON> une valeur", "pageNotFound": "Page non trouvée", "pageNotFoundMessage": "La page {pageName} n'a pas été trouvée.", "start": "Commencer", "notAvailable": "Non disponible"}, "auth": {"login": "Connexion", "register": "S'inscrire", "logout": "Déconnexion", "email": "Email", "password": "Mot de passe", "confirmPassword": "Confirmer le mot de passe", "username": "Nom d'utilisateur", "firstName": "Prénom", "lastName": "Nom", "mobile": "Téléphone mobile", "organization": "Organisation", "role": "R<PERSON><PERSON>", "fullName": "Nom complet", "rememberMe": "Se souvenir de moi", "forgotPassword": "Mot de passe oublié ?", "welcomeBack": "Bienvenue à nouveau", "pleaseSignIn": "Veuillez vous connecter pour continuer", "dontHaveAccount": "Vous n'avez pas de compte ?", "alreadyHaveAccount": "Vous avez déjà un compte ?", "alreadyRegistered": "Déjà inscrit ? Cliquez ici", "notRegistered": "Pas encore inscrit ? Cliquez ici", "profilePicture": "Photo de profil", "uploadProfilePicture": "Télécharger une photo de profil", "registrationSuccess": "Votre compte a été créé avec succès. Veuillez vous connecter avec vos identifiants.", "createAccount": "<PERSON><PERSON><PERSON> un compte", "signIn": "Se connecter", "signUp": "S'inscrire", "resetPassword": "Réinitialiser le mot de passe", "validation": {"emailRequired": "L'email est obligatoire", "emailInvalid": "Veuillez saisir une adresse email valide", "passwordRequired": "Le mot de passe est obligatoire", "passwordTooShort": "Le mot de passe doit contenir au moins 8 caractères", "passwordsDoNotMatch": "Les mots de passe ne correspondent pas", "usernameRequired": "Le nom d'utilisateur est obligatoire", "firstNameRequired": "Le prénom est obligatoire", "lastNameRequired": "Le nom est obligatoire", "organizationRequired": "L'organisation est obligatoire", "roleRequired": "Le rôle est obligatoire", "mobileRequired": "Le numéro de téléphone mobile est obligatoire", "mobileInvalid": "Veuillez saisir un numéro de téléphone mobile valide"}, "localAccount": "Compte local", "loggingOut": "Déconnexion en cours...", "signOutOfAccount": "Se déconnecter de votre compte"}, "navigation": {"home": "Accueil", "chat": "Cha<PERSON>", "build": "Construire", "transact": "Transaction", "myTransactions": "Mes transactions", "settings": "Paramètres", "profile": "Profil", "logout": "Déconnexion", "dashboard": "Tableau de bord", "components": "Composants", "uiComponents": "Composants UI", "drawer": {"appName": "NSL"}, "logoutConfirmation": "Êtes-vous sûr de vouloir vous déconnecter ?", "helpComingSoon": "La section d'aide arrive bi<PERSON><PERSON><PERSON> !", "widgetBinder": "Lieur de widgets", "help": "Aide", "code": "Code"}, "profile": {"title": "Profil", "editProfile": "Modifier le profil", "changePassword": "Changer le mot de passe", "personalInfo": "Informations personnelles", "contactInfo": "Coordonnées", "saveChanges": "Enregistrer les modifications", "discardChanges": "Annuler les modifications", "userInfoNotAvailable": "Informations utilisateur non disponibles", "profileInformation": "Informations de profil", "accountInformation": "Informations du compte", "refreshProfile": "Actualiser le profil", "fullName": "Nom complet", "username": "Nom d'utilisateur", "emailAddress": "<PERSON><PERSON><PERSON> email", "mobileNumber": "Numéro de téléphone mobile", "role": "R<PERSON><PERSON>", "organization": "Organisation", "userId": "ID utilisateur", "accountStatus": "Statut du compte", "tenantId": "ID locataire", "roles": "<PERSON><PERSON><PERSON>", "organizationUnits": "Unités d'organisation", "authProvider": "Fournisseur d'authentification", "notProvided": "Non fourni", "notAvailable": "Non disponible", "editProfileComingSoon": "La fonctionnalité de modification de profil arrive bientôt", "viewProfileDetails": "Voir les détails de votre profil"}, "settings": {"title": "Paramètres", "theme": "Thème", "darkMode": "Mode sombre", "darkModeDescription": "Basculer entre les thèmes clair et sombre", "language": "<PERSON><PERSON>", "languageDescription": "Changer la langue de l'application", "notifications": "Notifications", "account": "<PERSON><PERSON><PERSON>", "about": "À propos", "help": "Aide", "feedback": "Commentaires", "saveSuccess": "Paramètres enregistrés avec succès", "saveChanges": "Enregistrer les modifications", "appearance": "Apparence", "privacy": "Confidentialité", "advanced": "<PERSON><PERSON><PERSON>", "uiSettings": "Paramètres d'interface", "chatSettings": "Paramètres de chat", "fontSize": "Taille de police", "fontSizeDescription": "Ajuster la taille du texte dans toute l'application", "uiDensity": "Densité de l'interface", "uiDensityDescription": "Ajuster l'espacement entre les éléments de l'interface", "showTimestamps": "Afficher les horodatages", "showTimestampsDescription": "Afficher les horodatages pour chaque message", "showReadReceipts": "Afficher les accusés de lecture", "showReadReceiptsDescription": "Permettre aux autres de savoir quand vous avez lu leurs messages", "sendMessageOnEnter": "Envoyer le message avec Entrée", "sendMessageOnEnterDescription": "Appuyer sur Entrée pour envoyer des messages au lieu de Maj+Entrée", "version": "Version", "versionDescription": "Version actuelle de l'application", "light": "<PERSON>", "dark": "Sombre", "system": "Système", "comingSoon": "Paramètres de {feature} bientôt disponibles"}, "dashboard": {"title": "Tableau de bord", "welcome": "Bien<PERSON>ue, {name} !", "recentActivity": "Activité récente", "quickActions": "Actions rapides", "statistics": "Statistiques", "notifications": "Notifications", "viewAll": "Voir tout", "noRecentActivity": "Aucune activité récente", "noNotifications": "Aucune notification"}, "workflow": {"title": "Flux de travail", "createNew": "Créer un nouveau flux de travail", "createNewComingSoon": "<PERSON><PERSON>er un nouveau flux de travail - Bientôt disponible", "workflowDetails": "Détails du flux de travail", "workflowDetailsComingSoon": "Détails du flux de travail - Bientôt disponible", "status": {"active": "Actif", "draft": "Brouillon", "archived": "Archivé", "completed": "<PERSON><PERSON><PERSON><PERSON>"}, "types": {"approvalWorkflow": "Flux d'approbation", "documentProcessing": "Traitement de documents"}, "descriptions": {"approvalWorkflow": "Processus d'approbation standard avec plusieurs étapes", "documentProcessing": "Traitement et validation automatisés de documents"}, "nodeDetails": "<PERSON><PERSON><PERSON> du nœud", "closeDetails": "<PERSON><PERSON><PERSON> les d<PERSON>", "currentSolution": "Solution actuelle", "noActiveSolution": "Aucune solution active", "components": "Composants", "noComponentsDefined": "Aucun composant défini pour le moment", "solutionTemplates": "Modèles de solution", "templates": {"dataProcessing": "Pipeline de traitement de données", "orderManagement": "Système de gestion des commandes", "customerFeedback": "Analyse des retours clients"}}, "components": {"title": "Composants UI", "richTextEditor": "Éditeur de texte enrichi <PERSON>", "basicEditor": "Éditeur basique avec barre d'outils", "tryFormatting": "Essayez de formater ce texte en utilisant la barre d'outils ci-dessus.", "startTyping": "Commencez à taper ici..."}, "build": {"newSolution": "Nouvelle solution", "solutionName": "Nom de la solution", "description": "Description", "uploadFile": "Télécharger un fichier", "createSolution": "C<PERSON>er une solution", "create": "<PERSON><PERSON><PERSON>", "newProject": "Nouveau projet", "backToCreateMenu": "Retour au menu de création", "clearChat": "<PERSON><PERSON><PERSON><PERSON> le chat", "clearChatConfirmation": "Êtes-vous sûr de vouloir effacer l'historique du chat ?", "noSolutions": "Aucune solution disponible", "createYourFirstSolution": "Créez votre première solution", "solutions": "Solutions", "solution": "Solution", "solutionDetails": "Détails de la solution", "exportYAML": "Exporter en YAML", "suggestions": {"title": "Essayez de demander à NSL :", "createWorkflow": "Créer un flux de travail", "generateYAML": "Générer du YAML", "buildSolution": "Construire une solution"}, "examples": {"createWorkflow": "C<PERSON>er un flux de travail pour traiter les commandes clients", "generateYAML": "Générer du YAML pour un pipeline de transformation de données", "buildSolution": "Construire une solution pour la gestion des stocks"}}, "chat": {"newChat": "Nouveau chat", "newConversation": "Nouvelle conversation", "typeMessage": "Tapez un message...", "send": "Envoyer", "greeting": "Comment puis-je vous aider {greeting} ?", "clearChat": "<PERSON><PERSON><PERSON><PERSON> le chat", "clearChatConfirmation": "Êtes-vous sûr de vouloir effacer l'historique du chat ?", "cancel": "Annuler", "clear": "<PERSON><PERSON><PERSON><PERSON>", "chatWithNSL": "Discuter avec NSL...", "fetchingAnswer": "NSL recherche une réponse", "conversations": "Conversations", "history": "Historique", "searchConversations": "Rechercher des conversations", "recentChats": "<PERSON>ts récents", "chatCount": "{count} chats", "noConversations": "Pas encore de conversations", "noMessagesYet": "Pas encore de messages", "rename": "<PERSON>mmer", "renameConversation": "Renommer la conversation", "enterNewName": "Saisir un nouveau nom", "exportChat": "Exporter le chat", "context": "Contexte", "aiAssistant": "Assistant IA", "currentConversation": "Conversation actuelle", "topic": "Sujet :", "noSpecificTopic": "Aucun sujet spécifique détecté", "keyPoints": "Points clés :", "noContextAvailable": "• Aucun contexte disponible pour cette conversation", "relatedInformation": "Informations connexes", "noRelatedInformation": "Aucune information connexe disponible", "microphonePermissionRequired": "Permission de microphone requise", "microphonePermissionMessage": "Cette application a besoin d'accéder au microphone pour la fonctionnalité de synthèse vocale. Veuillez accorder la permission du microphone dans les paramètres de votre appareil.", "suggestions": {"codeReview": "Aidez-moi avec la revue de code", "explainConcept": "Expliquez ce concept", "debugCode": "Déboguez mon code"}}, "transaction": {"globalObjectives": "Objectifs globaux", "localObjectives": "Objectifs locaux", "status": "Statut", "id": "ID", "version": "Version", "viewDetails": "Voir les détails", "startWorkflow": "Démarrer le flux de travail", "completeWorkflow": "Terminer le flux de travail", "noObjectives": "Aucun objectif disponible", "transact": "Transaction", "myTransactions": "Mes transactions", "checkingExistingTransactions": "Vérification des transactions existantes...", "existingTransactions": "Transactions existantes", "existingTransactionsFor": "Transactions existantes pour {name}", "existingTransactionsQuestion": "Il existe des transactions. Voulez-vous les voir ?", "noExistingTransactions": "Aucune transaction existante trouvée", "startNewTransactionPrompt": "Démarrez une nouvelle transaction en utilisant le bouton ci-dessous", "startNewTransaction": "Démarrer une nouvelle transaction", "instanceId": "ID d'instance", "created": "<PERSON><PERSON><PERSON>", "updated": "Mis à jour", "resume": "Reprendre", "yes": "O<PERSON>", "no": "Non", "data": "<PERSON><PERSON><PERSON>", "timestamp": "Horodatage", "clearChat": "<PERSON><PERSON><PERSON><PERSON> le chat", "clearChatConfirmation": "Êtes-vous sûr de vouloir effacer l'historique du chat ?", "completed": "TERMINÉ", "pending": "EN ATTENTE", "failed": "ÉCHOUÉ", "greeting": "<PERSON><PERSON><PERSON>, {name}", "welcomeMessage": "Bienvenue au Centre de Transactions", "searchTransactions": "Rechercher des transactions...", "enterTransactionDetails": "Saisir les détails de la transaction...", "errorLoadingData": "Erreur lors du chargement des données : {error}", "objectiveId": "ID : {id}", "objectiveName": "Nom : {name}", "objectiveStatus": "Statut : {status}", "objectiveVersion": "Version : {version}", "objectiveDetails": "Détails de l'objectif", "startTransaction": "Démarrer la transaction", "startNewTransactionWith": "Démarrer une nouvelle transaction avec {name} ?", "errorLoadingTransactions": "Erreur lors du chargement des transactions", "loadingTransactions": "Chargement des transactions...", "noTransactionsFound": "Aucune transaction trouvée", "noFilteredTransactionsFound": "Aucune transaction {status} trouvée", "all": "Toutes", "newTransactionMessage": "Le formulaire de nouvelle transaction serait ici.", "editTransactionMessage": "La fonctionnalité d'édition de transaction serait ici.", "resumeTransactionMessage": "<PERSON><PERSON> naviguerait vers l'écran de détails du flux de travail pour reprendre la transaction.", "workerId": "ID de travailleur : {id}", "workflowId": "ID de flux de travail", "dateTime": "Date et heure", "lastUpdated": "Dernière mise à jour", "updatedDate": "Mis à jour : {date}", "formattedDate": "{date}", "totalLocalObjectives": "Total des objectifs locaux : {count}", "noLocalObjectiveDetails": "Aucun détail d'objectif local disponible", "localObjective": "Objectif local", "loCount": "{count} OL{plural}", "selectItemForDetails": "Sélectionnez un élément pour voir les détails", "globalObjectiveDetails": "Détails de l'objectif global", "transactionDetails": "Détails de la transaction", "groupedTransactionDetails": "Détails de transaction groupés", "tenantId": "ID de locataire", "resumeTransaction": "Reprendre la transaction"}, "home": {"typeYourReply": "Tapez votre réponse", "greeting": "Bonjour {name}, comment puis-je vous aider ?", "selectQuickMessage": "Veuillez sélectionner un type de message rapide avant d'envoyer", "selectQuickMessageHint": "Sélectionnez un type de message", "sendingMessage": "Envoi du message...", "loadingChatHistory": "Chargement de l'historique du chat...", "noChatHistory": "Aucun historique de chat trouvé", "askNSL": "<PERSON><PERSON>er à NSL", "nsl": "NSL", "solution": "Solution", "general": "Général", "internet": "Internet"}, "library": {"books": "12 Livres", "objects": "102 Objets", "solutions": "35 Solutions", "agents": "10 Agents", "pageTitle": "Ma Bibliothèque", "createButtonText": "<PERSON><PERSON><PERSON> un Livre"}, "mylibrary": {"buttonText": {"recent": "<PERSON><PERSON><PERSON>", "favourite": "<PERSON><PERSON><PERSON>"}, "cardText": {"discover": {"title": "Découvrir", "description": "Partagez votre secteur d'activité et les solutions dont vous avez besoin—notre IA NSL gérera la découverte complète de solutions et la construira adaptée à vos besoins."}, "develop": {"title": "Développer", "description": "Saisissez ou téléchargez votre exigence, et nous extrairons, développerons et affinerons votre solution avec des suggestions guidées par l'IA tout au long du processus."}, "publish": {"title": "Publier", "description": "La solution que vous avez sauvegardée est prête à être publiée. Testez la solution depuis l'onglet Test et une fois qu'elle fonctionne à 100% selon les attentes, publiez-la pour l'exécution"}}, "TableHeaderText": {"fileName": "Nom du Fichier", "lastOpened": "Dernière Ouverture", "sortWith": "<PERSON><PERSON>", "project": "Projet", "solution": "Solution", "object": "Objet", "role": "R<PERSON><PERSON>", "draft": "Brouillon", "published": "<PERSON><PERSON><PERSON>"}, "TableBodyText": {"type": "Type", "draft": "Brouillon", "published": "<PERSON><PERSON><PERSON>"}}, "sidemenu": {"chat": "Cha<PERSON>", "create": "<PERSON><PERSON><PERSON>", "myBusiness": "Mon Entreprise", "home": "Accueil", "collections": "Collections", "solutions": "Solutions", "records": "Enregistrements", "myTransactions": "Mes Transactions", "calendar": "<PERSON><PERSON><PERSON>", "notifications": "Notifications", "nslToJavaCode": "NSL vers Code Java", "myProfile": "Mon Profil", "maxNewPlan": "Nouveau Plan Max", "viewPlan": "Voir le Plan", "learnMore": "En Savoir Plus", "language": "<PERSON><PERSON>", "getHelp": "Obtenir de l'Aide", "settings": "Paramètres", "logout": "Déconnexion"}, "bookdetails": {"book": "Livre", "nameOfTheProject": "Nom du Projet", "description": "Description", "industry": "Industrie", "descriptionAboutTheProject": "Description du projet", "start": "Commencer"}, "websolution": {"books": "12 Livres", "objects": "102 Objets", "solutions": "35 Solutions", "pageTitle": "Ma Solution", "createButtonText": "<PERSON><PERSON>er une Solution"}, "webobject": {"pageTitle": "<PERSON><PERSON> Objet<PERSON>", "createButtonText": "<PERSON><PERSON><PERSON> un Objet"}, "webagent": {"pageTitle": "Agents", "createButtonText": "<PERSON><PERSON><PERSON> un Agent"}}