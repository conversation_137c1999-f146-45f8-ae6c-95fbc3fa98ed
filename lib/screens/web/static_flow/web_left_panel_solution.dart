import 'package:flutter/material.dart';
import 'package:nsl/models/object_creation_model.dart';
import 'package:nsl/providers/create_entity_provider.dart';
import 'package:nsl/providers/creation_provider.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/logger.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:nsl/models/solution/go_model.dart';
import 'my_library_service.dart';
import 'get_all_my_library_model.dart';
import 'package:nsl/providers/selected_object_provider.dart';
import 'package:nsl/providers/accordion_availability_provider.dart';
import 'package:nsl/providers/go_details_provider.dart';
import 'package:provider/provider.dart';

class WebLeftPanelSolution extends StatefulWidget {
  const WebLeftPanelSolution({super.key});

  @override
  State<WebLeftPanelSolution> createState() => _WebLeftPanelSolutionState();
}

class _WebLeftPanelSolutionState extends State<WebLeftPanelSolution>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedFilter = 'All'; // State for dropdown selection

  // API data for entities and attributes
  List<EntitiesMongoDraft> _entitiesObjects = [];
  List<RolesPostgre> _rolesObjects = [];
  List<GlobalObjectivesPostgre> _goObjects = [];
  Map<String, List<EntityAttributesMongoDraft>> _entityAttributes =
      {}; // Cache for entity attribute objects with full type safety
  Map<String, List<LocalObjectivesPostgre>> _goLocalObjectivesObjects = {};

  // UI state tracking (separate from model data)
  Map<String, bool> _entityExpandedState = {}; // Track entity expansion state
  Map<String, bool> _entityAttributesLoadedState =
      {}; // Track attributes loaded state
  Map<String, List<ObjectAttribute>> _entityAttributeNames =
      {}; // Cache attribute names for UI
  Map<String, bool> _goExpandedState = {}; // Track GO expansion state
  Map<String, bool> _goLocalObjectivesLoadedState = {}; // Track LO loaded state

  bool _isLoadingEntities = true;
  bool _isLoadingAttributes = false;
  bool _isLoadingRoles = true;
  bool _isLoadingGO = true;

  @override
  void initState() {
    super.initState();
    // Initialize with default tab index (Objects = 1)
    // _currentTabIndex = 1;
    _tabController = TabController(
        length: 3,
        vsync: this,
        initialIndex: Provider.of<CreationProvider>(context, listen: false)
            .currentMiddleScreen);

    // Add listener to track tab changes locally
    // _tabController.addListener(() {
    //   if (_tabController.indexIsChanging) {
    //     setState(() {
    //       _currentTabIndex = _tabController.index;
    //     });
    //   }
    // });

    _fetchAllLibraryData();
  }

  @override
  didUpdateWidget(oldWidget) {
    _tabController.index = Provider.of<CreationProvider>(context, listen: false)
        .currentMiddleScreen;
    super.didUpdateWidget(oldWidget);
  }

  /// Fetch all library data from API in a single call
  Future<void> _fetchAllLibraryData() async {
    setState(() {
      _isLoadingRoles = true;
      _isLoadingEntities = true;
      _isLoadingGO = true;
    });

    try {
      MyLibraryService myLibraryService = MyLibraryService();
      // Single API call to get all library data
      final libraryResponse = await myLibraryService.getAllLibraryData();

      if (libraryResponse != null &&
          libraryResponse.data?.dataLibrary != null) {
        final library = libraryResponse.data!.dataLibrary!;

        // Cache the library data for reuse
        _cachedLibraryData = library;

        // Process Roles - pure object-based storage
        final rolesObjects = <RolesPostgre>[];
        if (library.roles?.postgres != null) {
          for (final role in library.roles!.postgres!) {
            if (role.name != null) {
              rolesObjects.add(role);
            }
          }
        }

        if (library.roles?.mongoDrafts != null) {
          // Note: mongoDrafts for roles is List<dynamic>, need to handle appropriately
          for (final role in library.roles!.mongoDrafts!) {
            if (role.name != null) {
              rolesObjects.add(role);
            }
          }
        }

        // Process Entities - pure object-based storage
        final entitiesObjects = <EntitiesMongoDraft>[];
        if (library.entities?.postgres != null) {
          for (final entity in library.entities!.postgres!) {
            entitiesObjects.add(entity);
            // Initialize UI state for each entity
            _entityExpandedState[entity.entityId ?? ''] = false;
            _entityAttributesLoadedState[entity.entityId ?? ''] = false;
          }
        }
        if (library.entities?.mongoDrafts != null) {
          for (final entity in library.entities!.mongoDrafts!) {
            entitiesObjects.add(entity);
            // Initialize UI state for each entity
            _entityExpandedState[entity.entityId ?? ''] = false;
            _entityAttributesLoadedState[entity.entityId ?? ''] = false;
          }
        }

        // Process Global Objectives - pure object-based storage
        final goObjects = <GlobalObjectivesPostgre>[];
        if (library.globalObjectives?.postgres != null) {
          for (final go in library.globalObjectives!.postgres!) {
            if (go.name != null && go.goId != null) {
              goObjects.add(go);
              // Initialize UI state for each GO
              _goExpandedState[go.goId ?? ''] = false;
              _goLocalObjectivesLoadedState[go.goId ?? ''] = false;
            }
          }
        }

        if (library.globalObjectives?.mongoDrafts != null) {
          // Note: mongoDrafts for GO is List<dynamic>, need to handle appropriately
          for (final go in library.globalObjectives!.mongoDrafts!) {
            if (go.name != null && go.goId != null) {
              goObjects.add(go);
              // Initialize UI state for each GO
              _goExpandedState[go.goId ?? ''] = false;
              _goLocalObjectivesLoadedState[go.goId ?? ''] = false;
            }
          }
        }

        setState(() {
          // Store only strongly-typed objects
          _rolesObjects = rolesObjects;
          _entitiesObjects = entitiesObjects;
          _goObjects = goObjects;
          _isLoadingRoles = false;
          _isLoadingEntities = false;
          _isLoadingGO = false;
        });

        Logger.info('Successfully fetched all library data in single API call');
        Logger.info('Global Objectives found: ${goObjects.length}');
        // for (final go in goObjects) {
        //   Logger.info('  - GO: ${go.name} (ID: ${go.goId})');
        // }

        // Debug: Check if local objectives data exists
        if (library.localObjectives?.postgres != null) {
          Logger.info(
              'Local Objectives found: ${library.localObjectives!.postgres!.length}');
          for (final lo in library.localObjectives!.postgres!) {
            Logger.info('  - LO: ${lo.name} (GO ID: ${lo.goId})');
          }
        } else {
          Logger.info('No Local Objectives found in API response');
        }
      } else {
        // No fallback data - pure object-based approach
        setState(() {
          _rolesObjects = [];
          _entitiesObjects = [];
          _goObjects = [];
          _isLoadingRoles = false;
          _isLoadingEntities = false;
          _isLoadingGO = false;
        });
        Logger.info('No library data received from API');
      }
    } catch (e) {
      // Error handling - pure object-based approach
      setState(() {
        _rolesObjects = [];
        _entitiesObjects = [];
        _goObjects = [];
        _isLoadingRoles = false;
        _isLoadingEntities = false;
        _isLoadingGO = false;
      });
      Logger.info('Error fetching library data: $e');
    }
  }

  // Store the library data for reuse
  Library? _cachedLibraryData;

  /// Fetch local objectives for a specific GO from cached library data
  Future<void> _fetchGoLocalObjectives(String goId, int goIndex) async {
    if (_goLocalObjectivesObjects.containsKey(goId)) {
      // Use cached local objectives - update UI state
      setState(() {
        _goLocalObjectivesLoadedState[goId] = true;
      });
      return;
    }

    try {
      List<LocalObjectivesPostgre> localObjectivesObjects = [];

      // Use cached library data if available
      if (_cachedLibraryData?.localObjectives != null) {
        // Add postgres local objectives (published)
        if (_cachedLibraryData!.localObjectives!.postgres != null) {
          final postgresLOs = _cachedLibraryData!.localObjectives!.postgres!
              .where((lo) => lo.goId == goId)
              .toList();

          final filteredLOs = postgresLOs
              .where((lo) => lo.name != null && lo.name!.isNotEmpty)
              .toList();

          localObjectivesObjects.addAll(filteredLOs);
        }
        if (_cachedLibraryData!.localObjectives!.mongoDrafts != null) {
          final mongoDraftsLOs = _cachedLibraryData!
              .localObjectives!.mongoDrafts!
              .where((lo) => lo.goId == goId)
              .toList();

          final filteredLOs = mongoDraftsLOs
              .where((lo) => lo.name != null && lo.name!.isNotEmpty)
              .toList();

          localObjectivesObjects.addAll(filteredLOs);
        }

        Logger.info(
            'Found ${localObjectivesObjects.length} local objectives for GO $goId');
        for (final lo in localObjectivesObjects) {
          Logger.info(
              '  - LO: ${lo.name} (ID: ${lo.loId}, Status: ${lo.status})');
        }
      }

      // Debug: Logger.info detailed information about local objectives search
      Logger.info('Searching for Local Objectives with GO ID: $goId');
      Logger.info(
          'Total Local Objectives in cache: ${_cachedLibraryData?.localObjectives?.postgres?.length ?? 0}');

      // Cache strongly-typed objects
      _goLocalObjectivesObjects[goId] = localObjectivesObjects;

      setState(() {
        _goLocalObjectivesLoadedState[goId] = true;
      });

      Logger.info(
          'Successfully loaded ${localObjectivesObjects.length} local objectives for GO $goId');
    } catch (e) {
      setState(() {
        _goLocalObjectivesLoadedState[goId] = true;
      });
      Logger.info('Error fetching local objectives for GO $goId: $e');
    }
  }

  /// Build add icon with conditional enabling/disabling based on accordion availability
  Widget _buildConditionalAddIcon({
    required VoidCallback onTap,
    required String tooltipMessage,
  }) {
    return Consumer<AccordionAvailabilityProvider>(
      builder: (context, accordionProvider, child) {
        final isEnabled = true;
        // accordionProvider.canAddToAccordion;
        final iconColor = isEnabled ? Colors.grey : Colors.grey.shade400;
        final cursor =
            isEnabled ? SystemMouseCursors.click : SystemMouseCursors.forbidden;

        return MouseRegion(
          cursor: cursor,
          child: Tooltip(
            message: isEnabled
                ? tooltipMessage
                // ignore: dead_code
                : accordionProvider.getDisabledMessage(),
            child: GestureDetector(
              onTap: isEnabled
                  ? onTap
                  // ignore: dead_code
                  : () => _showDisabledMessage(
                      context, accordionProvider.getDisabledMessage()),
              child: Icon(
                Icons.add,
                size: 16,
                color: iconColor,
              ),
            ),
          ),
        );
      },
    );
  }

  /// Show message when add action is disabled
  void _showDisabledMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Check if user has unsaved changes in their current custom GO work
  bool _hasUnsavedChanges(BuildContext context) {
    final goDetailsProvider =
        Provider.of<GoDetailsProvider>(context, listen: false);

    // Check if there's a current GO model with data
    if (goDetailsProvider.currentGoModel != null) {
      final currentGoModel = goDetailsProvider.currentGoModel!;

      // Check if there's any meaningful work in progress
      bool hasCustomWork = false;

      // Check if solution name is filled
      if (goDetailsProvider.solutionController.text.trim().isNotEmpty) {
        hasCustomWork = true;
      }

      // Check if description is filled
      if (goDetailsProvider.descriptionController.text.trim().isNotEmpty) {
        hasCustomWork = true;
      }

      // Check if there are local objectives
      if (currentGoModel.localObjectivesList != null &&
          currentGoModel.localObjectivesList!.isNotEmpty) {
        hasCustomWork = true;
      }

      // Check if user is in a step beyond initial
      if (goDetailsProvider.currentStep != GoDetailsStep.initial) {
        hasCustomWork = true;
      }

      return hasCustomWork;
    }

    return false;
  }

  /// Show unsaved changes warning dialog
  Future<bool> _showUnsavedChangesWarning(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext dialogContext) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              title: Text(
                'Unsaved Changes',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.titleMedium(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              content: Text(
                'Current changes will be lost. Do you want to continue?',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black87,
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(dialogContext).pop(false),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.grey[600],
                  ),
                  child: Text(
                    'Cancel',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                    ),
                  ),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(dialogContext).pop(true),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF007AFF),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'Continue',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                    ),
                  ),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  /// Handle GO selection - populate data in the right panel with LO management
  void _handleGoSelection(BuildContext context, GlobalObjectivesPostgre go) {
    final goDetailsProvider =
        Provider.of<GoDetailsProvider>(context, listen: false);

    final goId = go.goId ?? '';

    // Ensure local objectives are loaded before populating
    if (!(_goLocalObjectivesLoadedState[goId] ?? false)) {
      // If local objectives are not loaded, load them first
      if (goId.isNotEmpty) {
        final goIndex = _goObjects.indexOf(go);
        _fetchGoLocalObjectives(goId, goIndex).then((_) {
          // Check if widget is still mounted before using context
          if (mounted) {
            // After loading, populate the GO data with proper LO structure
            _populateGoDetailsWithLOs(go, goDetailsProvider);
          }
        });
      }
    } else {
      // Local objectives are already loaded, populate directly
      _populateGoDetailsWithLOs(go, goDetailsProvider);
    }

    // Show success message
    final localObjectivesCount = _goLocalObjectivesObjects[goId]?.length ?? 0;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'GO "${go.name}" loaded successfully with $localObjectivesCount Local Objectives',
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Populate GO details with proper LO structure and management
  void _populateGoDetailsWithLOs(
      GlobalObjectivesPostgre go, GoDetailsProvider goDetailsProvider) {
    final goId = go.goId ?? '';

    // Get local objectives from strongly-typed objects
    final localObjectivesList = _goLocalObjectivesObjects[goId] ?? [];
    final localObjectiveNames = localObjectivesList
        .map((lo) => lo.name ?? '')
        .where((name) => name.isNotEmpty)
        .toList();

    // Create a GO data structure for compatibility with existing provider
    final goData = {
      'goId': go.goId ?? '',
      'name': go.name ?? 'Unknown GO',
      'description': go.description ?? '',
      'localObjectives': localObjectiveNames,
      'status': go.status ?? 'active',
    };

    // Create a modified GO data structure with string list for compatibility
    final modifiedGoData = Map<String, dynamic>.from(goData);
    modifiedGoData['localObjectives'] = localObjectiveNames;

    // Populate the GO details provider
    goDetailsProvider.populateFromGoSelection(modifiedGoData);

    // Debug logging for development
    debugPrint(
        'Successfully populated GO "${go.name}" with ${localObjectiveNames.length} Local Objectives:');
    for (int i = 0; i < localObjectiveNames.length; i++) {
      debugPrint('  - LO-${i + 1}: ${localObjectiveNames[i]}');
    }
  }

  /// Handle Local Objective selection - navigate to LO details view or append to custom GO
  void _handleLoSelection(
      BuildContext context,
      LocalObjectivesPostgre localObjective,
      int loIndex,
      GlobalObjectivesPostgre go) {
    final goDetailsProvider =
        Provider.of<GoDetailsProvider>(context, listen: false);

    // Check if we have a custom GO in progress (user is creating their own GO)
    final hasCustomGoInProgress = goDetailsProvider.currentGoModel != null &&
        goDetailsProvider.currentGoModel!.globalObjectives != null &&
        goDetailsProvider.currentGoModel!.globalObjectives!.name != null &&
        goDetailsProvider.currentGoModel!.globalObjectives!.name!.isNotEmpty;

    // if (hasCustomGoInProgress) {
    // Show dialog to ask user if they want to append this LO to their custom GO
    _showAppendLoToCustomGoDialog(
        context, localObjective, go, goDetailsProvider);
    // } else {
    // Standard behavior: load library GO and show LO details
    // _handleStandardLoSelection(
    // context, localObjective, loIndex, go, goDetailsProvider);
    // }
  }

  /// Handle standard LO selection (existing behavior)
  void _handleStandardLoSelection(
      BuildContext context,
      Map<String, dynamic> localObjective,
      int loIndex,
      GlobalObjectivesPostgre go,
      GoDetailsProvider goDetailsProvider) {
    // Ensure the GO is already populated in the provider
    if (goDetailsProvider.currentGoModel == null) {
      // If GO is not loaded, load it first
      _populateGoDetailsWithLOs(go, goDetailsProvider);
    }

    // Set the selected local objective for detailed view
    goDetailsProvider.setShowLocalObjectiveDetails(loIndex);

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'LO "${localObjective['name']}" selected for detailed configuration',
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );

    debugPrint(
        'Selected LO-${loIndex + 1}: ${localObjective['name']} for detailed view');
  }

  /// Show dialog to ask user if they want to append library LO to their custom GO
  void _showAppendLoToCustomGoDialog(
      BuildContext context,
      LocalObjectivesPostgre localObjective,
      GlobalObjectivesPostgre go,
      GoDetailsProvider goDetailsProvider) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          title: Text(
            'Append Library LO',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'You have a custom GO in progress. Would you like to append this library LO to your custom GO?',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: Colors.blue[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Library LO to append:',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.blue[800],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      localObjective.name ?? 'Unknown LO',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w600,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'From GO: ${go.name ?? 'Unknown GO'}',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: Text(
                'Cancel',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[600],
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                _appendLibraryLoToCustomGo(
                    context, localObjective, go, goDetailsProvider);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF0058FF),
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              child: Text(
                'Append LO',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Append library LO to the current custom GO
  void _appendLibraryLoToCustomGo(
      BuildContext context,
      LocalObjectivesPostgre localObjective,
      GlobalObjectivesPostgre go,
      GoDetailsProvider goDetailsProvider) {
    try {
      final currentGoModel = goDetailsProvider.currentGoModel;
      if (currentGoModel == null ||
          currentGoModel.localObjectivesList == null) {
        // List localObjectivesList =
        //     go['localObjectives'] as List<Map<String, dynamic>>? ?? [];
        List<LocalObjectivesPostgre> localObjectivesList = [localObjective];
        final localObjectiveNames = localObjectivesList
            .map((lo) => lo.name ?? '')
            .where((name) => name.isNotEmpty)
            .toList();

        // Create a GO data structure for compatibility with existing provider
        final goData = {
          'goId': go.goId ?? '',
          'name': go.name ?? 'Unknown GO',
          'description': go.description ?? '',
          'localObjectives': localObjectiveNames,
          'status': go.status ?? 'active',
        };

        // Create a modified GO data structure with string list for compatibility
        final modifiedGoData = Map<String, dynamic>.from(goData);
        modifiedGoData['localObjectives'] = localObjectiveNames;

        // Populate the GO details provider
        goDetailsProvider.populateFromGoSelection(modifiedGoData);

        // debugPrint('❌ Error: No custom GO model found to append LO to');
        return;
      } else {
        // Get current LO count for proper numbering
        final currentLoCount = currentGoModel.localObjectivesList!.length;
        final newLoNumber = currentLoCount + 1;

        // Create new LocalObjectivesList from library LO
        // Use workSource to track the source GO information
        final sourceInfo = 'Library GO: ${go.name} (${go.goId})';
        final newLo = LocalObjectivesList(
          loNumber: newLoNumber,
          name: localObjective.name,
          version: localObjective.version,
          status: localObjective.status,
          naturalLanguage: localObjective.naturalLanguage ?? 'Unknown LO',
          goId: go.goId ?? '',
          loId: localObjective.loId,
          roleTypes: null,
          terminal: false,
          pathwayData: null,
          workSource: sourceInfo, // Track source GO information
          workflowSource: 'library', // Mark as library-sourced
        );

        // Append to the current GO's local objectives list
        currentGoModel.localObjectivesList!.add(newLo);

        // Sync provider data
        goDetailsProvider.syncProviderDataToGoModel();

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Library LO "${localObjective.name}" appended as LO-$newLoNumber to your custom GO',
              style: const TextStyle(color: Colors.white),
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );

        debugPrint(
            '✅ Successfully appended library LO "${localObjective.name}" as LO-$newLoNumber to custom GO');
        debugPrint('   Source GO: ${go.name} (${go.goId})');
        debugPrint(
            '   Total LOs in custom GO: ${currentGoModel.localObjectivesList!.length}');
      }
    } catch (e) {
      debugPrint('❌ Error appending library LO to custom GO: $e');

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Failed to append library LO: $e',
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    }
  }

  /// Fetch attributes for a specific entity from cached library data
  Future<void> _fetchEntityAttributes(String entityId, int objectIndex) async {
    if (_entityAttributes.containsKey(entityId)) {
      // Use cached attributes - update UI state
      final attributeNames = _entityAttributes[entityId]!.map(
        (attr) {
          return ObjectAttribute(
            id: attr.id,
            name: attr.name,
            displayName: attr.displayName,
            dataType: attr.datatype,
            required: attr.required,
            unique: attr.isUnique,
            defaultType: attr.defaultType,
            defaultValue: attr.defaultValue,
            description: attr.description,
            helperText: attr.helperText,
            // enumValues:attr.enumValues,
            // validation,
            isPrimaryKey: attr.isPrimaryKey ?? false,
            isForeignKey: attr.isForeignKey ?? false,
            status: attr.status,
          );
        },
      ).toList();
      setState(() {
        _entityAttributeNames[entityId] = attributeNames;
        _entityAttributesLoadedState[entityId] = true;
        _isLoadingAttributes = false;
      });
      return;
    }

    setState(() {
      _isLoadingAttributes = true;
    });

    try {
      List<ObjectAttribute> attributeNames = [];

      // Use cached library data if available
      if (_cachedLibraryData?.entityAttributes != null) {
        // Optimize by using a more efficient approach
        final postgresAttributes =
            _cachedLibraryData!.entityAttributes!.postgres ??
                <EntityAttributesMongoDraft>[];
        final mongoDraftAttributes =
            _cachedLibraryData!.entityAttributes!.mongoDrafts ??
                <EntityAttributesMongoDraft>[];

        // Combine and filter in one pass for better performance
        final entityAttributes = <EntityAttributesMongoDraft>[];

        // Add postgres attributes that match the entityId
        for (final attr in postgresAttributes) {
          if (attr.entityId == entityId) {
            entityAttributes.add(attr);
          }
        }

        // Add mongo draft attributes that match the entityId
        for (final attr in mongoDraftAttributes) {
          if (attr.entityId == entityId) {
            entityAttributes.add(attr);
          }
        }

        // Extract attribute names for UI display
        attributeNames = entityAttributes.map(
          (attr) {
            return ObjectAttribute(
              id: attr.id,
              name: attr.name,
              displayName: attr.displayName,
              dataType: attr.datatype,
              required: attr.required,
              unique: attr.isUnique,
              defaultType: attr.defaultType,
              defaultValue: attr.defaultValue,
              description: attr.description,
              helperText: attr.helperText,
              // enumValues:attr.enumValues,
              // validation,
              isPrimaryKey: attr.isPrimaryKey ?? false,
              isForeignKey: attr.isForeignKey ?? false,
              status: attr.status,
            );
          },
        ).toList();

        // Cache the EntityAttributesMongoDraft objects directly
        _entityAttributes[entityId] = entityAttributes;

        setState(() {
          _entityAttributeNames[entityId] = attributeNames;
          _entityAttributesLoadedState[entityId] = true;
          _isLoadingAttributes = false;
        });

        Logger.info(
            'Loaded ${attributeNames.length} attributes for entity $entityId from cached data');
        Logger.info('Full attribute objects cached for future use');
      }
    } catch (e) {
      setState(() {
        _entityAttributeNames[entityId] = [];
        _entityAttributesLoadedState[entityId] = true;
        _isLoadingAttributes = false;
      });
      Logger.info('Error fetching attributes for entity $entityId: $e');
    }
  }

  @override
  void dispose() {
    // Dispose controllers without accessing any providers or context
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      //  width: 300,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Color(0xffF7F9FB),
        border: Border(
          top: BorderSide(color: Colors.grey.shade300, width: .5),
          right: BorderSide(color: Colors.grey.shade300, width: .5),
          bottom: BorderSide(color: Colors.grey.shade300, width: .5),
          left: BorderSide.none, // or BorderSide(width: 0)
        ),
        // borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        children: [
          // _buildTabBar(),
          _buildDynamicHeader(),
          _buildSearchRow(),
          Expanded(
            child: _buildTabContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildDynamicHeader() {
    // Check if widget is still mounted before proceeding
    if (!mounted) {
      return const SizedBox.shrink();
    }

    // Get the current tab names
    const List<String> tabNames = [
      'ROLES LIBRARY',
      'OBJECT LIBRARY',
      'GO LIBRARY'
    ];

    // Use local state instead of provider to avoid widget lifecycle issues
    // Ensure the index is within bounds and fallback to 0 if invalid
    final int currentIndex =
        Provider.of<CreationProvider>(context, listen: false)
            .currentMiddleScreen;
    final String currentTabName = tabNames[currentIndex];

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 9, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.black,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              currentTabName,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.titleSmall(context),
                fontWeight: FontManager.regular,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.white,
              ),
            ),
          ),
          // Add icon (plus button) with popup functionality
          GestureDetector(
            onTap: () => _showCreateOptionsPopup(context),
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              child: Icon(
                Icons.add,
                size: 20,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        onTap: (value) {
          Provider.of<CreationProvider>(context, listen: false)
              .currentMiddleScreen = value;
          Provider.of<CreationProvider>(context, listen: false)
              .isGoMyLibraryClicked = false;
        },
        labelColor: Colors.white,
        unselectedLabelColor: Colors.grey.shade600,
        indicator: BoxDecoration(
          color: Colors.black,
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.grey.shade300, // Set divider color to grey
        labelStyle: FontManager.getCustomStyle(
          fontSize: FontManager.s14,
          fontWeight: FontManager.medium,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.white,
        ),
        unselectedLabelStyle: FontManager.getCustomStyle(
          fontSize: FontManager.s14,
          fontWeight: FontManager.regular,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.grey.shade600,
        ),
        tabs: const [
          Tab(text: 'Roles'),
          Tab(text: 'Objects'),
          Tab(text: 'GO'),
        ],
      ),
    );
  }

  Widget _buildSearchRow() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
      child: Row(
        children: [
          // Search TextField
          Expanded(
            flex: 2,
            child: TextField(
              controller: _searchController,
              onChanged: (value) {
                setState(() {
                  _searchQuery = value.toLowerCase();
                });
              },
              decoration: InputDecoration(
                hintText: 'Search',
                hintStyle: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontManager.regular,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Color(0xff989898),
                ),
                border: UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                enabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: UnderlineInputBorder(
                  borderSide:
                      BorderSide(color: AppColors.primaryBlue, width: 2),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 6, vertical: 8),
                isDense: true,
                filled: false,
                fillColor: Colors.transparent,
                hoverColor: Colors.transparent,
              ),
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s14,
                fontWeight: FontManager.regular,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),

          // Spacer (10px)
          const SizedBox(width: 10),

          // Dropdown
          SizedBox(
            width: 100,
            child: Container(
              height: 28,
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade300, width: 1),
                ),
                borderRadius: BorderRadius.circular(0),
                color: Colors.white,
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: _selectedFilter,
                  isExpanded: true,
                  icon: Icon(
                    Icons.keyboard_arrow_down,
                    size: 18,
                    color: Colors.grey.shade600,
                  ),
                  style: FontManager.getCustomStyle(
                    fontSize: FontManager.s12,
                    fontWeight: FontManager.regular,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                  dropdownColor: Colors.white,
                  items: ['All', 'Draft', 'Published']
                      .map((item) => DropdownMenuItem(
                            value: item,
                            child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 8),
                              child: Text(item),
                            ),
                          ))
                      .toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedFilter = value;
                      });
                    }
                  },
                  selectedItemBuilder: (BuildContext context) {
                    return ['All', 'Draft', 'Published']
                        .map<Widget>((String item) {
                      return Container(
                        alignment: Alignment.centerLeft,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: item == 'Draft'
                                ? Colors.orange.shade100
                                : item == 'Published'
                                    ? Colors.green.shade100
                                    : Colors.transparent,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: item == 'Draft'
                                  ? Colors.orange.shade300
                                  : item == 'Published'
                                      ? Colors.green.shade300
                                      : Colors.transparent,
                              width: 0.5,
                            ),
                          ),
                          child: Text(
                            item,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.labelSmall(context),
                              fontWeight: FontManager.regular,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: item == 'Draft'
                                  ? Colors.orange.shade700
                                  : item == 'Published'
                                      ? Colors.green.shade700
                                      : Colors.grey.shade500,
                            ),
                          ),
                        ),
                      );
                    }).toList();
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildRolesTab(),
        _buildObjectsTab(),
        _buildGoTab(),
      ],
    );
  }

  Widget _buildRolesTab() {
    if (_isLoadingRoles) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    final filteredRoles = _rolesObjects.where((role) {
      // Apply search filter
      bool matchesSearch = _searchQuery.isEmpty ||
          (role.name?.toLowerCase().contains(_searchQuery) ?? false);

      // Apply status filter
      bool matchesStatus = true;
      if (_selectedFilter == 'Draft') {
        matchesStatus = false; // No draft roles in postgres data
      } else if (_selectedFilter == 'Published') {
        matchesStatus = true; // All postgres roles are published
      }
      // 'All' shows both draft and published

      return matchesSearch && matchesStatus;
    }).toList();

    if (filteredRoles.isEmpty && _searchQuery.isNotEmpty) {
      return Center(
        child: Text(
          'No roles found for "$_searchQuery"',
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s14,
            fontWeight: FontManager.regular,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.grey[600],
          ),
        ),
      );
    }

    if (filteredRoles.isEmpty) {
      return Center(
        child: Text(
          'No roles available',
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s14,
            fontWeight: FontManager.regular,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.grey[600],
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 1, vertical: 6),
      itemCount: filteredRoles.length,
      itemBuilder: (context, index) {
        final role = filteredRoles[index];
        return _buildRoleItem(role);
      },
    );
  }

  Widget _buildObjectsTab() {
    if (_isLoadingEntities) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    // Show loading indicator when fetching attributes
    if (_isLoadingAttributes) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              'Loading attributes...',
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s14,
                fontWeight: FontManager.regular,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    final filteredObjects = _entitiesObjects.where((entity) {
      // Apply search filter
      final displayName = entity.displayName ?? entity.name ?? 'Unknown Entity';
      bool matchesSearch = _searchQuery.isEmpty ||
          displayName.toLowerCase().contains(_searchQuery);

      // Apply status filter
      bool matchesStatus = true;
      if (_selectedFilter == 'Draft') {
        matchesStatus = entity.status == 'draft';
      } else if (_selectedFilter == 'Published') {
        matchesStatus =
            entity.status == 'active' || entity.status == 'published';
      }
      // 'All' shows both draft and published

      return matchesSearch && matchesStatus;
    }).toList();

    if (filteredObjects.isEmpty && _searchQuery.isNotEmpty) {
      return Center(
        child: Text(
          'No objects found for "$_searchQuery"',
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s14,
            fontWeight: FontManager.regular,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.grey[600],
          ),
        ),
      );
    }

    if (filteredObjects.isEmpty && _selectedFilter != 'All') {
      return Center(
        child: Text(
          'No ${_selectedFilter.toLowerCase()} entities available',
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s14,
            fontWeight: FontManager.regular,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.grey[600],
          ),
        ),
      );
    }

    if (filteredObjects.isEmpty) {
      return Center(
        child: Text(
          'No entities available',
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s14,
            fontWeight: FontManager.regular,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.grey[600],
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 1, vertical: 6),
      itemCount: filteredObjects.length,
      itemBuilder: (context, index) {
        final entity = filteredObjects[index];
        final originalIndex = _entitiesObjects.indexOf(entity);
        return _buildObjectExpansionTile(entity, originalIndex);
      },
    );
  }

  Widget _buildGoTab() {
    if (_isLoadingGO) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    final filteredGOs = _goObjects.where((go) {
      // Apply search filter
      bool matchesSearch = _searchQuery.isEmpty ||
          (go.name?.toLowerCase().contains(_searchQuery) ?? false);

      // Apply status filter
      bool matchesStatus = true;
      if (_selectedFilter == 'Draft') {
        matchesStatus = go.status?.toLowerCase() == 'draft';
      } else if (_selectedFilter == 'Published') {
        matchesStatus = go.status?.toLowerCase() == 'active' ||
            go.status?.toLowerCase() == 'published';
      }
      // 'All' shows both draft and published

      return matchesSearch && matchesStatus;
    }).toList();

    if (filteredGOs.isEmpty && _searchQuery.isNotEmpty) {
      return Center(
        child: Text(
          'No GOs found for "$_searchQuery"',
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s14,
            fontWeight: FontManager.regular,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.grey[600],
          ),
        ),
      );
    }

    if (filteredGOs.isEmpty && _selectedFilter != 'All') {
      return Center(
        child: Text(
          'No ${_selectedFilter.toLowerCase()} Global Objectives available',
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s14,
            fontWeight: FontManager.regular,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.grey[600],
          ),
        ),
      );
    }

    if (filteredGOs.isEmpty) {
      return Center(
        child: Text(
          'No Global Objectives available',
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s14,
            fontWeight: FontManager.regular,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.grey[600],
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 1, vertical: 6),
      itemCount: filteredGOs.length,
      itemBuilder: (context, index) {
        final go = filteredGOs[index];
        final originalIndex = _goObjects.indexOf(go);
        return _buildGoExpansionTile(go, originalIndex);
      },
    );
  }

  Widget _buildRoleItem(RolesPostgre role) {
    return Container(
      margin: const EdgeInsets.only(bottom: 0),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xffE6E6E6), width: .5),
        ),
        color: Colors.white,
      ),
      child: ListTile(
        minTileHeight: 32,
        title: Row(
          children: [
            Expanded(
              child: Tooltip(
                message: 'Role: ${role.name ?? 'Unknown Role'}',
                child: Text(
                  'Role: ${role.name ?? 'Unknown Role'}',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.labelSmall(context),
                    fontWeight: FontManager.regular,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            const SizedBox(width: 8),
            // // Status indicator
            // Container(
            //   padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            //   decoration: BoxDecoration(
            //     color: role.status == 'draft'
            //         ? Colors.orange.shade100
            //         : Colors.green.shade100,
            //     borderRadius: BorderRadius.circular(12),
            //     border: Border.all(
            //       color: role.status == 'draft'
            //           ? Colors.orange.shade300
            //           : Colors.green.shade300,
            //       width: 1,
            //     ),
            //   ),
            //   child: Text(
            //     role.status == 'draft' ? 'Draft' : 'Published',
            //     style: FontManager.getCustomStyle(
            //       fontSize: FontManager.s10,
            //       fontWeight: FontManager.medium,
            //       fontFamily: FontManager.fontFamilyTiemposText,
            //       color: role.status == 'draft'
            //           ? Colors.orange.shade700
            //           : Colors.green.shade700,
            //     ),
            //   ),
            // ),
          ],
        ),
        trailing: MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              Logger.info('Role plus button tapped: ${role.name}');
              _handleRoleSelection(role.name ?? 'Unknown Role');
            },
            child: const Icon(
              Icons.add,
              size: 16,
              color: Colors.grey,
            ),
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
        minVerticalPadding: 0,
        dense: true,
      ),
    );
  }

  Widget _buildGoExpansionTile(GlobalObjectivesPostgre go, int index) {
    final goId = go.goId ?? '';
    final bool isExpanded = _goExpandedState[goId] ?? false;

    return Container(
      margin: const EdgeInsets.only(
          bottom: 0), // No spacing between items // Set height to 32
      decoration: BoxDecoration(
        border: isExpanded
            ? Border.all(
                color: AppColors.primaryBlue,
                width: 1,
              )
            : Border(
                bottom: BorderSide(color: Colors.grey.shade300, width: 0.5),
              ),
        borderRadius: isExpanded ? BorderRadius.circular(4) : BorderRadius.zero,
        color: Colors.white,
      ),
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent, // Remove default divider
        ),
        child: ExpansionTile(
          minTileHeight: 32,
          title: Row(
            children: [
              Expanded(
                child: Tooltip(
                  message: 'GO: ${go.name ?? 'Unknown GO'}',
                  child: Text(
                    'GO: ${go.name ?? 'Unknown GO'}',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelSmall(context),
                      fontWeight: FontManager.regular,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // Status indicator
              // Container(
              //   padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              //   decoration: BoxDecoration(
              //     color: go.status == 'draft'
              //         ? Colors.orange.shade100
              //         : Colors.green.shade100,
              //     borderRadius: BorderRadius.circular(12),
              //     border: Border.all(
              //       color: go.status == 'draft'
              //           ? Colors.orange.shade300
              //           : Colors.green.shade300,
              //       width: 1,
              //     ),
              //   ),
              //   child: Text(
              //     go.status == 'draft' ? 'Draft' : 'Published',
              //     style: FontManager.getCustomStyle(
              //       fontSize: FontManager.s10,
              //       fontWeight: FontManager.medium,
              //       fontFamily: FontManager.fontFamilyTiemposText,
              //       color: go.status == 'draft'
              //           ? Colors.orange.shade700
              //           : Colors.green.shade700,
              //     ),
              //   ),
              // ),
            ],
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Expansion arrow
              Icon(
                isExpanded
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: Colors.grey.shade600,
                size: 20,
              ),
              const SizedBox(width: 6),
              // Plus button with cursor pointer on hover
              MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  onTap: () async {
                    // Handle plus button tap - populate GO data in the right panel
                    Logger.info('GO plus button tapped: ${go.name}');

                    // Check for unsaved changes before proceeding
                    final hasUnsavedChanges = _hasUnsavedChanges(context);

                    if (hasUnsavedChanges) {
                      // Show confirmation dialog
                      final shouldProceed =
                          await _showUnsavedChangesWarning(context);
                      if (shouldProceed && mounted) {
                        _handleGoSelection(context, go);
                      }
                      // If user cancels, do nothing (keep current work)
                    } else {
                      // No unsaved changes, proceed directly
                      _handleGoSelection(context, go);
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    child: const Icon(
                      Icons.add,
                      size: 16,
                      color: Colors.grey,
                    ),
                  ),
                ),
              ),
            ],
          ),
          initiallyExpanded: false, // Always start collapsed
          onExpansionChanged: (expanded) async {
            // // Add dynamic tab when expansion panel is clicked (opened)
            // if (expanded) {
            //   // Check for unsaved changes before proceeding
            //   final hasUnsavedChanges = _hasUnsavedChanges(context);

            //   if (hasUnsavedChanges) {
            //     // Show confirmation dialog
            //     final shouldProceed = await _showUnsavedChangesWarning(context);
            //     if (shouldProceed && mounted) {
            //       _handleGoSelection(context, go);
            //     }
            //     // If user cancels, do nothing (keep current work)
            //   } else {
            //     // No unsaved changes, proceed directly
            //     _handleGoSelection(context, go);
            //   }
            // }

            setState(() {
              // Close all other GO panels when opening this one
              if (expanded) {
                for (final otherGoId in _goExpandedState.keys) {
                  if (otherGoId != goId) {
                    _goExpandedState[otherGoId] = false;
                  }
                }
              }
              _goExpandedState[goId] = expanded;
            });

            // Fetch local objectives when expanding and not already loaded
            if (expanded && !(_goLocalObjectivesLoadedState[goId] ?? false)) {
              if (goId.isNotEmpty) {
                Logger.info('Expanding GO: ${go.name} with ID: $goId');
                await _fetchGoLocalObjectives(goId, index);
              }
            }
          },
          tilePadding: EdgeInsets.symmetric(
              horizontal: 12, vertical: 0), // Remove padding
          childrenPadding: EdgeInsets
              .zero, // Remove all padding for full width local objectives
          children: [
            // Local objectives list with full width, plus icons, status indicators, and bottom borders
            ...(_goLocalObjectivesObjects[goId] ?? <LocalObjectivesPostgre>[])
                .asMap()
                .entries
                .map<Widget>((entry) {
              final int loIndex = entry.key;
              final LocalObjectivesPostgre localObjective = entry.value;
              final localObjectives = _goLocalObjectivesObjects[goId] ?? [];
              final bool isLastLO = loIndex == localObjectives.length - 1;

              return Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  border: Border(
                    bottom: isLastLO
                        ? BorderSide.none
                        : BorderSide(color: Colors.grey.shade300, width: 0.5),
                  ),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal:
                      12, // Increased horizontal padding for better alignment
                  vertical: 6, // Consistent vertical padding
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        localObjective.name ?? 'Unknown Local Objective',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.labelSmall(context),
                          fontWeight: FontManager.regular,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ),

                    // Plus icon with cursor pointer on hover
                    MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: GestureDetector(
                        onTap: () {
                          // Handle local objective plus button tap
                          _handleLoSelection(
                              context, localObjective, loIndex, go);
                        },
                        child: Container(
                          padding: const EdgeInsets.all(2),
                          child: const Icon(
                            Icons.add,
                            size: 16,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildObjectExpansionTile(EntitiesMongoDraft entity, int index) {
    final entityId = entity.entityId ?? '';
    final displayName = entity.displayName ?? entity.name ?? 'Unknown Entity';
    final bool isExpanded = _entityExpandedState[entityId] ?? false;

    return Container(
      margin: const EdgeInsets.only(bottom: 0),
      // height: 32, // Set height to 32
      decoration: BoxDecoration(
        border: isExpanded
            ? Border.all(
                color: AppColors.primaryBlue,
                width: .5,
              )
            : Border(
                bottom: BorderSide(color: Colors.grey.shade300, width: .5),
              ),
        borderRadius: isExpanded ? BorderRadius.circular(4) : BorderRadius.zero,
        color: Colors.white,
      ),
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent, // Remove default divider
        ),
        child: ExpansionTile(
          minTileHeight: 32,
          title: Row(
            children: [
              Expanded(
                child: Tooltip(
                  message: 'Object: $displayName',
                  child: Text(
                    'Object: $displayName',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelSmall(context),
                      fontWeight: FontManager.regular,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // Status indicator
              // Container(
              //   padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              //   decoration: BoxDecoration(
              //     color: entity.status == 'draft'
              //         ? Colors.orange.shade100
              //         : Colors.green.shade100,
              //     borderRadius: BorderRadius.circular(12),
              //     border: Border.all(
              //       color: entity.status == 'draft'
              //           ? Colors.orange.shade300
              //           : Colors.green.shade300,
              //       width: 1,
              //     ),
              //   ),
              //   child: Text(
              //     entity.status == 'draft' ? 'Draft' : 'Published',
              //     style: FontManager.getCustomStyle(
              //       fontSize: FontManager.s10,
              //       fontWeight: FontManager.medium,
              //       fontFamily: FontManager.fontFamilyTiemposText,
              //       color: entity.status == 'draft'
              //           ? Colors.orange.shade700
              //           : Colors.green.shade700,
              //     ),
              //   ),
              // ),
            ],
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                isExpanded
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: Colors.grey.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              // Plus button with conditional enabling
              _buildConditionalAddIcon(
                onTap: () {
                  Logger.info('Object plus button tapped: ${entity.name}');
                  _handleObjectSelection(context, entity);
                },
                tooltipMessage:
                    'Add ${entity.displayName ?? entity.name} to Input Stack',
              ),
            ],
          ),
          initiallyExpanded: false, // Always start collapsed
          onExpansionChanged: (expanded) async {
            // Add dynamic tab when expansion panel is clicked (opened)
            // if (expanded) {
            //   _handleObjectSelection(context, entity);
            // }

            setState(() {
              // Close all other Entity panels when opening this one
              if (expanded) {
                for (final otherEntityId in _entityExpandedState.keys) {
                  if (otherEntityId != entityId) {
                    _entityExpandedState[otherEntityId] = false;
                  }
                }
              }
              _entityExpandedState[entityId] = expanded;
            });

            // Fetch attributes when expanding and not already loaded
            if (expanded &&
                !(_entityAttributesLoadedState[entityId] ?? false)) {
              if (entityId.isNotEmpty) {
                await _fetchEntityAttributes(entityId, index);
              }
            }
          },
          tilePadding: const EdgeInsets.symmetric(
              horizontal: 12, vertical: 0), // Remove padding
          childrenPadding:
              EdgeInsets.zero, // Remove all padding for full width attributes
          children: [
            // Show loading indicator when attributes are being fetched
            if (!(_entityAttributesLoadedState[entityId] ?? false))
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                child: Row(
                  children: [
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Colors.grey[600]!,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Loading attributes...',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontManager.regular,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              )
            else
              // Attributes list with full width, plus icons, and bottom borders
              ...(_entityAttributeNames[entityId] ?? [])
                  .asMap()
                  .entries
                  .map<Widget>((entry) {
                final int attributeIndex = entry.key;
                final ObjectAttribute attribute = entry.value;
                final attributeNames = _entityAttributeNames[entityId] ?? [];
                final bool isLastAttribute =
                    attributeIndex == attributeNames.length - 1;

                return Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: isLastAttribute
                          ? BorderSide.none
                          : BorderSide(color: Colors.grey.shade300, width: 1),
                    ),
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal:
                        12, // Only horizontal padding to match container
                    vertical: 6, // Proper vertical padding for better spacing
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          attribute.displayName ?? '',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.labelSmall(context),
                            fontWeight: FontManager.regular,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.grey.shade700,
                          ),
                        ),
                      ),
                      // Plus icon with conditional enabling
                      _buildConditionalAddIcon(
                        onTap: () {
                          Logger.info(
                              'Attribute plus button tapped: $attribute');
                          _handleAttributeSelection(context, entity, attribute);
                        },
                        tooltipMessage: 'Add $attribute to Input Stack',
                      ),
                    ],
                  ),
                );
              }),
          ],
        ),
      ),
    );
  }

  /// Handle object selection and populate accordion
  void _handleObjectSelection(
      BuildContext context, EntitiesMongoDraft entity) async {
    // // Check if accordion is available before proceeding
    // try {
    //   final accordionProvider = Provider.of<AccordionAvailabilityProvider>(
    //     context,
    //     listen: false,
    //   );
    //   if (!accordionProvider.canAddToAccordion) {
    //     _showDisabledMessage(context, accordionProvider.getDisabledMessage());
    //     return;
    //   }
    // } catch (e) {
    //   // Provider might not be available in some contexts - continue anyway
    // }

    final provider = Provider.of<CreationProvider>(context, listen: false);
    if (provider.currentMiddleScreen == 2 || provider.isGoMyLibraryClicked) {
      // Use GoDetailsProvider for LO-specific accordion state management
      final goDetailsProvider =
          Provider.of<GoDetailsProvider>(context, listen: false);
      final selectedLoIndex = goDetailsProvider.selectedLocalObjectiveIndex;

      final entityId = entity.entityId ?? '';
      final displayName = entity.displayName ?? entity.name ?? 'Unknown Entity';

      Logger.info('=== Object Selection Debug ===');
      Logger.info('Entity name: ${entity.name}');
      Logger.info('Entity displayName: $displayName');
      Logger.info('Entity entityId: $entityId');
      Logger.info(
          'Attributes loaded: ${_entityAttributesLoadedState[entityId] ?? false}');
      // Get the entity's attributes - if not loaded, fetch them first
      List<ObjectAttribute> attributes = _entityAttributeNames[entityId] ?? [];

      // If attributes are empty and not loaded, fetch them
      if (attributes.isEmpty &&
          !(_entityAttributesLoadedState[entityId] ?? false)) {
        Logger.info('Attributes not loaded, fetching...');
        if (entityId.isNotEmpty) {
          // Find the entity index to fetch attributes
          final entityIndex = _entitiesObjects.indexOf(entity);
          if (entityIndex != -1) {
            Logger.info('Found entity at index: $entityIndex');
            // Fetch attributes for this entity
            await _fetchEntityAttributes(entityId, entityIndex);
            // Get the updated attributes after fetching
            attributes = _entityAttributeNames[entityId] ?? [];
            Logger.info('Fetched attributes: $attributes');
          } else {
            Logger.info('Entity not found in _entitiesObjects');
          }
        } else {
          Logger.info('No entityId found for entity');
        }
      } else {
        Logger.info('Using existing attributes: $attributes');
      }

      // Create a Map representation for provider compatibility
      final entityMap = ObjectCreationModel(
        id: entity.id,
        name: entity.name,
        displayName: displayName,
        description: entity.description,
        type: entity.type,
        attributes: attributes,
      );

      // Add the selected object to the appropriate accordion state
      if (selectedLoIndex != null) {
        // Add to LO-specific accordion state
        goDetailsProvider.addLoSelectedObject(
            selectedLoIndex, entityMap, attributes);
        Logger.info('Added object to LO-${selectedLoIndex + 1} accordion');
      } else {
        // Fall back to global provider for backward compatibility
        final selectedObjectProvider =
            Provider.of<SelectedObjectProvider>(context, listen: false);
        selectedObjectProvider.addSelectedObject(entityMap, attributes);
        Logger.info('Added object to global accordion (no LO selected)');
      }

      // Show a confirmation message (check if widget is still mounted)
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              selectedLoIndex != null
                  ? 'Added $displayName to LO-${selectedLoIndex + 1} with ${attributes.length} attributes'
                  : 'Selected $displayName with ${attributes.length} attributes',
              style: const TextStyle(color: Colors.white),
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } else if (provider.currentMiddleScreen == 1) {
      Provider.of<CreateEntityProvider>(context, listen: false)
          .handleMyLibraryObject(ObjectCreationModel(
        id: entity.id,
        name: entity.name,
        displayName: entity.displayName,
        description: entity.description,
        type: entity.type,
      ));
    }
  }

  /// Handle single attribute selection and populate accordion
  void _handleAttributeSelection(BuildContext context,
      EntitiesMongoDraft entity, ObjectAttribute attribute) {
    // Check if accordion is available before proceeding
    try {
      final accordionProvider = Provider.of<AccordionAvailabilityProvider>(
        context,
        listen: false,
      );
      if (!accordionProvider.canAddToAccordion) {
        _showDisabledMessage(context, accordionProvider.getDisabledMessage());
        return;
      }
    } catch (e) {
      // Provider might not be available in some contexts - continue anyway
    }

    // Use GoDetailsProvider for LO-specific accordion state management
    final goDetailsProvider =
        Provider.of<GoDetailsProvider>(context, listen: false);
    final selectedLoIndex = goDetailsProvider.selectedLocalObjectiveIndex;

    final displayName = entity.displayName ?? entity.name ?? 'Unknown Entity';
    final entityId = entity.entityId ?? '';

    // Create a modified object with just the selected attribute
    final modifiedObject = ObjectCreationModel(
      id: entity.id,
      name: entity.name,
      displayName: entity.displayName,
      description: entity.description ?? '',
      type: entity.type,
      attributes: [attribute],
    );
    // final modifiedObject = {
    //   'entityId': entityId,
    //   'name': '$displayName - $attribute',
    //   'displayName': '$displayName - $attribute',
    //   'description': entity.description ?? '',
    //   'type': entity.type ?? 'Unknown',
    //   'status': entity.status ?? 'active',
    //   'attributes': [attribute],
    // };

    // Add the selected attribute to the appropriate accordion state
    if (selectedLoIndex != null) {
      // Use the new method with entity and attribute checking
      final result = goDetailsProvider.addLoSelectedObjectWithCheck(
          selectedLoIndex, modifiedObject, [attribute]);

      // Show appropriate message based on the result
      Color snackBarColor;
      String message;

      if (result['success'] == true) {
        snackBarColor = Colors.blue;
        if (result['action'] == 'entity_added') {
          message =
              'Added new entity "$displayName" with attribute "$attribute" to LO-${selectedLoIndex + 1}';
        } else {
          message =
              'Added attribute "$attribute" to existing entity "$displayName" in LO-${selectedLoIndex + 1}';
        }
      } else {
        snackBarColor = Colors.orange;
        message =
            'Attribute "$attribute" already exists in entity "$displayName" for LO-${selectedLoIndex + 1}';
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            message,
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: snackBarColor,
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );

      Logger.info(
          'Attribute selection result: ${result['action']} - ${result['message']}');
    } else {
      // Fall back to global provider for backward compatibility
      final selectedObjectProvider =
          Provider.of<SelectedObjectProvider>(context, listen: false);
      selectedObjectProvider.addSelectedObject(modifiedObject, [attribute]);
      Logger.info('Added attribute to global accordion (no LO selected)');

      // Show a confirmation message for global provider
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Added attribute "$attribute" from $displayName',
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: Colors.blue,
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    }
  }

  /// Show create options popup when plus icon is clicked
  void _showCreateOptionsPopup(BuildContext context) {
    // Create an overlay entry for custom positioning
    OverlayEntry? overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          // Transparent background to detect taps outside
          Positioned.fill(
            child: GestureDetector(
              onTap: () {
                overlayEntry?.remove();
              },
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),
          // Popup positioned relative to the plus icon in the left panel
          Positioned(
            top: 40, // Position below the header
            left: MediaQuery.of(context).size.width *
                0.15, // Position from left edge to align with plus icon
            child: Container(
              width: 120,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
              decoration: BoxDecoration(
                color: Color(0xffF5F5F5),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: const Color(0xFFD0D0D0),
                  width: 0.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0x67676741),
                    offset: const Offset(0, 3),
                    blurRadius: 20,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Create One Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        overlayEntry?.remove();
                        _handleCreateOne();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: Colors.black,
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(
                            vertical: 3, horizontal: 6),
                        shape: RoundedRectangleBorder(
                          side: BorderSide(color: Colors.black, width: .5),
                        ),
                      ),
                      child: Text(
                        'Create One',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.labelSmall(context),
                          fontWeight: FontManager.regular,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  // OR Text
                  Text(
                    'OR',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelSmall(context),
                      fontWeight: FontManager.regular,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 6),
                  // Bulk Upload Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        overlayEntry?.remove();
                        _handleBulkUpload();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: Colors.black,
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(
                            vertical: 3, horizontal: 6),
                        shape: RoundedRectangleBorder(
                          // borderRadius: BorderRadius.circular(4),
                          side: BorderSide(color: Colors.black, width: .5),
                        ),
                      ),
                      child: Text(
                        'Bulk Upload',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.labelSmall(context),
                          fontWeight: FontManager.regular,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );

    // Add the overlay to the screen
    Overlay.of(context).insert(overlayEntry);
  }

  /// Handle the selection from create options popup
  void _handleCreateOptionSelection(String option) {
    switch (option) {
      case 'create_one':
        print('Create One selected');
        // Handle create one action based on current tab
        _handleCreateOne();
        break;
      case 'bulk_upload':
        print('Bulk Upload selected');
        // Handle bulk upload action based on current tab
        _handleBulkUpload();
        break;
    }
  }

  /// Handle create one action based on current tab
  void _handleCreateOne() {
    const List<String> tabNames = [
      'ROLES LIBRARY',
      'OBJECT LIBRARY',
      'GO LIBRARY'
    ];

    final creationProvider =
        Provider.of<CreationProvider>(context, listen: false);
    final int currentTabIndex = creationProvider.currentMiddleScreen;
    final String currentTabName = tabNames[currentTabIndex];

    // Handle specific actions based on current tab
    switch (currentTabIndex) {
      case 0: // ROLES LIBRARY
        // Trigger adding new role rows in role creation screen
        print('=== DEBUGGING: Triggering new role row addition ===');
        creationProvider.triggerAddNewRoleRow();
        print(
            '=== DEBUGGING: Trigger set, shouldAddNewRoleRow = ${creationProvider.shouldAddNewRoleRow} ===');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'New role rows added to creation form',
              style: const TextStyle(color: Colors.white),
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
        break;
      case 1: // OBJECT LIBRARY
      case 2: // GO LIBRARY
      default:
        // For other tabs, show the default message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Create One selected for $currentTabName',
              style: const TextStyle(color: Colors.white),
            ),
            backgroundColor: Colors.blue,
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
        break;
    }
  }

  /// Handle bulk upload action based on current tab
  void _handleBulkUpload() {
    const List<String> tabNames = [
      'ROLES LIBRARY',
      'OBJECT LIBRARY',
      'GO LIBRARY'
    ];

    final String currentTabName = tabNames[
        Provider.of<CreationProvider>(context, listen: false)
            .currentMiddleScreen];

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Bulk Upload selected for $currentTabName',
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Handle role selection and show role creation in middle panel
  void _handleRoleSelection(String roleName) {
    Logger.info(
        '=== DEBUGGING: _handleRoleSelection called with role: $roleName ===');

    // Find the role data from cached library data
    RolesPostgre? selectedRole;
    if (_cachedLibraryData?.roles?.postgres != null) {
      Logger.info('Cached library data available, searching for role...');
      try {
        selectedRole = _cachedLibraryData!.roles!.postgres!
            .firstWhere((role) => role.name == roleName);
        Logger.info(
            'Found role: ${selectedRole.name}, ID: ${selectedRole.roleId}, Reports To: ${selectedRole.reportsToRoleId}');
      } catch (e) {
        Logger.info('Role not found in cached data: $roleName');
      }
    } else {
      Logger.info('No cached library data available');
    }

    // Use CreationProvider to show role creation screen in middle panel
    final creationProvider =
        Provider.of<CreationProvider>(context, listen: false);

    // Set the role data for auto-population
    print('Setting role data in CreationProvider...');
    print('selectedRole: ${selectedRole?.name}, ID: ${selectedRole?.roleId}');
    print(
        'All roles count: ${_cachedLibraryData?.roles?.postgres?.length ?? 0}');
    creationProvider.setRoleForCreation(
        selectedRole, _cachedLibraryData?.roles?.postgres ?? []);
    print(
        'Role data set in provider. Provider role: ${creationProvider.prePopulatedRole?.name}');

    // Switch to role creation screen
    Logger.info('Switching to role creation screen (currentMiddleScreen = 0)');
    creationProvider.currentMiddleScreen = 0; // 0 = roles screen

    Logger.info('=== END DEBUGGING ===');
  }
}
