import 'package:flutter/material.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';

/// Enum for progress flow states
enum ProgressFlowState {
  /// Completed state - green bullet
  done,

  /// Current/active state - yellow bullet
  pending,

  /// Future/inactive state - disabled/grey bullet
  disabled,
}

/// Model for progress flow step
class ProgressFlowStep {
  final String title;
  final ProgressFlowState state;
  final String? subtitle;

  const ProgressFlowStep({
    required this.title,
    required this.state,
    this.subtitle,
  });
}

/// A progress flow bar widget with bullet indicators
class CreateProgressFlowBar extends StatefulWidget {
  /// List of progress steps
  final List<ProgressFlowStep> steps;

  /// Height of the progress bar
  final double height;

  /// Size of the bullet indicators
  final double bulletSize;

  /// Color for done state (default: green)
  final Color doneColor;

  /// Color for pending state (default: yellow/orange)
  final Color pendingColor;

  /// Color for disabled state (default: grey)
  final Color disabledColor;

  /// Color for the connecting line
  final Color lineColor;

  /// Whether to show step titles
  final bool showTitles;

  /// Text style for step titles
  final TextStyle? titleStyle;

  /// Text style for step subtitles
  final TextStyle? subtitleStyle;

  /// Spacing between bullet and text
  final double textSpacing;

  const CreateProgressFlowBar({
    super.key,
    required this.steps,
    this.height = 40.0,
    this.bulletSize = 8.0,
    this.doneColor = const Color(0xFF00C31E), // Green
    this.pendingColor = const Color(0xFFF79B08), // Orange/Yellow
    this.disabledColor = const Color(0xFF9F9F9F), // Grey
    this.lineColor = const Color(0xFFD0D0D0),
    this.showTitles = true,
    this.titleStyle,
    this.subtitleStyle,
    this.textSpacing = 4.0,
  });

  @override
  State<CreateProgressFlowBar> createState() => _CreateProgressFlowBarState();
}

class _CreateProgressFlowBarState extends State<CreateProgressFlowBar> {
  @override
  Widget build(BuildContext context) {
    if (widget.steps.isEmpty) return const SizedBox.shrink();

    return Container(
      height: widget.height,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: _buildStepWidgets(),
      ),
    );
  }

  /// Builds the list of step widgets with connecting lines
  List<Widget> _buildStepWidgets() {
    List<Widget> widgets = [];
    for (int i = 0; i < widget.steps.length; i++) {
      final isLastStep = i == widget.steps.length - 1;
      widgets.add(
        _buildStepWidget(widget.steps[i], i),
      );

      if (!isLastStep) {
        widgets.add(
          _buildConnectingLine(),
        );
      }
    }
    return widgets;
  }

  /// Builds individual step widget with bullet and optional text
  Widget _buildStepWidget(ProgressFlowStep step, int index) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Bullet indicator
        _buildBullet(step.state),

        if (widget.showTitles) ...[
          SizedBox(width: widget.textSpacing),
          // Step title and subtitle in a columns
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                step.title,
                style: widget.titleStyle?.copyWith(
                      color: _getTextColor(step.state),
                    ) ??
                    FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelSmall(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: _getTextColor(step.state),
                    ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              // Step subtitle (if provided)
              if (step.subtitle != null) ...[
                const SizedBox(height: 1),
                Text(
                  step.subtitle!,
                  style: widget.subtitleStyle?.copyWith(
                        color: _getTextColor(step.state).withValues(alpha: 0.7),
                      ) ??
                      TextStyle(
                        fontSize: 10,
                        color: _getTextColor(step.state).withValues(alpha: 0.7),
                      ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ],
      ],
    );
  }

  /// Builds bullet indicator based on state
  Widget _buildBullet(ProgressFlowState state) {
    Color bulletColor = _getBulletColor(state);

    return Container(
      width: widget.bulletSize,
      height: widget.bulletSize,
      decoration: BoxDecoration(
        color: bulletColor,
        shape: BoxShape.circle,
        border: state == ProgressFlowState.disabled
            ? Border.all(color: widget.disabledColor, width: 1)
            : null,
      ),
      child: state == ProgressFlowState.done
          ? Icon(
              Icons.check,
              color: Colors.white,
              size: widget.bulletSize * 0.6,
            )
          : null,
    );
  }

  /// Builds connecting line between steps
  Widget _buildConnectingLine() {
    return Container(
      height: 1,
      width: 15,
      color: widget.lineColor,
      margin: const EdgeInsets.symmetric(horizontal: 4),
    );
  }

  /// Gets bullet color based on state
  Color _getBulletColor(ProgressFlowState state) {
    switch (state) {
      case ProgressFlowState.done:
        return widget.doneColor;
      case ProgressFlowState.pending:
        return widget.pendingColor;
      case ProgressFlowState.disabled:
        return Colors.transparent; // Transparent with border
    }
  }

  /// Gets text color based on state
  Color _getTextColor(ProgressFlowState state) {
    switch (state) {
      case ProgressFlowState.done:
        //return widget.doneColor;
        return Colors.black;
      case ProgressFlowState.pending:
        //return widget.pendingColor;
        return Colors.black;
      case ProgressFlowState.disabled:
        return widget.disabledColor;
    }
  }
}

/// Middle bar section widget - shows only the middle portion of progress
class MiddleProgressFlowBar extends StatelessWidget {
  /// List of all progress steps
  final List<ProgressFlowStep> allSteps;

  /// Index of the current step (0-based)
  final int currentStepIndex;

  /// Number of steps to show around current step (default: 1 means show 1 before and 1 after)
  final int contextSteps;

  /// All other properties from CreateProgressFlowBar
  final double height;
  final double bulletSize;
  final Color doneColor;

  final Color pendingColor;
  final Color disabledColor;
  final Color lineColor;
  final bool showTitles;
  final TextStyle? titleStyle;
  final TextStyle? subtitleStyle;
  final double textSpacing;

  const MiddleProgressFlowBar({
    super.key,
    required this.allSteps,
    required this.currentStepIndex,
    this.contextSteps = 1,
    this.height = 40.0,
    this.bulletSize = 8.0,
    this.doneColor = const Color(0xFF4CAF50),
    this.pendingColor = const Color(0xFFFF9800),
    this.disabledColor = const Color(0xFFBDBDBD),
    this.lineColor = const Color(0xFFE0E0E0),
    this.showTitles = true,
    this.titleStyle,
    this.subtitleStyle,
    this.textSpacing = 4.0,
  });

  @override
  Widget build(BuildContext context) {
    if (allSteps.isEmpty ||
        currentStepIndex < 0 ||
        currentStepIndex >= allSteps.length) {
      return const SizedBox.shrink();
    }

    // Calculate the range of steps to show
    int startIndex =
        (currentStepIndex - contextSteps).clamp(0, allSteps.length - 1);
    int endIndex =
        (currentStepIndex + contextSteps).clamp(0, allSteps.length - 1);

    // Generate steps for the visible range with calculated states
    List<ProgressFlowStep> middleSteps = [];
    for (int i = startIndex; i <= endIndex; i++) {
      final stepData = allSteps[i];
      ProgressFlowState state;

      if (i < currentStepIndex) {
        state = ProgressFlowState.done;
      } else if (i == currentStepIndex) {
        state = ProgressFlowState.pending;
      } else {
        state = ProgressFlowState.disabled;
      }

      middleSteps.add(
        ProgressFlowStep(
          title: stepData.title,
          subtitle: stepData.subtitle,
          state: state,
        ),
      );
    }

    return CreateProgressFlowBar(
      steps: middleSteps,
      height: height,
      bulletSize: bulletSize,
      doneColor: doneColor,
      pendingColor: pendingColor,
      disabledColor: disabledColor,
      lineColor: lineColor,
      showTitles: showTitles,
      titleStyle: titleStyle,
      subtitleStyle: subtitleStyle,
      textSpacing: textSpacing,
    );
  }
}
