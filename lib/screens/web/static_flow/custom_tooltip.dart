import 'dart:async';
import 'package:flutter/material.dart';

/// Enum for tooltip positioning options
enum TooltipPosition {
  left,
  right,
  top,
  bottom,
}

class CustomTooltip {
  static OverlayEntry? _currentEntry;
  static Timer? _autoHideTimer;
  static Timer? _showDelayTimer;

  static void show({
    required BuildContext context,
    required GlobalKey key,
    required String message,
    TooltipPosition position = TooltipPosition.top,
  }) {
    // Cancel any pending show timer
    _showDelayTimer?.cancel();

    // Add a small delay before showing tooltip to prevent interference with immediate clicks
    _showDelayTimer = Timer(const Duration(milliseconds: 500), () {
      // Remove existing tooltip before showing new one
      hide();

      final overlay = Overlay.of(context);
      final renderBox = key.currentContext?.findRenderObject() as RenderBox?;
      if (renderBox == null) return;

      final targetPosition = renderBox.localToGlobal(Offset.zero);
      final targetSize = renderBox.size;

      _currentEntry = OverlayEntry(
        builder: (context) => _TooltipOverlay(
          position: targetPosition,
          size: targetSize,
          message: message,
          tooltipPosition: position,
          onDismiss: hide,
        ),
      );

      overlay.insert(_currentEntry!);

      // Auto-hide tooltip after 5 seconds to prevent interference
      _autoHideTimer?.cancel();
      _autoHideTimer = Timer(const Duration(seconds: 5), () {
        hide();
      });
    });
  }

  static void hide() {
    _showDelayTimer?.cancel();
    _showDelayTimer = null;
    _autoHideTimer?.cancel();
    _autoHideTimer = null;
    _currentEntry?.remove();
    _currentEntry = null;
  }
}

/// Tooltip overlay widget that handles positioning and click-to-dismiss behavior
class _TooltipOverlay extends StatelessWidget {
  final Offset position;
  final Size size;
  final String message;
  final TooltipPosition tooltipPosition;
  final VoidCallback onDismiss;

  const _TooltipOverlay({
    required this.position,
    required this.size,
    required this.message,
    required this.tooltipPosition,
    required this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        _buildPositionedTooltip(),
      ],
    );
  }

  Widget _buildPositionedTooltip() {
    final tooltipOffset = _calculateTooltipOffset();

    return Positioned(
      left: tooltipOffset.dx,
      top: tooltipOffset.dy,
      child: Material(
        color: Colors.transparent,
        child: _buildTooltipWithArrow(Offset.zero), // Arrow offset calculated dynamically now
      ),
    );
  }

  Widget _buildTooltipWithArrow(Offset arrowOffset) {
    // Use a GlobalKey to measure the tooltip container
    final tooltipKey = GlobalKey();

    final tooltipContainer = Container(
      key: tooltipKey,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      constraints: const BoxConstraints(maxWidth: 300),
      decoration: BoxDecoration(
        color: const Color(0xFF0058FF),
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: Text(
              message,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 13,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: 8),
          GestureDetector(
            onTap: onDismiss,
            child: const Icon(Icons.close, size: 16, color: Colors.white),
          )
        ],
      ),
    );

    return LayoutBuilder(
      builder: (context, constraints) {
        final arrow = CustomPaint(
          size: tooltipPosition == TooltipPosition.left || tooltipPosition == TooltipPosition.right
              ? const Size(8, 16)
              : const Size(16, 8),
          painter: _TooltipArrowPainter(direction: tooltipPosition),
        );

        // For different positions, use appropriate layout
        if (tooltipPosition == TooltipPosition.top) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              tooltipContainer,
              SizedBox(height: 0),
              arrow,
            ],
          );
        } else if (tooltipPosition == TooltipPosition.bottom) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              arrow,
              SizedBox(height: 0),
              tooltipContainer,
            ],
          );
        } else if (tooltipPosition == TooltipPosition.left) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              tooltipContainer,
              SizedBox(width: 0),
              arrow,
            ],
          );
        } else if (tooltipPosition == TooltipPosition.right) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              arrow,
              SizedBox(width: 0),
              tooltipContainer,
            ],
          );
        } else {
          return Stack(
            clipBehavior: Clip.none,
            children: [
              tooltipContainer,
              arrow,
            ],
          );
        }
      },
    );
  }

  Offset _calculateTooltipOffset() {
    const tooltipWidth = 300.0; // Max width constraint
    const tooltipHeight = 50.0; // Approximate height
    const arrowSize = 8.0;
    const spacing = 10.0; // Space between tooltip and target

    switch (tooltipPosition) {
      case TooltipPosition.top:
        return Offset(
          position.dx + (size.width / 2) - (tooltipWidth / 2),
          position.dy - tooltipHeight - arrowSize - spacing,
        );
      case TooltipPosition.bottom:
        return Offset(
          position.dx + (size.width / 2) - (tooltipWidth / 2),
          position.dy + size.height + spacing,
        );
      case TooltipPosition.left:
        return Offset(
          position.dx - tooltipWidth - arrowSize - spacing,
          position.dy + (size.height / 2) - (tooltipHeight / 2),
        );
      case TooltipPosition.right:
        return Offset(
          position.dx + size.width + arrowSize + spacing,
          position.dy + (size.height / 2) - (tooltipHeight / 2),
        );
    }
  }


}

/// Custom painter for tooltip arrows with directional support
class _TooltipArrowPainter extends CustomPainter {
  final TooltipPosition direction;

  const _TooltipArrowPainter({required this.direction});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFF0058FF) // Match tooltip background color
      ..style = PaintingStyle.fill;

    final path = Path();

    switch (direction) {
      case TooltipPosition.top:
        // Downward pointing arrow (tooltip above target)
        path.moveTo(0, 0);
        path.lineTo(size.width / 2, size.height);
        path.lineTo(size.width, 0);
        break;
      case TooltipPosition.bottom:
        // Upward pointing arrow (tooltip below target)
        path.moveTo(size.width / 2 - 8, size.height);
        path.lineTo(size.width / 2, 0);
        path.lineTo(size.width / 2 + 8, size.height);
        break;
      case TooltipPosition.left:
        // Rightward pointing arrow (tooltip left of target)
        path.moveTo(0, size.height / 2 - 8);
        path.lineTo(size.width, size.height / 2);
        path.lineTo(0, size.height / 2 + 8);
        break;
      case TooltipPosition.right:
        // Leftward pointing arrow (tooltip right of target)
        path.moveTo(size.width, size.height / 2 - 8);
        path.lineTo(0, size.height / 2);
        path.lineTo(size.width, size.height / 2 + 8);
        break;
    }

    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
