import 'dart:async';
import 'package:flutter/material.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:nsl/models/agent_manual_response_model.dart';
import '../../../../../models/role_info.dart';
import '../../../../../theme/spacing.dart';

/// Constants for consistent styling across the role details panel
class _RoleDetailsPanelMobileConstants {
  // Private constructor to prevent instantiation
  const _RoleDetailsPanelMobileConstants._();

  // Layout constants
  static const double borderRadius = 20.0;
  static const double sectionVerticalPadding = 16.0;

  // Avatar constants
  static const double avatarRadius = 12.0;
  static const double avatarIconSize = 24.0;

  // Navigation chip constants
  static const double chipBorderRadius = 6.0;
  static const double chipSpacing = 8.0;

  // Spacing constants
  static const double spacingSmall = 8.0;
  static const double spacingMedium = 12.0;

  // Text constants
  static const String fontFamily = "TiemposText";
  static const double titleFontSize = 16.0;
  static const double sectionTitleFontSize = 11.0;
  static const double contentFontSize = 13.0;
  static const double chipFontSize = 12.0;
  static const double userDetailFontSize = 11.0;
  static const double expandedDetailFontSize = 10.0;

  // Colors
  static const Color avatarBackgroundColor = Color(0xffE6F7FF);
  static const Color avatarIconColor = Color(0xff1890FF);
  static const Color activeChipColor = Color(0xffE4EDFF);
  static const Color inactiveChipColor = Color(0xffF6F6F6);

  // Animation constants
  static const Duration scrollAnimationDuration = Duration(milliseconds: 300);
  static const Duration postFrameDelay = Duration(milliseconds: 50);
  static const Duration snackBarDuration = Duration(seconds: 1);

  // Layout constants
  static const double defaultBottomSpacerHeight = 200.0;
  static const double expandedBottomSpacerOffset = 100.0;
  static const int lastSectionsThreshold = 2;
}

/// A mobile-optimized role details panel widget that displays detailed information about a selected role.
///
/// This widget shows role information including use cases and permissions in a structured format
/// optimized for mobile screens with touch-friendly interactions.
class RoleDetailsPanelMobile extends StatefulWidget {
  /// The role information to display
  final RoleInfo role;

  /// Callback when the close button is pressed
  final VoidCallback? onClose;

  /// Chat controller for interactive elements
  final TextEditingController? chatController;

  /// Callback when a message is sent
  final VoidCallback? onSendMessage;

  /// Configuration flag to show legacy sections (Use Cases/Permissions) vs new sections (CR/KPI/DA)
  final bool showLegacySections;

  /// List of users to display in the Users section
  final List<User>? users;

  const RoleDetailsPanelMobile({
    super.key,
    required this.role,
    this.onClose,
    this.chatController,
    this.onSendMessage,
    this.showLegacySections = false,
    this.users,
  });

  @override
  State<RoleDetailsPanelMobile> createState() => _RoleDetailsPanelMobileState();
}

class _RoleDetailsPanelMobileState extends State<RoleDetailsPanelMobile> {
  /// Track expanded state for each user card
  final Set<String> _expandedUserIds = <String>{};

  /// Track active section for navigation styling
  String? _activeSectionId;

  /// Global keys for sections - persists across builds
  late final Map<String, GlobalKey> _roleSectionKeys;

  /// ScrollController for the content area
  late final ScrollController _scrollController;

  /// Map to store visibility information for each section
  final Map<String, VisibilityInfo> _sectionVisibilityInfo = {};

  /// Dynamic bottom spacer height for better scrolling
  double _bottomSpacerHeight =
      _RoleDetailsPanelMobileConstants.defaultBottomSpacerHeight;

  /// Cached section definitions to avoid repeated computation
  late final List<Map<String, String>> _sectionDefinitions;

  /// Timer for debouncing visibility updates
  Timer? _visibilityUpdateTimer;

  /// Flag to prevent updates during programmatic scrolling
  bool _isProgrammaticScrolling = false;

  /// Debounce duration for visibility updates
  static const Duration _visibilityUpdateDelay = Duration(milliseconds: 150);

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeSectionData();
    _setInitialActiveSection();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _visibilityUpdateTimer?.cancel();
    super.dispose();
  }

  /// Initialize controllers and listeners
  void _initializeControllers() {
    _scrollController = ScrollController()..addListener(_onScroll);
  }

  /// Initialize section keys and definitions based on legacy/new mode
  void _initializeSectionData() {
    _sectionDefinitions = _buildSectionDefinitions();
    _roleSectionKeys = Map.fromEntries(
      _sectionDefinitions
          .map((section) => MapEntry(section['id']!, GlobalKey())),
    );
  }

  /// Set the initial active section after frame is built
  void _setInitialActiveSection() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && _sectionDefinitions.isNotEmpty) {
        setState(() {
          _activeSectionId = _sectionDefinitions.first['id'];
        });
      }
    });
  }

  /// Handle scroll events for active section detection
  void _onScroll() {
    // Only update if not programmatically scrolling
    if (!_isProgrammaticScrolling) {
      _debouncedVisibilityUpdate();
    }
  }

  /// Debounced visibility update to prevent flickering
  void _debouncedVisibilityUpdate() {
    _visibilityUpdateTimer?.cancel();
    _visibilityUpdateTimer = Timer(_visibilityUpdateDelay, () {
      _updateActiveSectionFromVisibility();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: _buildContainerDecoration(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          _buildMainContentWithNavigation(),
        ],
      ),
    );
  }

  /// Builds the main container decoration
  BoxDecoration _buildContainerDecoration() {
    return const BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(_RoleDetailsPanelMobileConstants.borderRadius),
      ),
    );
  }

  /// Builds the header section with avatar, title, and close button
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 10, 20, 20),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Row(
        children: [
          _buildAvatar(),
          const SizedBox(width: _RoleDetailsPanelMobileConstants.spacingMedium),
          _buildTitle(),
          _buildCloseButton(),
        ],
      ),
    );
  }

  /// Builds the role avatar
  Widget _buildAvatar() {
    return const CircleAvatar(
      backgroundColor: _RoleDetailsPanelMobileConstants.avatarBackgroundColor,
      radius: _RoleDetailsPanelMobileConstants.avatarRadius,
      child: Icon(
        Icons.person_outline,
        color: _RoleDetailsPanelMobileConstants.avatarIconColor,
        size: _RoleDetailsPanelMobileConstants.avatarIconSize,
      ),
    );
  }

  /// Builds the role title
  Widget _buildTitle() {
    return Expanded(
      child: Text(
        widget.role.title,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: _RoleDetailsPanelMobileConstants.titleFontSize,
          fontFamily: _RoleDetailsPanelMobileConstants.fontFamily,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// Builds the close button
  Widget _buildCloseButton() {
    return GestureDetector(
      onTap: widget.onClose,
      child: const Icon(Icons.close, color: Colors.black, size: 24),
    );
  }

  /// Builds the main content area with vertical navigation chips
  Widget _buildMainContentWithNavigation() {
    return Expanded(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildVerticalNavigationChips(),
          Expanded(
            child: _buildContentArea(),
          ),
        ],
      ),
    );
  }

  /// Builds the vertical navigation chips section
  Widget _buildVerticalNavigationChips() {
    return Container(
      width: 60,
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: _RoleDetailsPanelMobileConstants.spacingMedium,
      ),
      child: Column(
        children: _buildNavigationChipsList(),
      ),
    );
  }

  /// Builds the main content area
  Widget _buildContentArea() {
    return SingleChildScrollView(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.xs),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ..._buildSectionsList(),
          SizedBox(height: _bottomSpacerHeight),
        ],
      ),
    );
  }

  /// Builds section definitions based on legacy/new mode
  List<Map<String, String>> _buildSectionDefinitions() {
    return widget.showLegacySections
        ? const [
            {'id': 'useCases', 'label': 'Use Cases', 'short': 'UC'},
            {'id': 'permissions', 'label': 'Permissions', 'short': 'PR'},
            {'id': 'users', 'label': 'Users', 'short': 'U'},
          ]
        : const [
            {
              'id': 'coreResponsibilities',
              'label': 'Core Responsibilities',
              'short': 'CR'
            },
            {'id': 'kpis', 'label': 'KPIs', 'short': 'KPI'},
            {
              'id': 'decisionAuthority',
              'label': 'Decision Authority',
              'short': 'DA'
            },
            {'id': 'users', 'label': 'Users', 'short': 'U'},
          ];
  }

  /// Builds the list of sections based on legacy/new mode
  List<Widget> _buildSectionsList() {
    return widget.showLegacySections
        ? [
            _buildUseCasesSection(context, _roleSectionKeys),
            _buildPermissionsSection(context, _roleSectionKeys),
          ]
        : [
            _buildCoreResponsibilitiesSection(context, _roleSectionKeys),
            _buildKPIsSection(context, _roleSectionKeys),
            _buildDecisionAuthoritySection(context, _roleSectionKeys),
            _buildUsersSection(context, _roleSectionKeys),
          ];
  }

  /// Scrolls to a specific role section
  void _scrollToRoleSection(String sectionId) {
    final key = _roleSectionKeys[sectionId];
    if (key?.currentContext == null) return;

    // Set programmatic scrolling flag to prevent interference
    _isProgrammaticScrolling = true;

    setState(() {
      _activeSectionId = sectionId;
      _updateBottomSpacerHeight(sectionId);
    });

    Future.delayed(_RoleDetailsPanelMobileConstants.postFrameDelay, () {
      if (_scrollController.hasClients && key!.currentContext != null) {
        Scrollable.ensureVisible(
          key.currentContext!,
          alignment: 0.0,
          duration: _RoleDetailsPanelMobileConstants.scrollAnimationDuration,
          curve: Curves.easeInOut,
        ).then((_) {
          // Reset flag after animation completes
          Future.delayed(const Duration(milliseconds: 100), () {
            _isProgrammaticScrolling = false;
          });
        });
      }
    });
  }

  /// Updates bottom spacer height based on section position
  void _updateBottomSpacerHeight(String sectionId) {
    final sectionIndex =
        _sectionDefinitions.indexWhere((section) => section['id'] == sectionId);

    if (sectionIndex >=
        (_sectionDefinitions.length -
            _RoleDetailsPanelMobileConstants.lastSectionsThreshold)) {
      _bottomSpacerHeight = MediaQuery.of(context).size.height -
          _RoleDetailsPanelMobileConstants.expandedBottomSpacerOffset;
    } else {
      _bottomSpacerHeight =
          _RoleDetailsPanelMobileConstants.defaultBottomSpacerHeight;
    }
  }

  /// Builds the list of navigation chips
  List<Widget> _buildNavigationChipsList() {
    return _sectionDefinitions
        .map((section) => Padding(
              padding: const EdgeInsets.only(
                  bottom: _RoleDetailsPanelMobileConstants.chipSpacing),
              child: _buildNavigationChip(section),
            ))
        .toList();
  }

  /// Builds an individual navigation chip
  Widget _buildNavigationChip(Map<String, String> section) {
    final isActive = _activeSectionId == section['id'];
    final sectionId = section['id']!;

    return GestureDetector(
      onTap: () => _scrollToRoleSection(sectionId),
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: isActive
              ? _RoleDetailsPanelMobileConstants.activeChipColor
              : _RoleDetailsPanelMobileConstants.inactiveChipColor,
          borderRadius: BorderRadius.circular(
              _RoleDetailsPanelMobileConstants.chipBorderRadius),
        ),
        child: Center(
          child: Text(
            section['short']!,
            style: TextStyle(
              fontSize: _RoleDetailsPanelMobileConstants.chipFontSize,
              fontWeight: isActive ? FontWeight.bold : FontWeight.w500,
              color: isActive ? Colors.blue.shade700 : Colors.black,
              fontFamily: _RoleDetailsPanelMobileConstants.fontFamily,
            ),
          ),
        ),
      ),
    );
  }

  /// Builds a generic section widget with VisibilityDetector for mobile
  Widget _buildSection(
    BuildContext context,
    Map<String, GlobalKey> sectionKeys,
    String sectionId,
    String title,
    bool hasData,
    Widget Function() contentBuilder,
  ) {
    return VisibilityDetector(
      key: Key(sectionId),
      onVisibilityChanged: (VisibilityInfo info) {
        _sectionVisibilityInfo[sectionId] = info;
        // Use debounced update to prevent flickering
        _debouncedVisibilityUpdate();
      },
      child: Container(
        key: sectionKeys[sectionId],
        padding: const EdgeInsets.symmetric(
            vertical: _RoleDetailsPanelMobileConstants.sectionVerticalPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle(title),
            const SizedBox(
                height: _RoleDetailsPanelMobileConstants.spacingSmall),
            hasData ? contentBuilder() : _buildNoDataWidget(title),
            const SizedBox(
                height: _RoleDetailsPanelMobileConstants.spacingSmall),
            _buildSectionDivider(),
          ],
        ),
      ),
    );
  }

  /// Builds the section title
  Widget _buildSectionTitle(String title) {
    return Text(
      '$title:',
      style: const TextStyle(
        fontWeight: FontWeight.bold,
        fontSize: _RoleDetailsPanelMobileConstants.sectionTitleFontSize,
        fontFamily: _RoleDetailsPanelMobileConstants.fontFamily,
        color: Colors.black,
      ),
    );
  }

  /// Builds the section divider
  Widget _buildSectionDivider() {
    return Container(
      height: 1,
      color: Colors.grey.shade200,
    );
  }

  /// Builds a no data widget
  Widget _buildNoDataWidget(String sectionName) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        'No $sectionName data available.',
        style: const TextStyle(
          fontFamily: _RoleDetailsPanelMobileConstants.fontFamily,
          fontSize: _RoleDetailsPanelMobileConstants.contentFontSize,
          fontStyle: FontStyle.italic,
          color: Colors.grey,
        ),
      ),
    );
  }

  /// Method to update active section based on visibility
  void _updateActiveSectionFromVisibility() {
    if (_sectionVisibilityInfo.isEmpty || _isProgrammaticScrolling) return;

    String? bestSection;

    // Simple approach: Find the section that is most prominently visible
    // Priority order:
    // 1. Any section that is 100% visible (perfect for small sections)
    // 2. Section with highest visibility that's also well-positioned

    double bestVisibility = 0;
    String? bestFullyVisibleSection;

    // First, check for any fully or nearly fully visible sections
    for (final sectionDef in _sectionDefinitions) {
      final sectionId = sectionDef['id']!;
      final visibilityInfo = _sectionVisibilityInfo[sectionId];

      if (visibilityInfo != null) {
        final visibleFraction = visibilityInfo.visibleFraction;
        final bounds = visibilityInfo.visibleBounds;

        // Perfect visibility - this is ideal for small sections
        if (visibleFraction >= 0.99) {
          bestFullyVisibleSection = sectionId;
          break; // Found a perfect match, use it immediately
        }

        // High visibility with good positioning
        if (visibleFraction >= 0.8 && bounds.top <= 0.2) {
          if (visibleFraction > bestVisibility) {
            bestVisibility = visibleFraction;
            bestSection = sectionId;
          }
        }

        // For sections with decent visibility, check if they're prominently positioned
        if (visibleFraction >= 0.5) {
          // Section starts near the top of viewport - good candidate
          if (bounds.top <= 0.1) {
            if (visibleFraction > bestVisibility) {
              bestVisibility = visibleFraction;
              bestSection = sectionId;
            }
          }
          // Section is well-centered in viewport
          else if (bounds.top >= 0.1 &&
              bounds.bottom <= 0.9 &&
              visibleFraction >= 0.7) {
            if (visibleFraction > bestVisibility) {
              bestVisibility = visibleFraction;
              bestSection = sectionId;
            }
          }
        }
      }
    }

    // Use fully visible section if found (prioritizes small sections)
    if (bestFullyVisibleSection != null) {
      bestSection = bestFullyVisibleSection;
    }

    // If no good candidate found, fall back to highest visibility
    if (bestSection == null) {
      double maxVisibility = 0;
      for (final sectionDef in _sectionDefinitions) {
        final sectionId = sectionDef['id']!;
        final visibilityInfo = _sectionVisibilityInfo[sectionId];

        if (visibilityInfo != null &&
            visibilityInfo.visibleFraction > maxVisibility) {
          maxVisibility = visibilityInfo.visibleFraction;
          bestSection = sectionId;
        }
      }
    }

    // Update active section with minimal resistance for small sections
    if (bestSection != null && bestSection != _activeSectionId) {
      // For fully visible sections, update immediately
      if (bestFullyVisibleSection != null) {
        if (mounted) {
          setState(() {
            _activeSectionId = bestSection;
          });
        }
      } else {
        // For other sections, check if change is significant enough
        final currentInfo = _activeSectionId != null
            ? _sectionVisibilityInfo[_activeSectionId!]
            : null;
        final newInfo = _sectionVisibilityInfo[bestSection];

        if (currentInfo == null || newInfo == null) {
          // No current info or new info, safe to update
          if (mounted) {
            setState(() {
              _activeSectionId = bestSection;
            });
          }
        } else {
          // Only switch if new section is significantly more visible or better positioned
          final currentVisibility = currentInfo.visibleFraction;
          final newVisibility = newInfo.visibleFraction;
          final newBounds = newInfo.visibleBounds;

          bool shouldSwitch = false;

          // New section is much more visible
          if (newVisibility > currentVisibility + 0.2) {
            shouldSwitch = true;
          }
          // New section is at top of viewport and reasonably visible
          else if (newBounds.top <= 0.1 &&
              newVisibility >= 0.6 &&
              newVisibility > currentVisibility + 0.1) {
            shouldSwitch = true;
          }
          // Current section is barely visible and new section is decent
          else if (currentVisibility < 0.3 && newVisibility >= 0.5) {
            shouldSwitch = true;
          }

          if (shouldSwitch && mounted) {
            setState(() {
              _activeSectionId = bestSection;
            });
          }
        }
      }
    }
  }

  /// Builds the core responsibilities section
  Widget _buildCoreResponsibilitiesSection(
      BuildContext context, Map<String, GlobalKey> roleSectionKeys) {
    return _buildSection(
      context,
      roleSectionKeys,
      'coreResponsibilities',
      'Core Responsibilities',
      widget.role.coreResponsibilities != null &&
          widget.role.coreResponsibilities!.isNotEmpty,
      () => _buildCoreResponsibilitiesContent(context),
    );
  }

  /// Builds the core responsibilities content
  Widget _buildCoreResponsibilitiesContent(BuildContext context) {
    final responsibilities = widget.role.coreResponsibilities;
    if (responsibilities == null || responsibilities.isEmpty) {
      return _buildNoDataWidget('core responsibilities');
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: responsibilities
          .map((responsibility) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: GestureDetector(
                  onTap: () =>
                      _showChatDialog(context, 'core responsibilities'),
                  child: Text(
                    responsibility,
                    style: const TextStyle(
                      fontSize:
                          _RoleDetailsPanelMobileConstants.contentFontSize,
                      color: Colors.black,
                      fontFamily: _RoleDetailsPanelMobileConstants.fontFamily,
                    ),
                  ),
                ),
              ))
          .toList(),
    );
  }

  /// Builds the KPIs section
  Widget _buildKPIsSection(
      BuildContext context, Map<String, GlobalKey> roleSectionKeys) {
    return _buildSection(
      context,
      roleSectionKeys,
      'kpis',
      'Key Performance Indicators',
      widget.role.kpis != null && widget.role.kpis!.isNotEmpty,
      () => _buildKPIsContent(context),
    );
  }

  /// Builds the KPIs content
  Widget _buildKPIsContent(BuildContext context) {
    final kpis = widget.role.kpis;
    if (kpis == null || kpis.isEmpty) {
      return _buildNoDataWidget('key performance indicators');
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: kpis.map((kpi) => _buildKPIItem(context, kpi)).toList(),
    );
  }

  /// Builds an individual KPI item
  Widget _buildKPIItem(BuildContext context, dynamic kpi) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '• ',
                style: TextStyle(
                  fontSize: _RoleDetailsPanelMobileConstants.contentFontSize,
                  color: Colors.black,
                  fontFamily: _RoleDetailsPanelMobileConstants.fontFamily,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () =>
                      _showChatDialog(context, 'key performance indicators'),
                  child: RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: kpi.name,
                          style: const TextStyle(
                            fontSize: _RoleDetailsPanelMobileConstants
                                .contentFontSize,
                            color: Colors.black,
                            fontWeight: FontWeight.bold,
                            fontFamily:
                                _RoleDetailsPanelMobileConstants.fontFamily,
                          ),
                        ),
                        TextSpan(
                          text: ": ${kpi.description}",
                          style: const TextStyle(
                            fontSize: _RoleDetailsPanelMobileConstants
                                .contentFontSize,
                            color: Colors.black,
                            fontFamily:
                                _RoleDetailsPanelMobileConstants.fontFamily,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
          ..._buildKPIDetails(kpi),
        ],
      ),
    );
  }

  /// Builds KPI detail items (formula, target, measurement frequency)
  List<Widget> _buildKPIDetails(dynamic kpi) {
    final details = <Widget>[];

    if (kpi.formula != null) {
      details.add(_buildKPIDetailItem('Formula', kpi.formula));
    }
    if (kpi.target != null) {
      details.add(_buildKPIDetailItem('Target', kpi.target));
    }
    if (kpi.measurementFrequency != null) {
      details.add(_buildKPIDetailItem(
          'Measurement Frequency', kpi.measurementFrequency));
    }

    return details;
  }

  /// Builds a KPI detail item
  Widget _buildKPIDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(left: 8),
      child: Text(
        '- $label: $value',
        style: const TextStyle(
          fontSize: _RoleDetailsPanelMobileConstants.contentFontSize,
          color: Colors.black,
          fontFamily: _RoleDetailsPanelMobileConstants.fontFamily,
        ),
      ),
    );
  }

  /// Builds the decision authority section
  Widget _buildDecisionAuthoritySection(
      BuildContext context, Map<String, GlobalKey> roleSectionKeys) {
    return _buildSection(
      context,
      roleSectionKeys,
      'decisionAuthority',
      'Decision Authority',
      widget.role.decisionAuthority != null &&
          widget.role.decisionAuthority!.isNotEmpty,
      () => _buildDecisionAuthorityContent(context),
    );
  }

  /// Builds the decision authority content
  Widget _buildDecisionAuthorityContent(BuildContext context) {
    final authorities = widget.role.decisionAuthority;
    if (authorities == null || authorities.isEmpty) {
      return _buildNoDataWidget('decision authority');
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: authorities
          .map((authority) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: GestureDetector(
                  onTap: () => _showChatDialog(context, 'decision authority'),
                  child: Text(
                    authority,
                    style: const TextStyle(
                      fontSize:
                          _RoleDetailsPanelMobileConstants.contentFontSize,
                      color: Colors.black,
                      fontFamily: _RoleDetailsPanelMobileConstants.fontFamily,
                    ),
                  ),
                ),
              ))
          .toList(),
    );
  }

  /// Builds the users section
  Widget _buildUsersSection(
      BuildContext context, Map<String, GlobalKey> roleSectionKeys) {
    return _buildSection(
      context,
      roleSectionKeys,
      'users',
      'Users',
      widget.users != null && widget.users!.isNotEmpty,
      () => _buildUsersContent(context),
    );
  }

  /// Builds the users content
  Widget _buildUsersContent(BuildContext context) {
    List<User> assignedUsers = _getAssignedUsers();

    if (assignedUsers.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: assignedUsers.map((user) {
          final userId = _getUserId(user);
          final isExpanded = _expandedUserIds.contains(userId);

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // User card
              _buildUserCard(context, user, userId, isExpanded),
              // Expanded details (if expanded)
              if (isExpanded) ...[
                SizedBox(height: 8),
                _buildExpandedUserDetails(context, user),
              ],
              // Add spacing between cards
              SizedBox(height: 12),
            ],
          );
        }).toList(),
      );
    }

    return _buildNoDataWidget('users');
  }

  /// Get users assigned to this role
  List<User> _getAssignedUsers() {
    if (widget.users == null || widget.users!.isEmpty) {
      return [];
    }

    return widget.users!.where((user) {
      if (user.roleAssignments != null) {
        final primaryRole = user.roleAssignments!.primaryRole;
        return primaryRole == widget.role.title ||
            primaryRole == widget.role.id;
      }
      return false;
    }).toList();
  }

  /// Builds a user card widget
  Widget _buildUserCard(
      BuildContext context, User user, String userId, bool isExpanded) {
    return GestureDetector(
      onTap: () => _toggleUserExpansion(userId),
      child: Container(
        padding: EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Colors.blue.shade200, width: 1),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 3,
              offset: Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with user name and expand icon
            Row(
              children: [
                Expanded(
                  child: Text(
                    'User - ${user.name ?? 'Unknown User'}:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                      fontFamily: "TiemposText",
                      color: Colors.black,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Icon(
                  isExpanded
                      ? Icons.keyboard_arrow_down
                      : Icons.keyboard_arrow_right,
                  size: 16,
                  color: Colors.grey.shade600,
                ),
              ],
            ),
            SizedBox(height: 8),
            // Primary user details
            _buildPrimaryUserDetails(user),
          ],
        ),
      ),
    );
  }

  /// Builds primary user details for the card
  Widget _buildPrimaryUserDetails(User user) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (user.name != null) _buildUserDetailItem('Full Name', user.name!),
        if (user.personalInformation?.email != null)
          _buildUserDetailItem('Email', user.personalInformation!.email!),
        if (user.personalInformation?.phone != null)
          _buildUserDetailItem('Phone', user.personalInformation!.phone!),
        if (user.personalInformation?.employeeId != null)
          _buildUserDetailItem(
              'Employee ID', user.personalInformation!.employeeId!),
      ],
    );
  }

  /// Builds a user detail item
  Widget _buildUserDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 2),
      child: Text(
        '• $label: $value',
        style: const TextStyle(
          fontSize: _RoleDetailsPanelMobileConstants.userDetailFontSize,
          fontFamily: _RoleDetailsPanelMobileConstants.fontFamily,
          color: Colors.black87,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// Get user ID for tracking expanded state
  String _getUserId(User user) {
    return user.name ?? 'unknown_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Toggle user card expansion
  void _toggleUserExpansion(String userId) {
    setState(() {
      if (_expandedUserIds.contains(userId)) {
        _expandedUserIds.remove(userId);
      } else {
        _expandedUserIds.add(userId);
      }
    });
  }

  /// Builds expanded user details for mobile
  Widget _buildExpandedUserDetails(BuildContext context, User user) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Authentication section
          if (user.authentication != null) ...[
            _buildExpandedSection('Authentication', [
              if (user.authentication!.username != null)
                'Username: ${user.authentication!.username}',
              if (user.authentication!.passwordPolicy != null)
                'Password Policy: ${user.authentication!.passwordPolicy}',
              if (user.authentication!.multiFactorAuthentication != null)
                'Multi-Factor Authentication: ${user.authentication!.multiFactorAuthentication}',
              if (user.authentication!.lastPasswordChange != null)
                'Last Password Change: ${user.authentication!.lastPasswordChange?.toIso8601String().split("T")[0]}',
            ]),
          ],

          // Role Assignments section
          if (user.roleAssignments != null) ...[
            _buildExpandedSection('Role Assignments', [
              if (user.roleAssignments!.primaryRole != null)
                'Primary Role: ${user.roleAssignments!.primaryRole}',
              if (user.roleAssignments!.secondaryRoles != null &&
                  user.roleAssignments!.secondaryRoles!.isNotEmpty)
                'Secondary Roles: ${user.roleAssignments!.secondaryRoles!.join(", ")}',
            ]),
          ],

          // Department and Team
          if (user.department != null || user.team != null) ...[
            _buildExpandedSection('Organization', [
              if (user.department != null) 'Department: ${user.department}',
              if (user.team != null) 'Team: ${user.team}',
            ]),
          ],

          // Reporting Structure
          if (user.reportingStructure != null) ...[
            _buildExpandedSection('Reporting Structure', [
              if (user.reportingStructure!.reportsTo != null)
                'Reports to: ${user.reportingStructure!.reportsTo}',
              if (user.reportingStructure!.directReports != null &&
                  user.reportingStructure!.directReports!.isNotEmpty)
                'Direct Reports: ${user.reportingStructure!.directReports!.join(", ")}',
            ]),
          ],

          // Access Control
          if (user.accessControl != null) ...[
            _buildExpandedSection('Access Control', [
              if (user.accessControl!.accountStatus != null)
                'Account Status: ${user.accessControl!.accountStatus}',
              if (user.accessControl!.accessLevel != null)
                'Access Level: ${user.accessControl!.accessLevel}',
              if (user.accessControl!.ipRestrictions != null)
                'IP Restrictions: ${user.accessControl!.ipRestrictions}',
              if (user.accessControl!.timeRestrictions != null)
                'Time Restrictions: ${user.accessControl!.timeRestrictions}',
            ]),
          ],

          // System Permissions
          if (user.systemPermissions != null) ...[
            _buildExpandedSection('System Permissions', [
              if (user.systemPermissions!.dataAccessScope != null)
                'Data Access Scope: ${user.systemPermissions!.dataAccessScope}',
              if (user.systemPermissions!.specialPermissions != null &&
                  user.systemPermissions!.specialPermissions!.isNotEmpty)
                'Special Permissions: ${user.systemPermissions!.specialPermissions!.join(", ")}',
            ]),
          ],

          // Activity Tracking
          if (user.activityTracking != null) ...[
            _buildExpandedSection('Activity Tracking', [
              if (user.activityTracking!.accountCreated != null)
                'Account Created: ${user.activityTracking!.accountCreated?.toIso8601String().split("T")[0]}',
              if (user.activityTracking!.lastLogin != null)
                'Last Login: ${user.activityTracking!.lastLogin?.toIso8601String().split("T")[0]}',
              if (user.activityTracking!.lastActivity != null)
                'Last Activity: ${user.activityTracking!.lastActivity?.toIso8601String().split("T")[0]}',
            ]),
          ],
        ],
      ),
    );
  }

  /// Builds an expanded section with title and items
  Widget _buildExpandedSection(String title, List<String> items) {
    if (items.isEmpty) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '• $title:',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: _RoleDetailsPanelMobileConstants.userDetailFontSize,
              fontFamily: _RoleDetailsPanelMobileConstants.fontFamily,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 2),
          ...items.map((item) => Padding(
                padding: const EdgeInsets.only(left: 8, bottom: 1),
                child: Text(
                  '- $item',
                  style: const TextStyle(
                    fontSize:
                        _RoleDetailsPanelMobileConstants.expandedDetailFontSize,
                    fontFamily: _RoleDetailsPanelMobileConstants.fontFamily,
                    color: Colors.black87,
                  ),
                ),
              )),
        ],
      ),
    );
  }

  /// Legacy sections for backward compatibility
  Widget _buildUseCasesSection(
      BuildContext context, Map<String, GlobalKey> roleSectionKeys) {
    return _buildSection(
      context,
      roleSectionKeys,
      'useCases',
      'Use Cases',
      widget.role.useCases != null && widget.role.useCases!.isNotEmpty,
      () => _buildUseCasesContent(context),
    );
  }

  Widget _buildUseCasesContent(BuildContext context) {
    final useCases = widget.role.useCases;
    if (useCases == null || useCases.isEmpty) {
      return _buildNoDataWidget('use cases');
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: useCases
          .map((useCase) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '• ',
                      style: TextStyle(
                        fontSize:
                            _RoleDetailsPanelMobileConstants.contentFontSize,
                        color: Colors.black,
                        fontFamily: _RoleDetailsPanelMobileConstants.fontFamily,
                      ),
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: () => _showChatDialog(context, 'use cases'),
                        child: Text(
                          useCase,
                          style: const TextStyle(
                            fontSize: _RoleDetailsPanelMobileConstants
                                .contentFontSize,
                            color: Colors.black,
                            fontFamily:
                                _RoleDetailsPanelMobileConstants.fontFamily,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ))
          .toList(),
    );
  }

  Widget _buildPermissionsSection(
      BuildContext context, Map<String, GlobalKey> roleSectionKeys) {
    return _buildSection(
      context,
      roleSectionKeys,
      'permissions',
      'Permissions',
      widget.role.permissions != null && widget.role.permissions!.isNotEmpty,
      () => _buildPermissionsContent(context),
    );
  }

  Widget _buildPermissionsContent(BuildContext context) {
    // Get entity permissions
    List<String> entityPermissions = widget.role.permissions?['entities'] ?? [];
    // Get objective permissions
    List<String> objectivePermissions =
        widget.role.permissions?['objectives'] ?? [];

    if (entityPermissions.isNotEmpty || objectivePermissions.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Entity permissions
          if (entityPermissions.isNotEmpty) ...[
            Text(
              'Entity Permissions:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                fontFamily: "TiemposText",
              ),
            ),
            SizedBox(height: 4),
            ...entityPermissions.map((permission) => Padding(
                  padding: EdgeInsets.only(bottom: 4, left: 8),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('• ',
                          style: TextStyle(
                              fontSize: 14,
                              color: Colors.black,
                              fontFamily: "TiemposText")),
                      Expanded(
                        child: GestureDetector(
                          onTap: () => _showChatDialog(context, 'permissions'),
                          child: Text(
                            permission,
                            style: TextStyle(
                                fontSize: 14,
                                color: Colors.black,
                                fontFamily: "TiemposText"),
                          ),
                        ),
                      ),
                    ],
                  ),
                )),
            SizedBox(height: 8),
          ],

          // Objective permissions
          if (objectivePermissions.isNotEmpty) ...[
            Text(
              'Objective Permissions:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                fontFamily: "TiemposText",
              ),
            ),
            SizedBox(height: 4),
            ...objectivePermissions.map((permission) => Padding(
                  padding: EdgeInsets.only(bottom: 4, left: 8),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('• ',
                          style: TextStyle(
                              fontSize: 14,
                              color: Colors.black,
                              fontFamily: "TiemposText")),
                      Expanded(
                        child: GestureDetector(
                          onTap: () => _showChatDialog(context, 'permissions'),
                          child: Text(
                            permission,
                            style: TextStyle(
                                fontSize: 14,
                                color: Colors.black,
                                fontFamily: "TiemposText"),
                          ),
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ],
      );
    }

    return _buildNoDataWidget('permissions');
  }

  /// Show chat dialog (placeholder for now)
  void _showChatDialog(BuildContext context, String section) {
    // Placeholder for chat functionality
    // This would typically open a chat interface or send a message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Chat about $section'),
        duration: _RoleDetailsPanelMobileConstants.snackBarDuration,
      ),
    );
  }
}
