import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// Constants for consistent styling across the comments bottom sheet
class _CommentsBottomSheetConstants {
  static const double handleBarWidth = 40.0;
  static const double handleBarHeight = 4.0;
  static const double handleBarVerticalMargin = 12.0;
  static const double headerPadding = 16.0;
  static const double iconSize = 24.0;
  static const double chatIconSize = 24.0;
  static const double borderRadius = 20.0;
  static const double contentPadding = 16.0;
  static const double avatarRadius = 16.0;
  static const double cardBorderRadius = 8.0;
  static const double cardMarginBottom = 16.0;
  static const double cardPadding = 12.0;
  static const double spacingSmall = 8.0;
  static const double spacingMedium = 16.0;

  static const String fontFamily = "TiemposText";
  static const String defaultTitle = 'All Comments';
  static const String noCommentsMessage = 'No comments yet';
}

/// A mobile-optimized comments bottom sheet widget for displaying and adding comments.
///
/// This widget provides a comments interface in a bottom sheet format with
/// a clean header design matching the provided mockup. It supports displaying
/// existing comments and provides callbacks for message handling.
class CommentsBottomSheet extends StatefulWidget {
  /// Chat controller for the input field
  final TextEditingController? chatController;

  /// Callback when a message is sent
  final VoidCallback? onSendMessage;

  /// Initial context or topic for the comments
  final String? context;

  const CommentsBottomSheet({
    super.key,
    this.chatController,
    this.onSendMessage,
    this.context,
  });

  @override
  State<CommentsBottomSheet> createState() => _CommentsBottomSheetState();
}

class _CommentsBottomSheetState extends State<CommentsBottomSheet> {
  /// List of comments to display
  List<Comment> comments = [];

  @override
  void initState() {
    super.initState();
    _initializeComments();
  }

  /// Initialize comments list - can be extended to load from API
  void _initializeComments() {
    comments = [
      // Empty for now - will be populated with actual comments
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(_CommentsBottomSheetConstants.borderRadius),
        ),
      ),
      child: Column(
        children: [
          _buildHandleBar(),
          _buildHeader(),
          _buildContentArea(),
        ],
      ),
    );
  }

  /// Builds the drag handle bar at the top
  Widget _buildHandleBar() {
    return Container(
      margin: EdgeInsets.symmetric(
        vertical: _CommentsBottomSheetConstants.handleBarVerticalMargin,
      ),
      width: _CommentsBottomSheetConstants.handleBarWidth,
      height: _CommentsBottomSheetConstants.handleBarHeight,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  /// Builds the header with title and action buttons
  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(_CommentsBottomSheetConstants.headerPadding),
      child: Row(
        children: [
          _buildCloseButton(),
          SizedBox(width: _CommentsBottomSheetConstants.spacingMedium),
          _buildTitle(),
          _buildChatIcon(),
        ],
      ),
    );
  }

  /// Builds the close button
  Widget _buildCloseButton() {
    return GestureDetector(
      onTap: () => Navigator.pop(context),
      child: Icon(
        Icons.close,
        color: Colors.black,
        size: _CommentsBottomSheetConstants.iconSize,
      ),
    );
  }

  /// Builds the title text
  Widget _buildTitle() {
    return Expanded(
      child: Text(
        _CommentsBottomSheetConstants.defaultTitle,
        style: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 16,
          fontFamily: _CommentsBottomSheetConstants.fontFamily,
          color: Colors.black,
        ),
      ),
    );
  }

  /// Builds the chat icon
  Widget _buildChatIcon() {
    return GestureDetector(
      onTap: () {
        // Handle chat icon tap if needed
      },
      child: SvgPicture.asset(
        'assets/images/comments_icon.svg',
        width: _CommentsBottomSheetConstants.chatIconSize,
        height: _CommentsBottomSheetConstants.chatIconSize,
      ),
    );
  }

  /// Builds the main content area
  Widget _buildContentArea() {
    return Expanded(
      child: Container(
        padding: EdgeInsets.all(_CommentsBottomSheetConstants.contentPadding),
        child: comments.isEmpty ? _buildEmptyState() : _buildCommentsList(),
      ),
    );
  }

  /// Builds the empty state when no comments are available
  Widget _buildEmptyState() {
    return Center(
      child: Text(
        _CommentsBottomSheetConstants.noCommentsMessage,
        style: TextStyle(
          fontSize: 16,
          color: Colors.grey.shade600,
          fontFamily: _CommentsBottomSheetConstants.fontFamily,
        ),
      ),
    );
  }

  /// Builds the comments list
  Widget _buildCommentsList() {
    return ListView.builder(
      itemCount: comments.length,
      itemBuilder: (context, index) => _buildCommentItem(comments[index]),
    );
  }

  /// Builds an individual comment item with proper styling and layout
  Widget _buildCommentItem(Comment comment) {
    return Container(
      margin: EdgeInsets.only(
          bottom: _CommentsBottomSheetConstants.cardMarginBottom),
      padding: EdgeInsets.all(_CommentsBottomSheetConstants.cardPadding),
      decoration: _buildCommentCardDecoration(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCommentHeader(comment),
          SizedBox(height: _CommentsBottomSheetConstants.spacingSmall),
          _buildCommentText(comment),
        ],
      ),
    );
  }

  /// Builds the decoration for comment cards
  BoxDecoration _buildCommentCardDecoration() {
    return BoxDecoration(
      color: Colors.grey.shade50,
      borderRadius:
          BorderRadius.circular(_CommentsBottomSheetConstants.cardBorderRadius),
      border: Border.all(color: Colors.grey.shade200),
    );
  }

  /// Builds the comment header with avatar and author info
  Widget _buildCommentHeader(Comment comment) {
    return Row(
      children: [
        _buildAuthorAvatar(comment),
        SizedBox(width: _CommentsBottomSheetConstants.spacingSmall),
        Expanded(child: _buildAuthorInfo(comment)),
      ],
    );
  }

  /// Builds the author avatar
  Widget _buildAuthorAvatar(Comment comment) {
    return CircleAvatar(
      backgroundColor: Colors.blue.shade100,
      radius: _CommentsBottomSheetConstants.avatarRadius,
      child: Text(
        _getAuthorInitial(comment.author),
        style: TextStyle(
          color: Colors.blue.shade700,
          fontWeight: FontWeight.bold,
          fontSize: 14,
        ),
      ),
    );
  }

  /// Builds the author information section
  Widget _buildAuthorInfo(Comment comment) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          comment.author,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
            fontFamily: _CommentsBottomSheetConstants.fontFamily,
          ),
        ),
        Text(
          _formatTimestamp(comment.timestamp),
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
            fontFamily: _CommentsBottomSheetConstants.fontFamily,
          ),
        ),
      ],
    );
  }

  /// Builds the comment text content
  Widget _buildCommentText(Comment comment) {
    return Text(
      comment.text,
      style: TextStyle(
        fontSize: 14,
        color: Colors.black87,
        fontFamily: _CommentsBottomSheetConstants.fontFamily,
        height: 1.4,
      ),
    );
  }

  /// Gets the first letter of author name for avatar
  String _getAuthorInitial(String author) {
    return author.isNotEmpty ? author.substring(0, 1).toUpperCase() : '?';
  }

  /// Formats timestamp into human-readable relative time
  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

/// Model class representing a comment in the chat interface
///
/// This class encapsulates all the necessary information for displaying
/// a comment including the content, author information, and timestamp.
class Comment {
  /// The text content of the comment
  final String text;

  /// The name of the comment author
  final String author;

  /// When the comment was created
  final DateTime timestamp;

  /// Creates a new Comment instance
  ///
  /// All parameters are required to ensure complete comment data.
  const Comment({
    required this.text,
    required this.author,
    required this.timestamp,
  });

  /// Creates a Comment from a JSON map
  ///
  /// Useful for deserializing comments from API responses.
  factory Comment.fromJson(Map<String, dynamic> json) {
    return Comment(
      text: json['text'] as String? ?? '',
      author: json['author'] as String? ?? 'Unknown',
      timestamp: json['timestamp'] != null
          ? DateTime.parse(json['timestamp'] as String)
          : DateTime.now(),
    );
  }

  /// Converts the Comment to a JSON map
  ///
  /// Useful for serializing comments for API requests.
  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'author': author,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  /// Creates a copy of this Comment with optionally updated values
  Comment copyWith({
    String? text,
    String? author,
    DateTime? timestamp,
  }) {
    return Comment(
      text: text ?? this.text,
      author: author ?? this.author,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Comment &&
        other.text == text &&
        other.author == author &&
        other.timestamp == timestamp;
  }

  @override
  int get hashCode => Object.hash(text, author, timestamp);

  @override
  String toString() {
    return 'Comment(text: $text, author: $author, timestamp: $timestamp)';
  }
}
