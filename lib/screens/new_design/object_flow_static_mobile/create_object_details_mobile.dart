import 'package:flutter/material.dart';
import 'package:nsl/providers/object_creation_provider.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:provider/provider.dart';

class CreateObjectDetailsMobile extends StatefulWidget {
  const CreateObjectDetailsMobile({super.key});

  @override
  State<CreateObjectDetailsMobile> createState() =>
      _CreateObjectDetailsMobileState();
}

// Data model for attributes
class AttributeModel {
  final String name;
  final String displayName;
  final String dataType;
  final bool isRequired;
  final bool isUnique;

  AttributeModel({
    required this.name,
    required this.displayName,
    required this.dataType,
    required this.isRequired,
    required this.isUnique,
  });
}

class _CreateObjectDetailsMobileState extends State<CreateObjectDetailsMobile> {
  final TextEditingController _objectController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final ScrollController _tableScrollController = ScrollController();
  String? _selectedType;
  String? _selectedIcon;
  bool _showExpansionPanels =
      false; // Start with form, show panels after validation
  Set<String> _expandedPanels = {}; // Track which panels are expanded

  // Expansion states for individual sections
  bool _isEntityRelationshipExpanded = false;
  bool _isEnumeratedValuesExpanded = false;

  // List to store attributes
  List<AttributeModel> _attributes = [];

  final List<String> _typeOptions = [
    'Entity',
    'Service',
    'Component',
    'Interface',
    'Process',
  ];

  @override
  void dispose() {
    _objectController.dispose();
    _descriptionController.dispose();
    _tableScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ObjectCreationProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          appBar: AppBar(
            surfaceTintColor: Colors.transparent,
            leading: Builder(
              builder: (context) => IconButton(
                icon: Icon(Icons.menu),
                onPressed: () {
                  Scaffold.of(context).openDrawer();
                },
              ),
            ),
            title: Text(''),
            backgroundColor: Color(0xffF7F9FB),
            elevation: 0,
            iconTheme: IconThemeData(color: Colors.black),
          ),
          drawer: const CustomDrawer(),
          backgroundColor: Colors.white,
          body: SafeArea(
            child: Column(
              children: [
                // Header
                _buildHeader(context),

                // Content area
                Expanded(
                  child: _buildContent(context, provider),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.black,
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E7EB), width: .5),
        ),
        boxShadow: [
          BoxShadow(
            color: Color(0x1A000000),
            blurRadius: 8,
            offset: Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.arrow_back,
              color: Colors.white,
              size: 24,
            ),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
          const SizedBox(width: 8),
          // Title
          Expanded(
            child: Text(
              'Create Object Details',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.titleSmall(context),
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.white,
                fontWeight: FontWeight.bold,
                height: 1,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context, ObjectCreationProvider provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(
          scrollbars: false,
          physics: const AlwaysScrollableScrollPhysics(),
        ),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height - 200,
            ),
            child: _buildContentWithLineNumbers(context, provider),
          ),
        ),
      ),
    );
  }

  Widget _buildContentWithLineNumbers(
      BuildContext context, ObjectCreationProvider provider) {
    int lineNumber = 1;
    final List<Widget> allWidgets = [];

    if (!_showExpansionPanels) {
      // Line 1: Object Detail header
      allWidgets
          .add(_buildLineWithNumber(lineNumber++, _buildObjectDetailHeader()));

      // Line 2: Object input field
      allWidgets.add(_buildLineWithNumber(lineNumber++, _buildObjectField()));

      // Line 3: Select Type and Icon row
      allWidgets
          .add(_buildLineWithNumber(lineNumber++, _buildTypeAndIconRow()));

      // Line 4: Description field
      allWidgets
          .add(_buildLineWithNumber(lineNumber++, _buildDescriptionField()));

      // Validate button (without line number)
      allWidgets.add(_buildValidateButton(provider));
    } else {
      // Show expansion panels after validation - restore original design
      allWidgets.add(_buildLineWithNumber(lineNumber++, _buildObjectHeader()));
      allWidgets.add(const SizedBox(height: 8));
      allWidgets.addAll(_buildExpansionPanelsWithLineNumbers(lineNumber));
    }

    return Stack(
      children: [
        // Content
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: allWidgets,
        ),
      ],
    );
  }

  Widget _buildLineWithNumber(int lineNumber, Widget content) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Line number
        Container(
          width: 20,
          child: Text(
            '$lineNumber',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
        ),
        // Space for the continuous vertical line
        const SizedBox(width: 17), // 8px margin + 1px line + 8px margin
        // Content
        Expanded(child: content),
      ],
    );
  }

  Widget _buildObjectDetailHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Object Detail',
          style: TextStyle(
            fontSize: ResponsiveFontSizes.bodyLarge(context),
            fontWeight: FontWeight.w700,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 12), // Bottom spacing
      ],
    );
  }

  Widget _buildObjectField() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8), // ✅ Bottom spacing
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Object',
            style: TextStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            height: 40,
            decoration: BoxDecoration(
              border: Border.all(
                color: const Color(0xFF7F7F7F),
                width: 0.5,
              ),
              borderRadius: BorderRadius.circular(6),
            ),
            alignment: Alignment.center, // ✅ Forces vertical centering
            child: TextField(
              controller: _objectController,
              decoration: const InputDecoration(
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                fillColor: Colors.transparent,
                hoverColor: Colors.transparent,
                contentPadding: EdgeInsets.symmetric(horizontal: 12),
                isDense: true,
              ),
              style: TextStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypeAndIconRow() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8), // ✅ Bottom spacing
      child: Row(
        children: [
          // Select Type section
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Select Type',
                  style: TextStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  height: 40,
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: const Color(0xFF7F7F7F),
                      width: 0.5,
                    ),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      value: _selectedType,
                      hint: Text(
                        'Select Type',
                        style: TextStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                      icon: Icon(Icons.keyboard_arrow_down,
                          color: Colors.grey.shade600),
                      isExpanded: true,
                      items: _typeOptions.map((String type) {
                        return DropdownMenuItem<String>(
                          value: type,
                          child: Text(
                            type,
                            style: TextStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        );
                      }).toList(),
                      onChanged: (String? newValue) {
                        setState(() {
                          _selectedType = newValue;
                        });
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          // Icon section
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Icon (64x64 Pixel):',
                  style: TextStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  height: 40,
                  child: ElevatedButton(
                    onPressed: () {
                      // Handle browse functionality
                      _showIconSelector(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      elevation: 0,
                      shadowColor: Colors.transparent,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                        side: const BorderSide(
                          color: Color(0xFF7F7F7F),
                          width: 0.5,
                        ),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Browse',
                          style: TextStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDescriptionField() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16), // ✅ Bottom spacing,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Description:',
            style: TextStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            height: 120,
            decoration: BoxDecoration(
              border: Border.all(
                color: const Color(0xFF7F7F7F),
                width: 0.5,
              ),
              borderRadius: BorderRadius.circular(6),
            ),
            child: TextField(
              controller: _descriptionController,
              maxLines: null,
              expands: true,
              textAlignVertical: TextAlignVertical.top,
              decoration: InputDecoration(
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                fillColor: Colors.transparent,
                hoverColor: Colors.transparent,
                contentPadding: const EdgeInsets.all(12),
                hintText:
                    'Comprehensive customer onboarding process from registration to welcome completion',
                hintStyle: TextStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              style: TextStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildValidateButton(ObjectCreationProvider provider) {
    return Center(
      child: Container(
        width: double.infinity,
        height: 40,
        margin: const EdgeInsets.only(left: 36), // ✅ Only left margin
        child: ElevatedButton(
          onPressed: () {
            _handleValidation(provider);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFF2F2F2),
            elevation: 0,
            shadowColor: Colors.transparent,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: const BorderSide(
                color: Color(0xFFE5E5E5),
                width: .5,
              ),
            ),
          ),
          child: Text(
            'Validate',
            style: TextStyle(
              fontSize: ResponsiveFontSizes.titleLarge(context),
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: const Color(0xFF333333),
            ),
          ),
        ),
      ),
    );
  }

  void _showIconSelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: 200,
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Select Icon',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: GridView.count(
                  crossAxisCount: 4,
                  children: [
                    _buildIconOption(Icons.person, 'Person'),
                    _buildIconOption(Icons.business, 'Business'),
                    _buildIconOption(Icons.settings, 'Settings'),
                    _buildIconOption(Icons.home, 'Home'),
                    _buildIconOption(Icons.email, 'Email'),
                    _buildIconOption(Icons.phone, 'Phone'),
                    _buildIconOption(Icons.location_on, 'Location'),
                    _buildIconOption(Icons.calendar_today, 'Calendar'),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildIconOption(IconData icon, String name) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedIcon = name;
        });
        Navigator.pop(context);
      },
      child: Container(
        margin: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 24, color: Colors.grey.shade700),
            const SizedBox(height: 4),
            Text(
              name,
              style: TextStyle(
                fontSize: 10,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildObjectHeader() {
    return Row(
      children: [
        Text(
          'Object: ',
          style: TextStyle(
            fontSize: ResponsiveFontSizes.bodyLarge(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        Text(
          'Customer',
          style: TextStyle(
            fontSize: ResponsiveFontSizes.bodyLarge(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: const Color(0xFF007AFF),
          ),
        ),
      ],
    );
  }

  List<Widget> _buildExpansionPanelsWithLineNumbers(int startingLineNumber) {
    List<Widget> widgets = [];
    int lineNumber = startingLineNumber;

    // Attributes Details Section with individual line numbers
    var attributesResult = _buildAttributesSection(lineNumber);
    widgets.addAll(attributesResult['widgets']);
    lineNumber = attributesResult['nextLineNumber'];

    widgets.add(const SizedBox(height: 16));

    // Entity Relationship Section
    widgets.add(_buildLineWithNumber(
        lineNumber++, _buildPlainSectionHeader('Entity Relationship')));
    widgets.add(_buildLineWithNumber(
        lineNumber++, _buildSectionSubtitleOnly('0 Rules Configured')));
    widgets.add(const SizedBox(height: 16));

    // Enumerated Values Section
    widgets.add(_buildLineWithNumber(
        lineNumber++, _buildPlainSectionHeader('Enumerated Values')));
    widgets.add(_buildLineWithNumber(
        lineNumber++, _buildSectionSubtitleOnly('0 Rules Configured')));

    return widgets;
  }

  Map<String, dynamic> _buildAttributesSection(int startingLineNumber) {
    List<Widget> widgets = [];
    int lineNumber = startingLineNumber;

    // Line 2: Attributes Details header
    widgets.add(
        _buildLineWithNumber(lineNumber++, _buildAttributesSectionHeader()));

    // Line 3: Attributes count
    widgets.add(_buildLineWithNumber(lineNumber++, _buildAttributesSubtitle()));

    // Line 4: Unified table (header + data together)
    widgets.add(
        _buildLineWithNumber(lineNumber++, _buildUnifiedAttributesTable()));

    return {
      'widgets': widgets,
      'nextLineNumber': lineNumber,
    };
  }

  Widget _buildAttributesSectionHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFF2196F3), width: 1), // Top border
          left: BorderSide(color: Color(0xFF2196F3), width: 1), // Left border
          right: BorderSide(color: Color(0xFF2196F3), width: 1), // Right border
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
        color: Colors.white,
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              'Attributes Details',
              style: TextStyle(
                fontSize: ResponsiveFontSizes.titleLarge(context),
                fontWeight: FontWeight.w600,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              _showAddAttributeModal();
            },
            child: Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade400, width: 1),
              ),
              child: Icon(
                Icons.add,
                size: 16,
                color: Colors.grey.shade700,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttributesSubtitle() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        border: Border(
          left: BorderSide(color: const Color(0xFF007AFF), width: 1),
          right: BorderSide(color: const Color(0xFF007AFF), width: 1),
        ),
        color: Colors.white,
      ),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(
          '${_attributes.length} Attributes',
          style: TextStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: const Color(0xFF333333)),
        ),
      ),
    );
  }

  // Helper method to build header cells with consistent styling
  Widget _buildHeaderCell(String text, double width) {
    return Container(
      width: width,
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      alignment: Alignment.centerLeft,
      child: Text(
        text,
        style: TextStyle(
          fontSize: ResponsiveFontSizes.bodyMedium(context),
          fontWeight: FontWeight.w500,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.black,
        ),
      ),
    );
  }

  // Helper method to build data cells with consistent styling
  Widget _buildDataCell(String text, double width) {
    return Container(
      width: width,
      // padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      alignment: Alignment.centerLeft,
      child: Text(
        text,
        style: TextStyle(
          fontSize: ResponsiveFontSizes.bodySmall(context),
          fontWeight: FontWeight.w400,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.black,
        ),
      ),
    );
  }

  // UNIFIED TABLE - This will ensure perfect synchronization
  Widget _buildUnifiedAttributesTable() {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          left: BorderSide(color: const Color(0xFF007AFF), width: 1),
          right: BorderSide(color: const Color(0xFF007AFF), width: 1),
          bottom: BorderSide(color: const Color(0xFF007AFF), width: 1),
        ),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(8),
        ),
        color: Colors.white,
      ),
      child: Row(
        children: [
          // Scrollable area containing both header and data - this ensures perfect sync
          Expanded(
            child: SingleChildScrollView(
              controller: _tableScrollController,
              scrollDirection: Axis.horizontal,
              child: Column(
                children: [
                  // Table Header
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      border: Border(
                        bottom:
                            BorderSide(color: Colors.grey.shade300, width: .5),
                      ),
                    ),
                    // padding:
                    //     // const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    child: Row(
                      children: [
                        _buildHeaderCell('Name', 100),
                        _buildHeaderCell('Display Name', 120),
                        _buildHeaderCell('Data Type', 100),
                        _buildHeaderCell('Required', 80),
                        _buildHeaderCell('Unique', 80),
                        _buildHeaderCell('Key', 80),
                      ],
                    ),
                  ),
                  // Data Rows
                  if (_attributes.isEmpty)
                    // Empty row
                    Container(
                      // padding: const EdgeInsets.symmetric(
                      //     horizontal: 0, vertical: 8),
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                              color: Colors.grey.shade300, width: .5),
                        ),
                      ),
                      child: Row(
                        children: [
                          _buildDataCell('', 100),
                          _buildDataCell('', 120),
                          _buildDataCell('', 100),
                          _buildDataCell('', 80),
                          _buildDataCell('', 80),
                          _buildDataCell('', 80),
                        ],
                      ),
                    )
                  else
                    ..._attributes.asMap().entries.map((entry) {
                      int index = entry.key;
                      AttributeModel attribute = entry.value;
                      bool isLastRow = index == _attributes.length;

                      return Container(
                        // padding: const EdgeInsets.symmetric(
                        //     horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: isLastRow
                                ? BorderSide.none
                                : BorderSide(
                                    color: Colors.grey.shade300, width: .5),
                          ),
                        ),
                        child: Row(
                          children: [
                            _buildDataCell(attribute.name, 100),
                            _buildDataCell(attribute.displayName, 120),
                            _buildDataCell(attribute.dataType, 100),
                            _buildDataCell(
                                attribute.isRequired ? 'Yes' : 'No', 80),
                            _buildDataCell(
                                attribute.isUnique ? 'Yes' : 'No', 80),
                            _buildDataCell('Default', 80),
                          ],
                        ),
                      );
                    }).toList(),
                ],
              ),
            ),
          ),
          // Fixed Action column that stays in place
          Container(
            width: 80, // fixed width for alignment
            // padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            decoration: BoxDecoration(
              border: Border(
                left: BorderSide(color: Colors.grey.shade300, width: 0.5),
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Action Header
                Container(
                  //height: 38, // match header row height
                  // padding:
                  //     const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 9.8),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    border: Border(
                      bottom:
                          BorderSide(color: Colors.grey.shade300, width: .5),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    'Action',
                    style: TextStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                ),
                // Action buttons for each row
                Container(
                  constraints: const BoxConstraints(maxHeight: 200),
                  // padding:
                  //     const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        if (_attributes.isEmpty)
                          // Empty action cell
                          Container(
                            // height: 40,
                            // padding: const EdgeInsets.symmetric(
                            //     vertical: 12, horizontal: 10),
                            //alignment: Alignment.center,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 10),
                            child: Icon(
                              Icons.delete_outline,
                              size: 15,
                              color: const Color(0xFFDC2626),
                            ),
                          )
                        else
                          ..._attributes.asMap().entries.map((entry) {
                            int index = entry.key;
                            return Container(
                              // padding: const EdgeInsets.symmetric(
                              //     vertical: 12, horizontal: 10),
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 10, vertical: 10),
                              decoration: BoxDecoration(
                                border: Border(
                                  bottom: index == _attributes.length - .5
                                      ? BorderSide.none
                                      : BorderSide(
                                          color: Colors.grey.shade300,
                                          width: .5),
                                ),
                              ),
                              alignment: Alignment.center,
                              child: IconButton(
                                onPressed: () {
                                  _removeAttribute(index);
                                },
                                icon: Icon(
                                  Icons.delete_outline,
                                  size: 14,
                                  color: Colors.red.shade600,
                                ),
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                              ),
                            );
                          }).toList(),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlainSectionHeader(String title) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300, width: .5),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
        color: Colors.white,
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                fontSize: ResponsiveFontSizes.titleLarge(context),
                fontWeight: FontWeight.w600,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade400, width: 1),
            ),
            child: Icon(
              Icons.add,
              size: 16,
              color: Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionSubtitleOnly(String subtitle) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        border: Border(
          left: BorderSide(color: Colors.grey.shade300, width: 1),
          right: BorderSide(color: Colors.grey.shade300, width: 1),
          bottom: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(8),
          bottomRight: Radius.circular(8),
        ),
        color: Colors.white,
      ),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(
          subtitle,
          style: TextStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: const Color(0xFF333333)),
        ),
      ),
    );
  }

  void _handleValidation(ObjectCreationProvider provider) {
    if (_objectController.text.trim().isEmpty) {
      _showErrorSnackBar('Please enter object name');
      return;
    }

    if (_selectedType == null) {
      _showErrorSnackBar('Please select a type');
      return;
    }

    if (_descriptionController.text.trim().isEmpty) {
      _showErrorSnackBar('Please enter description');
      return;
    }

    // Show expansion panels after successful validation
    setState(() {
      _showExpansionPanels = true;
    });

    _showSuccessSnackBar('Object details validated successfully!');
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showAddAttributeModal() {
    final TextEditingController nameController =
        TextEditingController(text: 'Customer');
    final TextEditingController displayNameController =
        TextEditingController(text: 'Customer ID');
    final TextEditingController defaultController =
        TextEditingController(text: 'primary');
    String selectedDataType = 'String';
    String selectedRequired = 'No';
    String selectedUnique = 'key';

    final List<String> dataTypes = [
      'String',
      'Integer',
      'Boolean',
      'Date',
      'Double'
    ];

    final List<String> requiredOptions = ['Yes', 'No'];
    final List<String> uniqueOptions = ['key', 'primary', 'default'];

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            return Container(
              height: MediaQuery.of(context).size.height * 0.75,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Column(
                children: [
                  // Header
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 16),
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(24),
                        topRight: Radius.circular(24),
                      ),
                    ),
                    child: Row(
                      children: [
                        Text(
                          'Validation Rules',
                          style: TextStyle(
                              fontSize: ResponsiveFontSizes.bodyLarge(context),
                              fontWeight: FontWeight.bold,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black),
                        ),
                        const Spacer(),
                        GestureDetector(
                          onTap: () => Navigator.pop(context),
                          child: Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              color: Colors.grey.shade100,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                  color: Colors.grey.shade300, width: 1),
                            ),
                            child: Icon(
                              Icons.close,
                              color: Colors.grey.shade600,
                              size: 16,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Form Content
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Name field
                          _buildBottomSheetField(
                            label: 'Name',
                            controller: nameController,
                            hintText: 'Customer',
                          ),
                          const SizedBox(height: 16),

                          // Display Name field
                          _buildBottomSheetField(
                            label: 'Display Name',
                            controller: displayNameController,
                            hintText: 'Customer ID',
                          ),
                          const SizedBox(height: 16),

                          // Data Type and Required row
                          Row(
                            children: [
                              // Data Type dropdown
                              Expanded(
                                child: _buildBottomSheetDropdown(
                                  label: 'Data Type',
                                  value: selectedDataType,
                                  items: dataTypes,
                                  onChanged: (String? newValue) {
                                    setModalState(() {
                                      selectedDataType = newValue!;
                                    });
                                  },
                                ),
                              ),
                              const SizedBox(width: 16),
                              // Required dropdown
                              Expanded(
                                child: _buildBottomSheetDropdown(
                                  label: 'Required',
                                  value: selectedRequired,
                                  items: requiredOptions,
                                  onChanged: (String? newValue) {
                                    setModalState(() {
                                      selectedRequired = newValue!;
                                    });
                                  },
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),

                          // Unique and Default row
                          Row(
                            children: [
                              // Unique dropdown
                              Expanded(
                                child: _buildBottomSheetDropdown(
                                  label: 'Unique',
                                  value: selectedUnique,
                                  items: uniqueOptions,
                                  onChanged: (String? newValue) {
                                    setModalState(() {
                                      selectedUnique = newValue!;
                                    });
                                  },
                                ),
                              ),
                              const SizedBox(width: 16),
                              // Default field
                              Expanded(
                                child: _buildBottomSheetField(
                                  label: 'Default',
                                  controller: defaultController,
                                  hintText: 'primary',
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Buttons
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      border: Border(
                        top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Container(
                            height: 40,
                            child: OutlinedButton(
                              onPressed: () => Navigator.pop(context),
                              style: OutlinedButton.styleFrom(
                                backgroundColor: Colors.white,
                                foregroundColor: Colors.black,
                                side: BorderSide(
                                    color: Colors.grey.shade300, width: 1),
                                elevation: 0,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: Text(
                                'Cancel',
                                style: TextStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleLarge(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Color(0xFF242424)),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Container(
                            height: 40,
                            child: ElevatedButton(
                              onPressed: () {
                                if (nameController.text.trim().isNotEmpty &&
                                    displayNameController.text
                                        .trim()
                                        .isNotEmpty) {
                                  _addAttribute(
                                    nameController.text.trim(),
                                    displayNameController.text.trim(),
                                    selectedDataType,
                                    selectedRequired == 'Yes',
                                    selectedUnique == 'primary',
                                  );
                                  Navigator.pop(context);
                                } else {
                                  _showErrorSnackBar(
                                      'Please fill all required fields');
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF0057FF),
                                foregroundColor: Colors.white,
                                elevation: 0,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 36),
                              ),
                              child: Text(
                                'Apply This',
                                style: TextStyle(
                                  fontSize:
                                      ResponsiveFontSizes.titleLarge(context),
                                  fontWeight: FontWeight.w400,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildBottomSheetField({
    required String label,
    required TextEditingController controller,
    required String hintText,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Color(0xFF242424)),
        ),
        const SizedBox(height: 6),
        Container(
          height: 42,
          decoration: BoxDecoration(
            border: Border.all(color: Color(0xFFCECECE), width: .5),
            borderRadius: BorderRadius.circular(6),
            color: Colors.white,
          ),
          child: TextField(
            controller: controller,
            decoration: InputDecoration(
              border: InputBorder.none,
              enabledBorder: InputBorder.none,
              focusedBorder: InputBorder.none,
              fillColor: Colors.transparent,
              hoverColor: Colors.transparent,
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
              hintText: hintText,
              hintStyle: TextStyle(
                  fontSize: ResponsiveFontSizes.titleLarge(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black),
              isDense: true,
            ),
            style: TextStyle(
                fontSize: ResponsiveFontSizes.titleLarge(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomSheetDropdown({
    required String label,
    required String value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Color(0xFF242424)),
        ),
        const SizedBox(height: 6),
        Container(
          height: 42,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            border: Border.all(color: Color(0xFFCECECE), width: .5),
            borderRadius: BorderRadius.circular(6),
            color: Colors.white,
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: value,
              icon: Icon(Icons.keyboard_arrow_down,
                  color: Colors.grey.shade600, size: 20),
              isExpanded: true,
              items: items.map((String item) {
                return DropdownMenuItem<String>(
                  value: item,
                  child: Text(
                    item,
                    style: TextStyle(
                        fontSize: ResponsiveFontSizes.titleLarge(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black),
                  ),
                );
              }).toList(),
              onChanged: onChanged,
            ),
          ),
        ),
      ],
    );
  }

  void _addAttribute(String name, String displayName, String dataType,
      bool isRequired, bool isUnique) {
    setState(() {
      _attributes.add(AttributeModel(
        name: name,
        displayName: displayName,
        dataType: dataType,
        isRequired: isRequired,
        isUnique: isUnique,
      ));
    });
    _showSuccessSnackBar('Attribute added successfully!');
  }

  void _removeAttribute(int index) {
    setState(() {
      _attributes.removeAt(index);
    });
    _showSuccessSnackBar('Attribute removed successfully!');
  }
}
