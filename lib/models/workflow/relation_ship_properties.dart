import 'dart:convert';

class RelationshipProperties {
  final String? sourceEntity;
  final String? targetEntity;
  final String? onDelete;
  final String? onUpdate;
  final String? foreignKeyType;

  RelationshipProperties({
    this.sourceEntity,
    this.targetEntity,
    this.onDelete,
    this.onUpdate,
    this.foreignKeyType,
  });

  factory RelationshipProperties.fromJson(String str) => RelationshipProperties.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory RelationshipProperties.fromMap(Map<String, dynamic> json) => RelationshipProperties(
    sourceEntity: json["source_entity"],
    targetEntity: json["target_entity"],
    onDelete: json["on_delete"],
    onUpdate: json["on_update"],
    foreignKeyType: json["foreign_key_type"],
  );

  Map<String, dynamic> toMap() => {
    "source_entity": sourceEntity,
    "target_entity": targetEntity,
    "on_delete": onDelete,
    "on_update": onUpdate,
    "foreign_key_type": foreignKeyType,
  };
}
