// To parse this JSON data, do
//
//     final validateInheritanceModel = validateInheritanceModelFromJson(jsonString);

import 'dart:convert';

ValidateInheritanceModel validateInheritanceModelFromJson(String str) => ValidateInheritanceModel.fromJson(json.decode(str));

String validateInheritanceModelToJson(ValidateInheritanceModel data) => json.encode(data.toJson());

class ValidateInheritanceModel {
    List<InheritanceResult>? inheritanceResults;
    Issues? issues;
    bool? hasErrors;
    bool? hasWarnings;
    IssueCounts? issueCounts;

    ValidateInheritanceModel({
        this.inheritanceResults,
        this.issues,
        this.hasErrors,
        this.hasWarnings,
        this.issueCounts,
    });

    factory ValidateInheritanceModel.fromJson(Map<String, dynamic> json) => ValidateInheritanceModel(
        inheritanceResults: json["inheritance_results"] == null ? [] : List<InheritanceResult>.from(json["inheritance_results"]!.map((x) => InheritanceResult.fromJson(x))),
        issues: json["issues"] == null ? null : Issues.fromJson(json["issues"]),
        hasErrors: json["has_errors"],
        hasWarnings: json["has_warnings"],
        issueCounts: json["issue_counts"] == null ? null : IssueCounts.fromJson(json["issue_counts"]),
    );

    Map<String, dynamic> toJson() => {
        "inheritance_results": inheritanceResults == null ? [] : List<dynamic>.from(inheritanceResults!.map((x) => x.toJson())),
        "issues": issues?.toJson(),
        "has_errors": hasErrors,
        "has_warnings": hasWarnings,
        "issue_counts": issueCounts?.toJson(),
    };
}

class InheritanceResult {
    ParsedData? parsedData;
    ValidationResult? validationResult;
    UniquenessResult? uniquenessResult;
    bool? isValid;

    InheritanceResult({
        this.parsedData,
        this.validationResult,
        this.uniquenessResult,
        this.isValid,
    });

    factory InheritanceResult.fromJson(Map<String, dynamic> json) => InheritanceResult(
        parsedData: json["parsed_data"] == null ? null : ParsedData.fromJson(json["parsed_data"]),
        validationResult: json["validation_result"] == null ? null : ValidationResult.fromJson(json["validation_result"]),
        uniquenessResult: json["uniqueness_result"] == null ? null : UniquenessResult.fromJson(json["uniqueness_result"]),
        isValid: json["is_valid"],
    );

    Map<String, dynamic> toJson() => {
        "parsed_data": parsedData?.toJson(),
        "validation_result": validationResult?.toJson(),
        "uniqueness_result": uniquenessResult?.toJson(),
        "is_valid": isValid,
    };
}

class ParsedData {
    int? id;
    dynamic parentRoleId;
    dynamic inheritsRoleId;
    String? naturalLanguage;
    int? version;
    String? status;
    String? parentRoleName;
    String? inheritsRoleName;

    ParsedData({
        this.id,
        this.parentRoleId,
        this.inheritsRoleId,
        this.naturalLanguage,
        this.version,
        this.status,
        this.parentRoleName,
        this.inheritsRoleName,
    });

    factory ParsedData.fromJson(Map<String, dynamic> json) => ParsedData(
        id: json["id"],
        parentRoleId: json["parent_role_id"],
        inheritsRoleId: json["inherits_role_id"],
        naturalLanguage: json["natural_language"],
        version: json["version"],
        status: json["status"],
        parentRoleName: json["parent_role_name"],
        inheritsRoleName: json["inherits_role_name"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "parent_role_id": parentRoleId,
        "inherits_role_id": inheritsRoleId,
        "natural_language": naturalLanguage,
        "version": version,
        "status": status,
        "parent_role_name": parentRoleName,
        "inherits_role_name": inheritsRoleName,
    };
}

class UniquenessResult {
    bool? isUnique;
    String? error;

    UniquenessResult({
        this.isUnique,
        this.error,
    });

    factory UniquenessResult.fromJson(Map<String, dynamic> json) => UniquenessResult(
        isUnique: json["is_unique"],
        error: json["error"],
    );

    Map<String, dynamic> toJson() => {
        "is_unique": isUnique,
        "error": error,
    };
}

class ValidationResult {
    List<String>? structureErrors;
    List<String>? requiredFieldErrors;
    List<dynamic>? dataTypeErrors;
    List<dynamic>? customErrors;
    List<String>? dependencyErrors;
    List<dynamic>? circularInheritanceErrors;

    ValidationResult({
        this.structureErrors,
        this.requiredFieldErrors,
        this.dataTypeErrors,
        this.customErrors,
        this.dependencyErrors,
        this.circularInheritanceErrors,
    });

    factory ValidationResult.fromJson(Map<String, dynamic> json) => ValidationResult(
        structureErrors: json["structure_errors"] == null ? [] : List<String>.from(json["structure_errors"]!.map((x) => x)),
        requiredFieldErrors: json["required_field_errors"] == null ? [] : List<String>.from(json["required_field_errors"]!.map((x) => x)),
        dataTypeErrors: json["data_type_errors"] == null ? [] : List<dynamic>.from(json["data_type_errors"]!.map((x) => x)),
        customErrors: json["custom_errors"] == null ? [] : List<dynamic>.from(json["custom_errors"]!.map((x) => x)),
        dependencyErrors: json["dependency_errors"] == null ? [] : List<String>.from(json["dependency_errors"]!.map((x) => x)),
        circularInheritanceErrors: json["circular_inheritance_errors"] == null ? [] : List<dynamic>.from(json["circular_inheritance_errors"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "structure_errors": structureErrors == null ? [] : List<dynamic>.from(structureErrors!.map((x) => x)),
        "required_field_errors": requiredFieldErrors == null ? [] : List<dynamic>.from(requiredFieldErrors!.map((x) => x)),
        "data_type_errors": dataTypeErrors == null ? [] : List<dynamic>.from(dataTypeErrors!.map((x) => x)),
        "custom_errors": customErrors == null ? [] : List<dynamic>.from(customErrors!.map((x) => x)),
        "dependency_errors": dependencyErrors == null ? [] : List<dynamic>.from(dependencyErrors!.map((x) => x)),
        "circular_inheritance_errors": circularInheritanceErrors == null ? [] : List<dynamic>.from(circularInheritanceErrors!.map((x) => x)),
    };
}

class IssueCounts {
    int? totalErrors;
    int? totalWarnings;
    int? totalExceptions;
    int? validationErrors;
    int? dependencyErrors;
    int? uniquenessIssues;
    int? parsingIssues;
    int? mongoErrors;
    int? postgresErrors;
    bool? hasCriticalErrors;
    bool? hasWarnings;

    IssueCounts({
        this.totalErrors,
        this.totalWarnings,
        this.totalExceptions,
        this.validationErrors,
        this.dependencyErrors,
        this.uniquenessIssues,
        this.parsingIssues,
        this.mongoErrors,
        this.postgresErrors,
        this.hasCriticalErrors,
        this.hasWarnings,
    });

    factory IssueCounts.fromJson(Map<String, dynamic> json) => IssueCounts(
        totalErrors: json["total_errors"],
        totalWarnings: json["total_warnings"],
        totalExceptions: json["total_exceptions"],
        validationErrors: json["validation_errors"],
        dependencyErrors: json["dependency_errors"],
        uniquenessIssues: json["uniqueness_issues"],
        parsingIssues: json["parsing_issues"],
        mongoErrors: json["mongo_errors"],
        postgresErrors: json["postgres_errors"],
        hasCriticalErrors: json["has_critical_errors"],
        hasWarnings: json["has_warnings"],
    );

    Map<String, dynamic> toJson() => {
        "total_errors": totalErrors,
        "total_warnings": totalWarnings,
        "total_exceptions": totalExceptions,
        "validation_errors": validationErrors,
        "dependency_errors": dependencyErrors,
        "uniqueness_issues": uniquenessIssues,
        "parsing_issues": parsingIssues,
        "mongo_errors": mongoErrors,
        "postgres_errors": postgresErrors,
        "has_critical_errors": hasCriticalErrors,
        "has_warnings": hasWarnings,
    };
}

class Issues {
    IssueCounts? summary;
    List<dynamic>? errors;
    List<dynamic>? warnings;
    List<dynamic>? exceptions;
    List<dynamic>? validationErrors;
    List<dynamic>? dependencyErrors;
    List<dynamic>? uniquenessIssues;
    List<dynamic>? parsingIssues;
    List<dynamic>? mongoErrors;
    List<dynamic>? postgresErrors;

    Issues({
        this.summary,
        this.errors,
        this.warnings,
        this.exceptions,
        this.validationErrors,
        this.dependencyErrors,
        this.uniquenessIssues,
        this.parsingIssues,
        this.mongoErrors,
        this.postgresErrors,
    });

    factory Issues.fromJson(Map<String, dynamic> json) => Issues(
        summary: json["summary"] == null ? null : IssueCounts.fromJson(json["summary"]),
        errors: json["errors"] == null ? [] : List<dynamic>.from(json["errors"]!.map((x) => x)),
        warnings: json["warnings"] == null ? [] : List<dynamic>.from(json["warnings"]!.map((x) => x)),
        exceptions: json["exceptions"] == null ? [] : List<dynamic>.from(json["exceptions"]!.map((x) => x)),
        validationErrors: json["validation_errors"] == null ? [] : List<dynamic>.from(json["validation_errors"]!.map((x) => x)),
        dependencyErrors: json["dependency_errors"] == null ? [] : List<dynamic>.from(json["dependency_errors"]!.map((x) => x)),
        uniquenessIssues: json["uniqueness_issues"] == null ? [] : List<dynamic>.from(json["uniqueness_issues"]!.map((x) => x)),
        parsingIssues: json["parsing_issues"] == null ? [] : List<dynamic>.from(json["parsing_issues"]!.map((x) => x)),
        mongoErrors: json["mongo_errors"] == null ? [] : List<dynamic>.from(json["mongo_errors"]!.map((x) => x)),
        postgresErrors: json["postgres_errors"] == null ? [] : List<dynamic>.from(json["postgres_errors"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "summary": summary?.toJson(),
        "errors": errors == null ? [] : List<dynamic>.from(errors!.map((x) => x)),
        "warnings": warnings == null ? [] : List<dynamic>.from(warnings!.map((x) => x)),
        "exceptions": exceptions == null ? [] : List<dynamic>.from(exceptions!.map((x) => x)),
        "validation_errors": validationErrors == null ? [] : List<dynamic>.from(validationErrors!.map((x) => x)),
        "dependency_errors": dependencyErrors == null ? [] : List<dynamic>.from(dependencyErrors!.map((x) => x)),
        "uniqueness_issues": uniquenessIssues == null ? [] : List<dynamic>.from(uniquenessIssues!.map((x) => x)),
        "parsing_issues": parsingIssues == null ? [] : List<dynamic>.from(parsingIssues!.map((x) => x)),
        "mongo_errors": mongoErrors == null ? [] : List<dynamic>.from(mongoErrors!.map((x) => x)),
        "postgres_errors": postgresErrors == null ? [] : List<dynamic>.from(postgresErrors!.map((x) => x)),
    };
}
