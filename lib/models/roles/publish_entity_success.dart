// To parse this JSON data, do
//
//     final publishEntitySuccessModel = publishEntitySuccessModelFromJson(jsonString);

import 'dart:convert';

PublishEntitySuccessModel publishEntitySuccessModelFromJson(String str) => PublishEntitySuccessModel.fromJson(json.decode(str));

String publishEntitySuccessModelToJson(PublishEntitySuccessModel data) => json.encode(data.toJson());

class PublishEntitySuccessModel {
    String? status;
    String? message;
    Result? result;
    String? operation;

    PublishEntitySuccessModel({
        this.status,
        this.message,
        this.result,
        this.operation,
    });

    factory PublishEntitySuccessModel.fromJson(Map<String, dynamic> json) => PublishEntitySuccessModel(
        status: json["status"],
        message: json["message"],
        result: json["result"] == null ? null : Result.fromJson(json["result"]),
        operation: json["operation"],
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "result": result?.toJson(),
        "operation": operation,
    };
}

class Result {
    RoleResult? roleResult;
    List<dynamic>? inheritanceResults;
    int? totalInheritanceProcessed;

    Result({
        this.roleResult,
        this.inheritanceResults,
        this.totalInheritanceProcessed,
    });

    factory Result.fromJson(Map<String, dynamic> json) => Result(
        roleResult: json["role_result"] == null ? null : RoleResult.fromJson(json["role_result"]),
        inheritanceResults: json["inheritance_results"] == null ? [] : List<dynamic>.from(json["inheritance_results"]!.map((x) => x)),
        totalInheritanceProcessed: json["total_inheritance_processed"],
    );

    Map<String, dynamic> toJson() => {
        "role_result": roleResult?.toJson(),
        "inheritance_results": inheritanceResults == null ? [] : List<dynamic>.from(inheritanceResults!.map((x) => x)),
        "total_inheritance_processed": totalInheritanceProcessed,
    };
}

class RoleResult {
    bool? success;
    int? insertedId;
    String? schema;
    String? roleId;
    String? name;
    String? tenantId;

    RoleResult({
        this.success,
        this.insertedId,
        this.schema,
        this.roleId,
        this.name,
        this.tenantId,
    });

    factory RoleResult.fromJson(Map<String, dynamic> json) => RoleResult(
        success: json["success"],
        insertedId: json["inserted_id"],
        schema: json["schema"],
        roleId: json["role_id"],
        name: json["name"],
        tenantId: json["tenant_id"],
    );

    Map<String, dynamic> toJson() => {
        "success": success,
        "inserted_id": insertedId,
        "schema": schema,
        "role_id": roleId,
        "name": name,
        "tenant_id": tenantId,
    };
}
