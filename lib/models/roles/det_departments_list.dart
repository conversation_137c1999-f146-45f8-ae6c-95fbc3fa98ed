// To parse this JSON data, do
//
//     final getDepartmentsListModel = getDepartmentsListModelFromJson(jsonString);

import 'dart:convert';

GetDepartmentsListModel getDepartmentsListModelFromJson(String str) => GetDepartmentsListModel.fromJson(json.decode(str));

String getDepartmentsListModelToJson(GetDepartmentsListModel data) => json.encode(data.toJson());

class GetDepartmentsListModel {
    bool? success;
    String? message;
    Data? data;
    dynamic error;
    DateTime? timestamp;

    GetDepartmentsListModel({
        this.success,
        this.message,
        this.data,
        this.error,
        this.timestamp,
    });

    factory GetDepartmentsListModel.fromJson(Map<String, dynamic> json) => GetDepartmentsListModel(
        success: json["success"],
        message: json["message"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        error: json["error"],
        timestamp: json["timestamp"] == null ? null : DateTime.parse(json["timestamp"]),
    );

    Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "data": data?.toJson(),
        "error": error,
        "timestamp": timestamp?.toIso8601String(),
    };
}

class Data {
    String? tenantId;
    String? tableName;
    List<dynamic>? postgresData;
    List<MongoDatum>? mongoData;
    Summary? summary;
    DateTime? retrievedAt;

    Data({
        this.tenantId,
        this.tableName,
        this.postgresData,
        this.mongoData,
        this.summary,
        this.retrievedAt,
    });

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        tenantId: json["tenant_id"],
        tableName: json["table_name"],
        postgresData: json["postgres_data"] == null ? [] : List<dynamic>.from(json["postgres_data"]!.map((x) => x)),
        mongoData: json["mongo_data"] == null ? [] : List<MongoDatum>.from(json["mongo_data"]!.map((x) => MongoDatum.fromJson(x))),
        summary: json["summary"] == null ? null : Summary.fromJson(json["summary"]),
        retrievedAt: json["retrieved_at"] == null ? null : DateTime.parse(json["retrieved_at"]),
    );

    Map<String, dynamic> toJson() => {
        "tenant_id": tenantId,
        "table_name": tableName,
        "postgres_data": postgresData == null ? [] : List<dynamic>.from(postgresData!.map((x) => x)),
        "mongo_data": mongoData == null ? [] : List<dynamic>.from(mongoData!.map((x) => x.toJson())),
        "summary": summary?.toJson(),
        "retrieved_at": retrievedAt?.toIso8601String(),
    };
}

class MongoDatum {
    String? id;
    int? mongoDatumId;
    String? tenantId;
    String? name;
    String? description;
    dynamic departmentHeadRoleId;
    dynamic parentDepartmentId;
    String? naturalLanguage;
    DateTime? createdAt;
    dynamic createdBy;
    DateTime? updatedAt;
    dynamic updatedBy;
    int? version;
    String? status;

    MongoDatum({
        this.id,
        this.mongoDatumId,
        this.tenantId,
        this.name,
        this.description,
        this.departmentHeadRoleId,
        this.parentDepartmentId,
        this.naturalLanguage,
        this.createdAt,
        this.createdBy,
        this.updatedAt,
        this.updatedBy,
        this.version,
        this.status,
    });

    factory MongoDatum.fromJson(Map<String, dynamic> json) => MongoDatum(
        id: json["_id"],
        mongoDatumId: json["id"],
        tenantId: json["tenant_id"],
        name: json["name"],
        description: json["description"],
        departmentHeadRoleId: json["department_head_role_id"],
        parentDepartmentId: json["parent_department_id"],
        naturalLanguage: json["natural_language"],
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
        createdBy: json["created_by"],
        updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
        updatedBy: json["updated_by"],
        version: json["version"],
        status: json["status"],
    );

    Map<String, dynamic> toJson() => {
        "_id": id,
        "id": mongoDatumId,
        "tenant_id": tenantId,
        "name": name,
        "description": description,
        "department_head_role_id": departmentHeadRoleId,
        "parent_department_id": parentDepartmentId,
        "natural_language": naturalLanguage,
        "created_at": createdAt?.toIso8601String(),
        "created_by": createdBy,
        "updated_at": updatedAt?.toIso8601String(),
        "updated_by": updatedBy,
        "version": version,
        "status": status,
    };
}

class Summary {
    int? postgresCount;
    int? mongoCount;
    int? totalCount;

    Summary({
        this.postgresCount,
        this.mongoCount,
        this.totalCount,
    });

    factory Summary.fromJson(Map<String, dynamic> json) => Summary(
        postgresCount: json["postgres_count"],
        mongoCount: json["mongo_count"],
        totalCount: json["total_count"],
    );

    Map<String, dynamic> toJson() => {
        "postgres_count": postgresCount,
        "mongo_count": mongoCount,
        "total_count": totalCount,
    };
}
