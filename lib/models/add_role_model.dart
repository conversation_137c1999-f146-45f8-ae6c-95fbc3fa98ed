// To parse this JSON data, do
//
//     final validateAddRoleModel = validateAddRoleModelFromJson(jsonString);

import 'dart:convert';

ValidateAddRoleModel validateAddRoleModelFromJson(String str) => ValidateAddRoleModel.fromJson(json.decode(str));

String validateAddRoleModelToJson(ValidateAddRoleModel data) => json.encode(data.toJson());

class ValidateAddRoleModel {
    RoleParsedData? parsedData;
    RoleValidationResult? validationResult;
    List<String>? dependencyErrors;
    RoleUniquenessResult? uniquenessResult;
    bool? isValid;
    RoleIssues? issues;
    bool? hasErrors;
    bool? hasWarnings;
    RoleIssueCounts? issueCounts;
    bool? success;
    String? failureReason;

    ValidateAddRoleModel({
        this.parsedData,
        this.validationResult,
        this.dependencyErrors,
        this.uniquenessResult,
        this.isValid,
        this.issues,
        this.hasErrors,
        this.hasWarnings,
        this.issueCounts,
        this.success,
        this.failureReason,
    });

    factory ValidateAddRoleModel.fromJson(Map<String, dynamic> json) => ValidateAddRoleModel(
        parsedData: json["parsed_data"] == null ? null : RoleParsedData.fromJson(json["parsed_data"]),
        validationResult: json["validation_result"] == null ? null : RoleValidationResult.fromJson(json["validation_result"]),
        dependencyErrors: json["dependency_errors"] == null ? [] : List<String>.from(json["dependency_errors"]!.map((x) => x)),
        uniquenessResult: json["uniqueness_result"] == null ? null : RoleUniquenessResult.fromJson(json["uniqueness_result"]),
        isValid: json["is_valid"],
        issues: json["issues"] == null ? null : RoleIssues.fromJson(json["issues"]),
        hasErrors: json["has_errors"],
        hasWarnings: json["has_warnings"],
        issueCounts: json["issue_counts"] == null ? null : RoleIssueCounts.fromJson(json["issue_counts"]),
        success: json["success"],
        failureReason: json["failure_reason"],
    );

    Map<String, dynamic> toJson() => {
        "parsed_data": parsedData?.toJson(),
        "validation_result": validationResult?.toJson(),
        "dependency_errors": dependencyErrors == null ? [] : List<dynamic>.from(dependencyErrors!.map((x) => x)),
        "uniqueness_result": uniquenessResult?.toJson(),
        "is_valid": isValid,
        "issues": issues?.toJson(),
        "has_errors": hasErrors,
        "has_warnings": hasWarnings,
        "issue_counts": issueCounts?.toJson(),
        "success": success,
        "failure_reason": failureReason,
    };
}

class RoleIssueCounts {
    int? totalErrors;
    int? totalWarnings;
    int? totalExceptions;
    int? validationErrors;
    int? dependencyErrors;
    int? uniquenessIssues;
    int? parsingIssues;
    int? mongoErrors;
    int? postgresErrors;
    bool? hasCriticalErrors;
    bool? hasWarnings;

    RoleIssueCounts({
        this.totalErrors,
        this.totalWarnings,
        this.totalExceptions,
        this.validationErrors,
        this.dependencyErrors,
        this.uniquenessIssues,
        this.parsingIssues,
        this.mongoErrors,
        this.postgresErrors,
        this.hasCriticalErrors,
        this.hasWarnings,
    });

    factory RoleIssueCounts.fromJson(Map<String, dynamic> json) => RoleIssueCounts(
        totalErrors: json["total_errors"],
        totalWarnings: json["total_warnings"],
        totalExceptions: json["total_exceptions"],
        validationErrors: json["validation_errors"],
        dependencyErrors: json["dependency_errors"],
        uniquenessIssues: json["uniqueness_issues"],
        parsingIssues: json["parsing_issues"],
        mongoErrors: json["mongo_errors"],
        postgresErrors: json["postgres_errors"],
        hasCriticalErrors: json["has_critical_errors"],
        hasWarnings: json["has_warnings"],
    );

    Map<String, dynamic> toJson() => {
        "total_errors": totalErrors,
        "total_warnings": totalWarnings,
        "total_exceptions": totalExceptions,
        "validation_errors": validationErrors,
        "dependency_errors": dependencyErrors,
        "uniqueness_issues": uniquenessIssues,
        "parsing_issues": parsingIssues,
        "mongo_errors": mongoErrors,
        "postgres_errors": postgresErrors,
        "has_critical_errors": hasCriticalErrors,
        "has_warnings": hasWarnings,
    };
}

class RoleIssues {
    RoleIssueCounts? summary;
    List<dynamic>? errors;
    List<dynamic>? warnings;
    List<dynamic>? exceptions;
    List<dynamic>? validationErrors;
    List<RoleDependencyError>? dependencyErrors;
    List<dynamic>? uniquenessIssues;
    List<dynamic>? parsingIssues;
    List<dynamic>? mongoErrors;
    List<dynamic>? postgresErrors;

    RoleIssues({
        this.summary,
        this.errors,
        this.warnings,
        this.exceptions,
        this.validationErrors,
        this.dependencyErrors,
        this.uniquenessIssues,
        this.parsingIssues,
        this.mongoErrors,
        this.postgresErrors,
    });

    factory RoleIssues.fromJson(Map<String, dynamic> json) => RoleIssues(
        summary: json["summary"] == null ? null : RoleIssueCounts.fromJson(json["summary"]),
        errors: json["errors"] == null ? [] : List<dynamic>.from(json["errors"]!.map((x) => x)),
        warnings: json["warnings"] == null ? [] : List<dynamic>.from(json["warnings"]!.map((x) => x)),
        exceptions: json["exceptions"] == null ? [] : List<dynamic>.from(json["exceptions"]!.map((x) => x)),
        validationErrors: json["validation_errors"] == null ? [] : List<dynamic>.from(json["validation_errors"]!.map((x) => x)),
        dependencyErrors: json["dependency_errors"] == null ? [] : List<RoleDependencyError>.from(json["dependency_errors"]!.map((x) => RoleDependencyError.fromJson(x))),
        uniquenessIssues: json["uniqueness_issues"] == null ? [] : List<dynamic>.from(json["uniqueness_issues"]!.map((x) => x)),
        parsingIssues: json["parsing_issues"] == null ? [] : List<dynamic>.from(json["parsing_issues"]!.map((x) => x)),
        mongoErrors: json["mongo_errors"] == null ? [] : List<dynamic>.from(json["mongo_errors"]!.map((x) => x)),
        postgresErrors: json["postgres_errors"] == null ? [] : List<dynamic>.from(json["postgres_errors"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "summary": summary?.toJson(),
        "errors": errors == null ? [] : List<dynamic>.from(errors!.map((x) => x)),
        "warnings": warnings == null ? [] : List<dynamic>.from(warnings!.map((x) => x)),
        "exceptions": exceptions == null ? [] : List<dynamic>.from(exceptions!.map((x) => x)),
        "validation_errors": validationErrors == null ? [] : List<dynamic>.from(validationErrors!.map((x) => x)),
        "dependency_errors": dependencyErrors == null ? [] : List<dynamic>.from(dependencyErrors!.map((x) => x.toJson())),
        "uniqueness_issues": uniquenessIssues == null ? [] : List<dynamic>.from(uniquenessIssues!.map((x) => x)),
        "parsing_issues": parsingIssues == null ? [] : List<dynamic>.from(parsingIssues!.map((x) => x)),
        "mongo_errors": mongoErrors == null ? [] : List<dynamic>.from(mongoErrors!.map((x) => x)),
        "postgres_errors": postgresErrors == null ? [] : List<dynamic>.from(postgresErrors!.map((x) => x)),
    };
}

class RoleDependencyError {
    String? message;
    String? source;
    DateTime? timestamp;

    RoleDependencyError({
        this.message,
        this.source,
        this.timestamp,
    });

    factory RoleDependencyError.fromJson(Map<String, dynamic> json) => RoleDependencyError(
        message: json["message"],
        source: json["source"],
        timestamp: json["timestamp"] == null ? null : DateTime.parse(json["timestamp"]),
    );

    Map<String, dynamic> toJson() => {
        "message": message,
        "source": source,
        "timestamp": timestamp?.toIso8601String(),
    };
}

class RoleParsedData {
    int? id;
    String? roleId;
    String? name;
    String? description;
    String? tenantId;
    dynamic reportsToRoleId;
    String? organizationalLevel;
    dynamic departmentId;
    String? naturalLanguage;
    DateTime? createdAt;
    dynamic createdBy;
    DateTime? updatedAt;
    dynamic updatedBy;
    int? version;
    String? status;
    String? departmentName;
    List<String>? inheritsRoles;

    RoleParsedData({
        this.id,
        this.roleId,
        this.name,
        this.description,
        this.tenantId,
        this.reportsToRoleId,
        this.organizationalLevel,
        this.departmentId,
        this.naturalLanguage,
        this.createdAt,
        this.createdBy,
        this.updatedAt,
        this.updatedBy,
        this.version,
        this.status,
        this.departmentName,
        this.inheritsRoles,
    });

    factory RoleParsedData.fromJson(Map<String, dynamic> json) => RoleParsedData(
        id: json["id"],
        roleId: json["role_id"],
        name: json["name"],
        description: json["description"],
        tenantId: json["tenant_id"],
        reportsToRoleId: json["reports_to_role_id"],
        organizationalLevel: json["organizational_level"],
        departmentId: json["department_id"],
        naturalLanguage: json["natural_language"],
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
        createdBy: json["created_by"],
        updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
        updatedBy: json["updated_by"],
        version: json["version"],
        status: json["status"],
        departmentName: json["department_name"],
        inheritsRoles: json["inherits_roles"] == null ? [] : List<String>.from(json["inherits_roles"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "role_id": roleId,
        "name": name,
        "description": description,
        "tenant_id": tenantId,
        "reports_to_role_id": reportsToRoleId,
        "organizational_level": organizationalLevel,
        "department_id": departmentId,
        "natural_language": naturalLanguage,
        "created_at": createdAt?.toIso8601String(),
        "created_by": createdBy,
        "updated_at": updatedAt?.toIso8601String(),
        "updated_by": updatedBy,
        "version": version,
        "status": status,
        "department_name": departmentName,
        "inherits_roles": inheritsRoles == null ? [] : List<dynamic>.from(inheritsRoles!.map((x) => x)),
    };
}

class RoleUniquenessResult {
    bool? isUnique;
    String? status;
    String? message;

    RoleUniquenessResult({
        this.isUnique,
        this.status,
        this.message,
    });

    factory RoleUniquenessResult.fromJson(Map<String, dynamic> json) => RoleUniquenessResult(
        isUnique: json["is_unique"],
        status: json["status"],
        message: json["message"],
    );

    Map<String, dynamic> toJson() => {
        "is_unique": isUnique,
        "status": status,
        "message": message,
    };
}

class RoleValidationResult {
    List<dynamic>? structureErrors;
    List<dynamic>? requiredFieldErrors;
    List<dynamic>? dataTypeErrors;
    List<dynamic>? customErrors;

    RoleValidationResult({
        this.structureErrors,
        this.requiredFieldErrors,
        this.dataTypeErrors,
        this.customErrors,
    });

    factory RoleValidationResult.fromJson(Map<String, dynamic> json) => RoleValidationResult(
        structureErrors: json["structure_errors"] == null ? [] : List<dynamic>.from(json["structure_errors"]!.map((x) => x)),
        requiredFieldErrors: json["required_field_errors"] == null ? [] : List<dynamic>.from(json["required_field_errors"]!.map((x) => x)),
        dataTypeErrors: json["data_type_errors"] == null ? [] : List<dynamic>.from(json["data_type_errors"]!.map((x) => x)),
        customErrors: json["custom_errors"] == null ? [] : List<dynamic>.from(json["custom_errors"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "structure_errors": structureErrors == null ? [] : List<dynamic>.from(structureErrors!.map((x) => x)),
        "required_field_errors": requiredFieldErrors == null ? [] : List<dynamic>.from(requiredFieldErrors!.map((x) => x)),
        "data_type_errors": dataTypeErrors == null ? [] : List<dynamic>.from(dataTypeErrors!.map((x) => x)),
        "custom_errors": customErrors == null ? [] : List<dynamic>.from(customErrors!.map((x) => x)),
    };
}
