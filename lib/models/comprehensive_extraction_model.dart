// To parse this JSON data, do
//
//     final comprehensiveExtractionModel = comprehensiveExtractionModelFromJson(jsonString);

import 'dart:convert';

ComprehensiveExtractionModel comprehensiveExtractionModelFromJson(String str) =>
    ComprehensiveExtractionModel.fromJson(json.decode(str));

String comprehensiveExtractionModelToJson(ComprehensiveExtractionModel data) =>
    json.encode(data.toJson());

class ComprehensiveExtractionModel {
  String? message;
  String? sessionId;
  ExtractionSummary? extractionSummary;
  ExtractedData? extractedData;
  List<StoredEntity>? storedEntities;
  String? businessDomain;
  String? tenantName;
  DateTime? extractionTimestamp;
  String? storageOptimization;
  AccessEndpoints? accessEndpoints;

  ComprehensiveExtractionModel({
    this.message,
    this.sessionId,
    this.extractionSummary,
    this.extractedData,
    this.storedEntities,
    this.businessDomain,
    this.tenantName,
    this.extractionTimestamp,
    this.storageOptimization,
    this.accessEndpoints,
  });

  ComprehensiveExtractionModel copyWith({
    String? message,
    String? sessionId,
    ExtractionSummary? extractionSummary,
    ExtractedData? extractedData,
    List<StoredEntity>? storedEntities,
    String? businessDomain,
    String? tenantName,
    DateTime? extractionTimestamp,
    String? storageOptimization,
    AccessEndpoints? accessEndpoints,
  }) =>
      ComprehensiveExtractionModel(
        message: message ?? this.message,
        sessionId: sessionId ?? this.sessionId,
        extractionSummary: extractionSummary ?? this.extractionSummary,
        extractedData: extractedData ?? this.extractedData,
        storedEntities: storedEntities ?? this.storedEntities,
        businessDomain: businessDomain ?? this.businessDomain,
        tenantName: tenantName ?? this.tenantName,
        extractionTimestamp: extractionTimestamp ?? this.extractionTimestamp,
        storageOptimization: storageOptimization ?? this.storageOptimization,
        accessEndpoints: accessEndpoints ?? this.accessEndpoints,
      );

  factory ComprehensiveExtractionModel.fromJson(Map<String, dynamic> json) =>
      ComprehensiveExtractionModel(
        message: json["message"],
        sessionId: json["session_id"],
        extractionSummary: json["extraction_summary"] == null
            ? null
            : ExtractionSummary.fromJson(json["extraction_summary"]),
        extractedData: json["extracted_data"] == null
            ? null
            : ExtractedData.fromJson(json["extracted_data"]),
        storedEntities: json["stored_entities"] == null
            ? []
            : List<StoredEntity>.from(
                json["stored_entities"]!.map((x) => StoredEntity.fromJson(x))),
        businessDomain: json["business_domain"],
        tenantName: json["tenant_name"],
        extractionTimestamp: json["extraction_timestamp"] == null
            ? null
            : DateTime.parse(json["extraction_timestamp"]),
        storageOptimization: json["storage_optimization"],
        accessEndpoints: json["access_endpoints"] == null
            ? null
            : AccessEndpoints.fromJson(json["access_endpoints"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "session_id": sessionId,
        "extraction_summary": extractionSummary?.toJson(),
        "extracted_data": extractedData?.toJson(),
        "stored_entities": storedEntities == null
            ? []
            : List<dynamic>.from(storedEntities!.map((x) => x.toJson())),
        "business_domain": businessDomain,
        "tenant_name": tenantName,
        "extraction_timestamp": extractionTimestamp?.toIso8601String(),
        "storage_optimization": storageOptimization,
        "access_endpoints": accessEndpoints?.toJson(),
      };
}

class AccessEndpoints {
  String? comprehensiveExport;
  String? rolesOnly;
  String? globalObjectivesOnly;
  String? localObjectivesOnly;
  String? entitiesOnly;

  AccessEndpoints({
    this.comprehensiveExport,
    this.rolesOnly,
    this.globalObjectivesOnly,
    this.localObjectivesOnly,
    this.entitiesOnly,
  });

  AccessEndpoints copyWith({
    String? comprehensiveExport,
    String? rolesOnly,
    String? globalObjectivesOnly,
    String? localObjectivesOnly,
    String? entitiesOnly,
  }) =>
      AccessEndpoints(
        comprehensiveExport: comprehensiveExport ?? this.comprehensiveExport,
        rolesOnly: rolesOnly ?? this.rolesOnly,
        globalObjectivesOnly: globalObjectivesOnly ?? this.globalObjectivesOnly,
        localObjectivesOnly: localObjectivesOnly ?? this.localObjectivesOnly,
        entitiesOnly: entitiesOnly ?? this.entitiesOnly,
      );

  factory AccessEndpoints.fromJson(Map<String, dynamic> json) =>
      AccessEndpoints(
        comprehensiveExport: json["comprehensive_export"],
        rolesOnly: json["roles_only"],
        globalObjectivesOnly: json["global_objectives_only"],
        localObjectivesOnly: json["local_objectives_only"],
        entitiesOnly: json["entities_only"],
      );

  Map<String, dynamic> toJson() => {
        "comprehensive_export": comprehensiveExport,
        "roles_only": rolesOnly,
        "global_objectives_only": globalObjectivesOnly,
        "local_objectives_only": localObjectivesOnly,
        "entities_only": entitiesOnly,
      };
}

class ExtractedData {
  List<Entity>? entities;
  Roles? roles;
  List<GlobalObjective>? globalObjectives;
  List<LocalObjective>? localObjectives;

  ExtractedData({
    this.entities,
    this.roles,
    this.globalObjectives,
    this.localObjectives,
  });

  ExtractedData copyWith({
    List<Entity>? entities,
    Roles? roles,
    List<GlobalObjective>? globalObjectives,
    List<LocalObjective>? localObjectives,
  }) =>
      ExtractedData(
        entities: entities ?? this.entities,
        roles: roles ?? this.roles,
        globalObjectives: globalObjectives ?? this.globalObjectives,
        localObjectives: localObjectives ?? this.localObjectives,
      );

  factory ExtractedData.fromJson(Map<String, dynamic> json) => ExtractedData(
        entities: json["entities"] == null
            ? []
            : List<Entity>.from(
                json["entities"]!.map((x) => Entity.fromJson(x))),
        roles: json["roles"] == null ? null : Roles.fromJson(json["roles"]),
        globalObjectives: json["global_objectives"] == null
            ? []
            : List<GlobalObjective>.from(json["global_objectives"]!
                .map((x) => GlobalObjective.fromJson(x))),
        localObjectives: json["local_objectives"] == null
            ? []
            : List<LocalObjective>.from(json["local_objectives"]!
                .map((x) => LocalObjective.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "entities": entities == null
            ? []
            : List<dynamic>.from(entities!.map((x) => x.toJson())),
        "roles": roles?.toJson(),
        "global_objectives": globalObjectives == null
            ? []
            : List<dynamic>.from(globalObjectives!.map((x) => x.toJson())),
        "local_objectives": localObjectives == null
            ? []
            : List<dynamic>.from(localObjectives!.map((x) => x.toJson())),
      };
}

class Entity {
  String? name;
  String? displayName;
  String? type;
  String? description;
  String? businessDomain;
  String? category;
  List<String>? tags;
  String? archivalStrategy;
  String? colorTheme;
  String? icon;
  String? entityDeclaration;
  List<Attribute>? attributes;
  List<EntityRelationship>? entityRelationships;
  List<AttributeBusinessRule>? attributeBusinessRules;
  List<UiProperty>? uiProperties;
  List<SecurityClassification>? securityClassification;
  List<EnumeratedValue>? enumeratedValues;
  List<SystemPermission>? systemPermissions;
  List<RoleSystemPermission>? roleSystemPermissions;

  Entity({
    this.name,
    this.displayName,
    this.type,
    this.description,
    this.businessDomain,
    this.category,
    this.tags,
    this.archivalStrategy,
    this.colorTheme,
    this.icon,
    this.entityDeclaration,
    this.attributes,
    this.entityRelationships,
    this.attributeBusinessRules,
    this.uiProperties,
    this.securityClassification,
    this.enumeratedValues,
    this.systemPermissions,
    this.roleSystemPermissions,
  });

  Entity copyWith({
    String? name,
    String? displayName,
    String? type,
    String? description,
    String? businessDomain,
    String? category,
    List<String>? tags,
    String? archivalStrategy,
    String? colorTheme,
    String? icon,
    String? entityDeclaration,
    List<Attribute>? attributes,
    List<EntityRelationship>? entityRelationships,
    List<AttributeBusinessRule>? attributeBusinessRules,
    List<UiProperty>? uiProperties,
    List<SecurityClassification>? securityClassification,
    List<EnumeratedValue>? enumeratedValues,
    List<SystemPermission>? systemPermissions,
    List<RoleSystemPermission>? roleSystemPermissions,
  }) =>
      Entity(
        name: name ?? this.name,
        displayName: displayName ?? this.displayName,
        type: type ?? this.type,
        description: description ?? this.description,
        businessDomain: businessDomain ?? this.businessDomain,
        category: category ?? this.category,
        tags: tags ?? this.tags,
        archivalStrategy: archivalStrategy ?? this.archivalStrategy,
        colorTheme: colorTheme ?? this.colorTheme,
        icon: icon ?? this.icon,
        entityDeclaration: entityDeclaration ?? this.entityDeclaration,
        attributes: attributes ?? this.attributes,
        entityRelationships: entityRelationships ?? this.entityRelationships,
        attributeBusinessRules:
            attributeBusinessRules ?? this.attributeBusinessRules,
        uiProperties: uiProperties ?? this.uiProperties,
        securityClassification:
            securityClassification ?? this.securityClassification,
        enumeratedValues: enumeratedValues ?? this.enumeratedValues,
        systemPermissions: systemPermissions ?? this.systemPermissions,
        roleSystemPermissions:
            roleSystemPermissions ?? this.roleSystemPermissions,
      );

  factory Entity.fromJson(Map<String, dynamic> json) => Entity(
        name: json["name"],
        displayName: json["displayName"],
        type: json["type"],
        description: json["description"],
        businessDomain: json["businessDomain"],
        category: json["category"],
        tags: json["tags"] == null
            ? []
            : List<String>.from(json["tags"]!.map((x) => x)),
        archivalStrategy: json["archivalStrategy"],
        colorTheme: json["colorTheme"],
        icon: json["icon"],
        entityDeclaration: json["entityDeclaration"],
        attributes: json["attributes"] == null
            ? []
            : List<Attribute>.from(
                json["attributes"]!.map((x) => Attribute.fromJson(x))),
        entityRelationships: json["entityRelationships"] == null
            ? []
            : List<EntityRelationship>.from(json["entityRelationships"]!
                .map((x) => EntityRelationship.fromJson(x))),
        attributeBusinessRules: json["attributeBusinessRules"] == null
            ? []
            : List<AttributeBusinessRule>.from(json["attributeBusinessRules"]!
                .map((x) => AttributeBusinessRule.fromJson(x))),
        uiProperties: json["uiProperties"] == null
            ? []
            : List<UiProperty>.from(
                json["uiProperties"]!.map((x) => UiProperty.fromJson(x))),
        securityClassification: json["securityClassification"] == null
            ? []
            : List<SecurityClassification>.from(json["securityClassification"]!
                .map((x) => SecurityClassification.fromJson(x))),
        enumeratedValues: json["enumeratedValues"] == null
            ? []
            : List<EnumeratedValue>.from(json["enumeratedValues"]!
                .map((x) => EnumeratedValue.fromJson(x))),
        systemPermissions: json["systemPermissions"] == null
            ? []
            : List<SystemPermission>.from(json["systemPermissions"]!
                .map((x) => SystemPermission.fromJson(x))),
        roleSystemPermissions: json["roleSystemPermissions"] == null
            ? []
            : List<RoleSystemPermission>.from(json["roleSystemPermissions"]!
                .map((x) => RoleSystemPermission.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "displayName": displayName,
        "type": type,
        "description": description,
        "businessDomain": businessDomain,
        "category": category,
        "tags": tags == null ? [] : List<dynamic>.from(tags!.map((x) => x)),
        "archivalStrategy": archivalStrategy,
        "colorTheme": colorTheme,
        "icon": icon,
        "entityDeclaration": entityDeclaration,
        "attributes": attributes == null
            ? []
            : List<dynamic>.from(attributes!.map((x) => x.toJson())),
        "entityRelationships": entityRelationships == null
            ? []
            : List<dynamic>.from(entityRelationships!.map((x) => x.toJson())),
        "attributeBusinessRules": attributeBusinessRules == null
            ? []
            : List<dynamic>.from(
                attributeBusinessRules!.map((x) => x.toJson())),
        "uiProperties": uiProperties == null
            ? []
            : List<dynamic>.from(uiProperties!.map((x) => x.toJson())),
        "securityClassification": securityClassification == null
            ? []
            : List<dynamic>.from(
                securityClassification!.map((x) => x.toJson())),
        "enumeratedValues": enumeratedValues == null
            ? []
            : List<dynamic>.from(enumeratedValues!.map((x) => x.toJson())),
        "systemPermissions": systemPermissions == null
            ? []
            : List<dynamic>.from(systemPermissions!.map((x) => x.toJson())),
        "roleSystemPermissions": roleSystemPermissions == null
            ? []
            : List<dynamic>.from(roleSystemPermissions!.map((x) => x.toJson())),
      };
}

class AttributeBusinessRule {
  String? entityName;
  String? attributeName;
  String? leftOperand;
  String? attributeBusinessRuleOperator;
  dynamic rightOperand;
  String? successValueRange;
  String? warningValueRange;
  String? failureValueRange;
  String? multiConditionOperator;
  String? warningMessage;
  String? successMessage;
  String? errorMessage;

  AttributeBusinessRule({
    this.entityName,
    this.attributeName,
    this.leftOperand,
    this.attributeBusinessRuleOperator,
    this.rightOperand,
    this.successValueRange,
    this.warningValueRange,
    this.failureValueRange,
    this.multiConditionOperator,
    this.warningMessage,
    this.successMessage,
    this.errorMessage,
  });

  AttributeBusinessRule copyWith({
    String? entityName,
    String? attributeName,
    String? leftOperand,
    String? attributeBusinessRuleOperator,
    dynamic rightOperand,
    String? successValueRange,
    String? warningValueRange,
    String? failureValueRange,
    String? multiConditionOperator,
    String? warningMessage,
    String? successMessage,
    String? errorMessage,
  }) =>
      AttributeBusinessRule(
        entityName: entityName ?? this.entityName,
        attributeName: attributeName ?? this.attributeName,
        leftOperand: leftOperand ?? this.leftOperand,
        attributeBusinessRuleOperator:
            attributeBusinessRuleOperator ?? this.attributeBusinessRuleOperator,
        rightOperand: rightOperand ?? this.rightOperand,
        successValueRange: successValueRange ?? this.successValueRange,
        warningValueRange: warningValueRange ?? this.warningValueRange,
        failureValueRange: failureValueRange ?? this.failureValueRange,
        multiConditionOperator:
            multiConditionOperator ?? this.multiConditionOperator,
        warningMessage: warningMessage ?? this.warningMessage,
        successMessage: successMessage ?? this.successMessage,
        errorMessage: errorMessage ?? this.errorMessage,
      );

  factory AttributeBusinessRule.fromJson(Map<String, dynamic> json) =>
      AttributeBusinessRule(
        entityName: json["entityName"],
        attributeName: json["attributeName"],
        leftOperand: json["leftOperand"],
        attributeBusinessRuleOperator: json["operator"],
        rightOperand: json["rightOperand"],
        successValueRange: json["successValueRange"],
        warningValueRange: json["warningValueRange"],
        failureValueRange: json["failureValueRange"],
        multiConditionOperator: json["multiConditionOperator"],
        warningMessage: json["warningMessage"],
        successMessage: json["successMessage"],
        errorMessage: json["errorMessage"],
      );

  Map<String, dynamic> toJson() => {
        "entityName": entityName,
        "attributeName": attributeName,
        "leftOperand": leftOperand,
        "operator": attributeBusinessRuleOperator,
        "rightOperand": rightOperand,
        "successValueRange": successValueRange,
        "warningValueRange": warningValueRange,
        "failureValueRange": failureValueRange,
        "multiConditionOperator": multiConditionOperator,
        "warningMessage": warningMessage,
        "successMessage": successMessage,
        "errorMessage": errorMessage,
      };
}

class Attribute {
  String? attributeName;
  String? displayName;
  String? dataType;
  bool? required;
  bool? unique;
  String? defaultType;
  dynamic defaultValue;
  String? description;
  String? helperText;
  int? maxLength;
  String? format;
  int? length;
  int? precision;
  int? minValue;
  int? maxValue;
  bool? computed;
  int? minLength;

  Attribute({
    this.attributeName,
    this.displayName,
    this.dataType,
    this.required,
    this.unique,
    this.defaultType,
    this.defaultValue,
    this.description,
    this.helperText,
    this.maxLength,
    this.format,
    this.length,
    this.precision,
    this.minValue,
    this.maxValue,
    this.computed,
    this.minLength,
  });

  Attribute copyWith({
    String? attributeName,
    String? displayName,
    String? dataType,
    bool? required,
    bool? unique,
    String? defaultType,
    dynamic defaultValue,
    String? description,
    String? helperText,
    int? maxLength,
    String? format,
    int? length,
    int? precision,
    int? minValue,
    int? maxValue,
    bool? computed,
    int? minLength,
  }) =>
      Attribute(
        attributeName: attributeName ?? this.attributeName,
        displayName: displayName ?? this.displayName,
        dataType: dataType ?? this.dataType,
        required: required ?? this.required,
        unique: unique ?? this.unique,
        defaultType: defaultType ?? this.defaultType,
        defaultValue: defaultValue ?? this.defaultValue,
        description: description ?? this.description,
        helperText: helperText ?? this.helperText,
        maxLength: maxLength ?? this.maxLength,
        format: format ?? this.format,
        length: length ?? this.length,
        precision: precision ?? this.precision,
        minValue: minValue ?? this.minValue,
        maxValue: maxValue ?? this.maxValue,
        computed: computed ?? this.computed,
        minLength: minLength ?? this.minLength,
      );

  factory Attribute.fromJson(Map<String, dynamic> json) => Attribute(
        attributeName: json["attributeName"],
        displayName: json["displayName"],
        dataType: json["dataType"],
        required: json["required"],
        unique: json["unique"],
        defaultType: json["defaultType"],
        defaultValue: json["defaultValue"],
        description: json["description"],
        helperText: json["helperText"],
        maxLength: json["maxLength"],
        format: json["format"],
        length: json["length"],
        precision: json["precision"],
        minValue: json["minValue"],
        maxValue: json["maxValue"],
        computed: json["computed"],
        minLength: json["minLength"],
      );

  Map<String, dynamic> toJson() => {
        "attributeName": attributeName,
        "displayName": displayName,
        "dataType": dataType,
        "required": required,
        "unique": unique,
        "defaultType": defaultType,
        "defaultValue": defaultValue,
        "description": description,
        "helperText": helperText,
        "maxLength": maxLength,
        "format": format,
        "length": length,
        "precision": precision,
        "minValue": minValue,
        "maxValue": maxValue,
        "computed": computed,
        "minLength": minLength,
      };
}

class EntityRelationship {
  String? primaryEntity;
  String? relatedEntity;
  String? primaryKey;
  String? foreignKey;
  String? relationshipType;
  String? onDelete;
  String? onUpdate;
  String? foreignKeyType;
  String? description;

  EntityRelationship({
    this.primaryEntity,
    this.relatedEntity,
    this.primaryKey,
    this.foreignKey,
    this.relationshipType,
    this.onDelete,
    this.onUpdate,
    this.foreignKeyType,
    this.description,
  });

  EntityRelationship copyWith({
    String? primaryEntity,
    String? relatedEntity,
    String? primaryKey,
    String? foreignKey,
    String? relationshipType,
    String? onDelete,
    String? onUpdate,
    String? foreignKeyType,
    String? description,
  }) =>
      EntityRelationship(
        primaryEntity: primaryEntity ?? this.primaryEntity,
        relatedEntity: relatedEntity ?? this.relatedEntity,
        primaryKey: primaryKey ?? this.primaryKey,
        foreignKey: foreignKey ?? this.foreignKey,
        relationshipType: relationshipType ?? this.relationshipType,
        onDelete: onDelete ?? this.onDelete,
        onUpdate: onUpdate ?? this.onUpdate,
        foreignKeyType: foreignKeyType ?? this.foreignKeyType,
        description: description ?? this.description,
      );

  factory EntityRelationship.fromJson(Map<String, dynamic> json) =>
      EntityRelationship(
        primaryEntity: json["primaryEntity"],
        relatedEntity: json["relatedEntity"],
        primaryKey: json["primaryKey"],
        foreignKey: json["foreignKey"],
        relationshipType: json["relationshipType"],
        onDelete: json["onDelete"],
        onUpdate: json["onUpdate"],
        foreignKeyType: json["foreignKeyType"],
        description: json["description"],
      );

  Map<String, dynamic> toJson() => {
        "primaryEntity": primaryEntity,
        "relatedEntity": relatedEntity,
        "primaryKey": primaryKey,
        "foreignKey": foreignKey,
        "relationshipType": relationshipType,
        "onDelete": onDelete,
        "onUpdate": onUpdate,
        "foreignKeyType": foreignKeyType,
        "description": description,
      };
}

class EnumeratedValue {
  String? entityAttribute;
  String? enumName;
  String? value;
  String? display;
  String? description;
  int? sortOrder;
  bool? active;
  List<Value>? values;

  EnumeratedValue({
    this.entityAttribute,
    this.enumName,
    this.value,
    this.display,
    this.description,
    this.sortOrder,
    this.active,
    this.values,
  });

  EnumeratedValue copyWith({
    String? entityAttribute,
    String? enumName,
    String? value,
    String? display,
    String? description,
    int? sortOrder,
    bool? active,
    List<Value>? values,
  }) =>
      EnumeratedValue(
        entityAttribute: entityAttribute ?? this.entityAttribute,
        enumName: enumName ?? this.enumName,
        value: value ?? this.value,
        display: display ?? this.display,
        description: description ?? this.description,
        sortOrder: sortOrder ?? this.sortOrder,
        active: active ?? this.active,
        values: values ?? this.values,
      );

  factory EnumeratedValue.fromJson(Map<String, dynamic> json) =>
      EnumeratedValue(
        entityAttribute: json["Entity.Attribute"],
        enumName: json["enumName"],
        value: json["value"],
        display: json["display"],
        description: json["description"],
        sortOrder: json["sortOrder"],
        active: json["active"],
        values: json["values"] == null
            ? []
            : List<Value>.from(json["values"]!.map((x) => Value.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "Entity.Attribute": entityAttribute,
        "enumName": enumName,
        "value": value,
        "display": display,
        "description": description,
        "sortOrder": sortOrder,
        "active": active,
        "values": values == null
            ? []
            : List<dynamic>.from(values!.map((x) => x.toJson())),
      };
}

class Value {
  String? value;
  String? display;
  String? description;
  int? sortOrder;
  bool? active;

  Value({
    this.value,
    this.display,
    this.description,
    this.sortOrder,
    this.active,
  });

  Value copyWith({
    String? value,
    String? display,
    String? description,
    int? sortOrder,
    bool? active,
  }) =>
      Value(
        value: value ?? this.value,
        display: display ?? this.display,
        description: description ?? this.description,
        sortOrder: sortOrder ?? this.sortOrder,
        active: active ?? this.active,
      );

  factory Value.fromJson(Map<String, dynamic> json) => Value(
        value: json["value"],
        display: json["display"],
        description: json["description"],
        sortOrder: json["sortOrder"],
        active: json["active"],
      );

  Map<String, dynamic> toJson() => {
        "value": value,
        "display": display,
        "description": description,
        "sortOrder": sortOrder,
        "active": active,
      };
}

class RoleSystemPermission {
  String? roleId;
  String? permissionId;
  List<String>? grantedActions;
  RowLevelConditions? rowLevelConditions;
  String? naturalLanguage;
  int? version;
  String? status;

  RoleSystemPermission({
    this.roleId,
    this.permissionId,
    this.grantedActions,
    this.rowLevelConditions,
    this.naturalLanguage,
    this.version,
    this.status,
  });

  RoleSystemPermission copyWith({
    String? roleId,
    String? permissionId,
    List<String>? grantedActions,
    RowLevelConditions? rowLevelConditions,
    String? naturalLanguage,
    int? version,
    String? status,
  }) =>
      RoleSystemPermission(
        roleId: roleId ?? this.roleId,
        permissionId: permissionId ?? this.permissionId,
        grantedActions: grantedActions ?? this.grantedActions,
        rowLevelConditions: rowLevelConditions ?? this.rowLevelConditions,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
        version: version ?? this.version,
        status: status ?? this.status,
      );

  factory RoleSystemPermission.fromJson(Map<String, dynamic> json) =>
      RoleSystemPermission(
        roleId: json["roleId"],
        permissionId: json["permissionId"],
        grantedActions: json["grantedActions"] == null
            ? []
            : List<String>.from(json["grantedActions"]!.map((x) => x)),
        rowLevelConditions: json["rowLevelConditions"] == null ||
                json["rowLevelConditions"].isEmpty
            ? null
            : RowLevelConditions.fromJson(json["rowLevelConditions"]),
        naturalLanguage: json["naturalLanguage"],
        version: json["version"],
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "roleId": roleId,
        "permissionId": permissionId,
        "grantedActions": grantedActions == null
            ? []
            : List<dynamic>.from(grantedActions!.map((x) => x)),
        "rowLevelConditions": rowLevelConditions?.toJson(),
        "naturalLanguage": naturalLanguage,
        "version": version,
        "status": status,
      };
}

class RowLevelConditions {
  String? assignedTo;
  String? territoryId;
  String? rowLevelConditionsAssignedTo;
  String? assignedUserId;
  String? businessUnit;
  dynamic status;
  String? ownedBy;
  String? field;
  String? rowLevelConditionsOperator;
  dynamic value;
  String? isActive;
  String? businessUnitId;
  bool? active;
  String? department;

  RowLevelConditions({
    this.assignedTo,
    this.territoryId,
    this.rowLevelConditionsAssignedTo,
    this.assignedUserId,
    this.businessUnit,
    this.status,
    this.ownedBy,
    this.field,
    this.rowLevelConditionsOperator,
    this.value,
    this.isActive,
    this.businessUnitId,
    this.active,
    this.department,
  });

  RowLevelConditions copyWith({
    String? assignedTo,
    String? territoryId,
    String? rowLevelConditionsAssignedTo,
    String? assignedUserId,
    String? businessUnit,
    dynamic status,
    String? ownedBy,
    String? field,
    String? rowLevelConditionsOperator,
    int? value,
    String? isActive,
    String? businessUnitId,
    bool? active,
    String? department,
  }) =>
      RowLevelConditions(
        assignedTo: assignedTo ?? this.assignedTo,
        territoryId: territoryId ?? this.territoryId,
        rowLevelConditionsAssignedTo:
            rowLevelConditionsAssignedTo ?? this.rowLevelConditionsAssignedTo,
        assignedUserId: assignedUserId ?? this.assignedUserId,
        businessUnit: businessUnit ?? this.businessUnit,
        status: status ?? this.status,
        ownedBy: ownedBy ?? this.ownedBy,
        field: field ?? this.field,
        rowLevelConditionsOperator:
            rowLevelConditionsOperator ?? this.rowLevelConditionsOperator,
        value: value ?? this.value,
        isActive: isActive ?? this.isActive,
        businessUnitId: businessUnitId ?? this.businessUnitId,
        active: active ?? this.active,
        department: department ?? this.department,
      );

  factory RowLevelConditions.fromJson(Map<String, dynamic> json) =>
      RowLevelConditions(
        assignedTo: json["assignedTo"],
        territoryId: json["territoryId"],
        rowLevelConditionsAssignedTo: json["assigned_to"],
        assignedUserId: json["assigned_user_id"],
        businessUnit: json["businessUnit"],
        status: json["status"],
        ownedBy: json["ownedBy"],
        field: json["field"],
        rowLevelConditionsOperator: json["operator"],
        value: json["value"],
        isActive: json["isActive"],
        businessUnitId: json["businessUnitId"],
        active: json["active"],
        department: json["department"],
      );

  Map<String, dynamic> toJson() => {
        "assignedTo": assignedTo,
        "territoryId": territoryId,
        "assigned_to": rowLevelConditionsAssignedTo,
        "assigned_user_id": assignedUserId,
        "businessUnit": businessUnit,
        "status": status,
        "ownedBy": ownedBy,
        "field": field,
        "operator": rowLevelConditionsOperator,
        "value": value,
        "isActive": isActive,
        "businessUnitId": businessUnitId,
        "active": active,
        "department": department,
      };
}

class SecurityClassification {
  String? entityAttribute;
  String? classification;
  String? piiType;
  bool? encryptionRequired;
  String? encryptionType;
  bool? maskingRequired;
  String? maskingPattern;
  String? accessLevel;
  bool? auditTrail;
  String? dataResidency;
  String? retentionOverride;
  bool? anonymizationRequired;
  String? anonymizationMethod;
  List<String>? complianceFrameworks;

  SecurityClassification({
    this.entityAttribute,
    this.classification,
    this.piiType,
    this.encryptionRequired,
    this.encryptionType,
    this.maskingRequired,
    this.maskingPattern,
    this.accessLevel,
    this.auditTrail,
    this.dataResidency,
    this.retentionOverride,
    this.anonymizationRequired,
    this.anonymizationMethod,
    this.complianceFrameworks,
  });

  SecurityClassification copyWith({
    String? entityAttribute,
    String? classification,
    String? piiType,
    bool? encryptionRequired,
    String? encryptionType,
    bool? maskingRequired,
    String? maskingPattern,
    String? accessLevel,
    bool? auditTrail,
    String? dataResidency,
    String? retentionOverride,
    bool? anonymizationRequired,
    String? anonymizationMethod,
    List<String>? complianceFrameworks,
  }) =>
      SecurityClassification(
        entityAttribute: entityAttribute ?? this.entityAttribute,
        classification: classification ?? this.classification,
        piiType: piiType ?? this.piiType,
        encryptionRequired: encryptionRequired ?? this.encryptionRequired,
        encryptionType: encryptionType ?? this.encryptionType,
        maskingRequired: maskingRequired ?? this.maskingRequired,
        maskingPattern: maskingPattern ?? this.maskingPattern,
        accessLevel: accessLevel ?? this.accessLevel,
        auditTrail: auditTrail ?? this.auditTrail,
        dataResidency: dataResidency ?? this.dataResidency,
        retentionOverride: retentionOverride ?? this.retentionOverride,
        anonymizationRequired:
            anonymizationRequired ?? this.anonymizationRequired,
        anonymizationMethod: anonymizationMethod ?? this.anonymizationMethod,
        complianceFrameworks: complianceFrameworks ?? this.complianceFrameworks,
      );

  factory SecurityClassification.fromJson(Map<String, dynamic> json) =>
      SecurityClassification(
        entityAttribute: json["Entity.Attribute"],
        classification: json["classification"],
        piiType: json["piiType"],
        encryptionRequired: json["encryptionRequired"],
        encryptionType: json["encryptionType"],
        maskingRequired: json["maskingRequired"],
        maskingPattern: json["maskingPattern"],
        accessLevel: json["accessLevel"],
        auditTrail: json["auditTrail"],
        dataResidency: json["dataResidency"],
        retentionOverride: json["retentionOverride"],
        anonymizationRequired: json["anonymizationRequired"],
        anonymizationMethod: json["anonymizationMethod"],
        complianceFrameworks: json["complianceFrameworks"] == null
            ? []
            : List<String>.from(json["complianceFrameworks"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "Entity.Attribute": entityAttribute,
        "classification": classification,
        "piiType": piiType,
        "encryptionRequired": encryptionRequired,
        "encryptionType": encryptionType,
        "maskingRequired": maskingRequired,
        "maskingPattern": maskingPattern,
        "accessLevel": accessLevel,
        "auditTrail": auditTrail,
        "dataResidency": dataResidency,
        "retentionOverride": retentionOverride,
        "anonymizationRequired": anonymizationRequired,
        "anonymizationMethod": anonymizationMethod,
        "complianceFrameworks": complianceFrameworks == null
            ? []
            : List<dynamic>.from(complianceFrameworks!.map((x) => x)),
      };
}

class SystemPermission {
  String? permissionId;
  String? permissionName;
  String? permissionType;
  String? resourceIdentifier;
  List<String>? actions;
  String? description;
  String? scope;
  String? naturalLanguage;
  int? version;
  String? status;

  SystemPermission({
    this.permissionId,
    this.permissionName,
    this.permissionType,
    this.resourceIdentifier,
    this.actions,
    this.description,
    this.scope,
    this.naturalLanguage,
    this.version,
    this.status,
  });

  SystemPermission copyWith({
    String? permissionId,
    String? permissionName,
    String? permissionType,
    String? resourceIdentifier,
    List<String>? actions,
    String? description,
    String? scope,
    String? naturalLanguage,
    int? version,
    String? status,
  }) =>
      SystemPermission(
        permissionId: permissionId ?? this.permissionId,
        permissionName: permissionName ?? this.permissionName,
        permissionType: permissionType ?? this.permissionType,
        resourceIdentifier: resourceIdentifier ?? this.resourceIdentifier,
        actions: actions ?? this.actions,
        description: description ?? this.description,
        scope: scope ?? this.scope,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
        version: version ?? this.version,
        status: status ?? this.status,
      );

  factory SystemPermission.fromJson(Map<String, dynamic> json) =>
      SystemPermission(
        permissionId: json["permissionId"],
        permissionName: json["permissionName"],
        permissionType: json["permissionType"],
        resourceIdentifier: json["resourceIdentifier"],
        actions: json["actions"] == null
            ? []
            : List<String>.from(json["actions"]!.map((x) => x)),
        description: json["description"],
        scope: json["scope"],
        naturalLanguage: json["naturalLanguage"],
        version: json["version"],
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "permissionId": permissionId,
        "permissionName": permissionName,
        "permissionType": permissionType,
        "resourceIdentifier": resourceIdentifier,
        "actions":
            actions == null ? [] : List<dynamic>.from(actions!.map((x) => x)),
        "description": description,
        "scope": scope,
        "naturalLanguage": naturalLanguage,
        "version": version,
        "status": status,
      };
}

class UiProperty {
  String? entityAttribute;
  String? controlType;
  String? displayFormat;
  String? inputMask;
  String? placeholderText;
  bool? autoComplete;
  bool? readOnly;
  String? validationDisplay;
  String? helpTextPosition;
  String? label;
  bool? requiredIndicator;

  UiProperty({
    this.entityAttribute,
    this.controlType,
    this.displayFormat,
    this.inputMask,
    this.placeholderText,
    this.autoComplete,
    this.readOnly,
    this.validationDisplay,
    this.helpTextPosition,
    this.label,
    this.requiredIndicator,
  });

  UiProperty copyWith({
    String? entityAttribute,
    String? controlType,
    String? displayFormat,
    String? inputMask,
    String? placeholderText,
    bool? autoComplete,
    bool? readOnly,
    String? validationDisplay,
    String? helpTextPosition,
    String? label,
    bool? requiredIndicator,
  }) =>
      UiProperty(
        entityAttribute: entityAttribute ?? this.entityAttribute,
        controlType: controlType ?? this.controlType,
        displayFormat: displayFormat ?? this.displayFormat,
        inputMask: inputMask ?? this.inputMask,
        placeholderText: placeholderText ?? this.placeholderText,
        autoComplete: autoComplete ?? this.autoComplete,
        readOnly: readOnly ?? this.readOnly,
        validationDisplay: validationDisplay ?? this.validationDisplay,
        helpTextPosition: helpTextPosition ?? this.helpTextPosition,
        label: label ?? this.label,
        requiredIndicator: requiredIndicator ?? this.requiredIndicator,
      );

  factory UiProperty.fromJson(Map<String, dynamic> json) => UiProperty(
        entityAttribute: json["Entity.Attribute"],
        controlType: json["controlType"],
        displayFormat: json["displayFormat"],
        inputMask: json["inputMask"],
        placeholderText: json["placeholderText"],
        autoComplete: json["autoComplete"],
        readOnly: json["readOnly"],
        validationDisplay: json["validationDisplay"],
        helpTextPosition: json["helpTextPosition"],
        label: json["label"],
        requiredIndicator: json["requiredIndicator"],
      );

  Map<String, dynamic> toJson() => {
        "Entity.Attribute": entityAttribute,
        "controlType": controlType,
        "displayFormat": displayFormat,
        "inputMask": inputMask,
        "placeholderText": placeholderText,
        "autoComplete": autoComplete,
        "readOnly": readOnly,
        "validationDisplay": validationDisplay,
        "helpTextPosition": helpTextPosition,
        "label": label,
        "requiredIndicator": requiredIndicator,
      };
}

class GlobalObjective {
  String? name;
  String? description;
  String? primaryEntity;
  Classification? classification;
  ProcessOwnership? processOwnership;
  TriggerDefinition? triggerDefinition;
  LocalObjectives? localObjectives;
  PathwayDefinitions? pathwayDefinitions;
  List<Pathway>? pathways;
  List<BusinessRule>? businessRules;
  List<ValidationRule>? validationRules;

  GlobalObjective({
    this.name,
    this.description,
    this.primaryEntity,
    this.classification,
    this.processOwnership,
    this.triggerDefinition,
    this.localObjectives,
    this.pathwayDefinitions,
    this.pathways,
    this.businessRules,
    this.validationRules,
  });

  GlobalObjective copyWith({
    String? name,
    String? description,
    String? primaryEntity,
    Classification? classification,
    ProcessOwnership? processOwnership,
    TriggerDefinition? triggerDefinition,
    LocalObjectives? localObjectives,
    PathwayDefinitions? pathwayDefinitions,
    List<Pathway>? pathways,
    List<BusinessRule>? businessRules,
    List<ValidationRule>? validationRules,
  }) =>
      GlobalObjective(
        name: name ?? this.name,
        description: description ?? this.description,
        primaryEntity: primaryEntity ?? this.primaryEntity,
        classification: classification ?? this.classification,
        processOwnership: processOwnership ?? this.processOwnership,
        triggerDefinition: triggerDefinition ?? this.triggerDefinition,
        localObjectives: localObjectives ?? this.localObjectives,
        pathwayDefinitions: pathwayDefinitions ?? this.pathwayDefinitions,
        pathways: pathways ?? this.pathways,
        businessRules: businessRules ?? this.businessRules,
        validationRules: validationRules ?? this.validationRules,
      );

  factory GlobalObjective.fromJson(Map<String, dynamic> json) =>
      GlobalObjective(
        name: json["name"],
        description: json["description"],
        primaryEntity: json["primary_entity"],
        classification: json["classification"] == null
            ? null
            : Classification.fromJson(json["classification"]),
        processOwnership: json["process_ownership"] == null
            ? null
            : ProcessOwnership.fromJson(json["process_ownership"]),
        triggerDefinition: json["trigger_definition"] == null
            ? null
            : TriggerDefinition.fromJson(json["trigger_definition"]),
        localObjectives: json["local_objectives"] == null
            ? null
            : LocalObjectives.fromJson(json["local_objectives"]),
        pathwayDefinitions: json["pathway_definitions"] == null
            ? null
            : PathwayDefinitions.fromJson(json["pathway_definitions"]),
        pathways: json["pathways"] == null
            ? []
            : List<Pathway>.from(
                json["pathways"]!.map((x) => Pathway.fromJson(x))),
        businessRules: json["business_rules"] == null
            ? []
            : List<BusinessRule>.from(
                json["business_rules"]!.map((x) => BusinessRule.fromJson(x))),
        validationRules: json["validation_rules"] == null
            ? []
            : List<ValidationRule>.from(json["validation_rules"]!
                .map((x) => ValidationRule.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "description": description,
        "primary_entity": primaryEntity,
        "classification": classification?.toJson(),
        "process_ownership": processOwnership?.toJson(),
        "trigger_definition": triggerDefinition?.toJson(),
        "local_objectives": localObjectives?.toJson(),
        "pathway_definitions": pathwayDefinitions?.toJson(),
        "pathways": pathways == null
            ? []
            : List<dynamic>.from(pathways!.map((x) => x.toJson())),
        "business_rules": businessRules == null
            ? []
            : List<dynamic>.from(businessRules!.map((x) => x.toJson())),
        "validation_rules": validationRules == null
            ? []
            : List<dynamic>.from(validationRules!.map((x) => x.toJson())),
      };
}

class BusinessRule {
  String? businessRuleName;
  String? description;
  List<String>? inputs;
  String? operation;
  String? output;
  String? error;
  String? validation;

  BusinessRule({
    this.businessRuleName,
    this.description,
    this.inputs,
    this.operation,
    this.output,
    this.error,
    this.validation,
  });

  BusinessRule copyWith({
    String? businessRuleName,
    String? description,
    List<String>? inputs,
    String? operation,
    String? output,
    String? error,
    String? validation,
  }) =>
      BusinessRule(
        businessRuleName: businessRuleName ?? this.businessRuleName,
        description: description ?? this.description,
        inputs: inputs ?? this.inputs,
        operation: operation ?? this.operation,
        output: output ?? this.output,
        error: error ?? this.error,
        validation: validation ?? this.validation,
      );

  factory BusinessRule.fromJson(Map<String, dynamic> json) => BusinessRule(
        businessRuleName: json["business_rule_name"],
        description: json["description"],
        inputs: json["inputs"] == null
            ? []
            : List<String>.from(json["inputs"]!.map((x) => x)),
        operation: json["operation"],
        output: json["output"],
        error: json["error"],
        validation: json["validation"],
      );

  Map<String, dynamic> toJson() => {
        "business_rule_name": businessRuleName,
        "description": description,
        "inputs":
            inputs == null ? [] : List<dynamic>.from(inputs!.map((x) => x)),
        "operation": operation,
        "output": output,
        "error": error,
        "validation": validation,
      };
}

class Classification {
  String? book;
  String? chapter;

  Classification({
    this.book,
    this.chapter,
  });

  Classification copyWith({
    String? book,
    String? chapter,
  }) =>
      Classification(
        book: book ?? this.book,
        chapter: chapter ?? this.chapter,
      );

  factory Classification.fromJson(Map<String, dynamic> json) => Classification(
        book: json["book"],
        chapter: json["chapter"],
      );

  Map<String, dynamic> toJson() => {
        "book": book,
        "chapter": chapter,
      };
}

class LocalObjectives {
  String? lo1;
  String? lo2;
  String? lo3;
  String? lo4;
  String? lo5;
  String? lo6;
  String? lo7;

  LocalObjectives({
    this.lo1,
    this.lo2,
    this.lo3,
    this.lo4,
    this.lo5,
    this.lo6,
    this.lo7,
  });

  LocalObjectives copyWith({
    String? lo1,
    String? lo2,
    String? lo3,
    String? lo4,
    String? lo5,
    String? lo6,
    String? lo7,
  }) =>
      LocalObjectives(
        lo1: lo1 ?? this.lo1,
        lo2: lo2 ?? this.lo2,
        lo3: lo3 ?? this.lo3,
        lo4: lo4 ?? this.lo4,
        lo5: lo5 ?? this.lo5,
        lo6: lo6 ?? this.lo6,
        lo7: lo7 ?? this.lo7,
      );

  factory LocalObjectives.fromJson(Map<String, dynamic> json) =>
      LocalObjectives(
        lo1: json["LO-1"],
        lo2: json["LO-2"],
        lo3: json["LO-3"],
        lo4: json["LO-4"],
        lo5: json["LO-5"],
        lo6: json["LO-6"],
        lo7: json["LO-7"],
      );

  Map<String, dynamic> toJson() => {
        "LO-1": lo1,
        "LO-2": lo2,
        "LO-3": lo3,
        "LO-4": lo4,
        "LO-5": lo5,
        "LO-6": lo6,
        "LO-7": lo7,
      };
}

class PathwayDefinitions {
  String? pathway1;
  String? steps;

  PathwayDefinitions({
    this.pathway1,
    this.steps,
  });

  PathwayDefinitions copyWith({
    String? pathway1,
    String? steps,
  }) =>
      PathwayDefinitions(
        pathway1: pathway1 ?? this.pathway1,
        steps: steps ?? this.steps,
      );

  factory PathwayDefinitions.fromJson(Map<String, dynamic> json) =>
      PathwayDefinitions(
        pathway1: json["PATHWAY-1"],
        steps: json["steps"],
      );

  Map<String, dynamic> toJson() => {
        "PATHWAY-1": pathway1,
        "steps": steps,
      };
}

class Pathway {
  String? objectiveName;
  String? type;
  String? description;
  String? routeType;
  List<String>? conditions;

  Pathway({
    this.objectiveName,
    this.type,
    this.description,
    this.routeType,
    this.conditions,
  });

  Pathway copyWith({
    String? objectiveName,
    String? type,
    String? description,
    String? routeType,
    List<String>? conditions,
  }) =>
      Pathway(
        objectiveName: objectiveName ?? this.objectiveName,
        type: type ?? this.type,
        description: description ?? this.description,
        routeType: routeType ?? this.routeType,
        conditions: conditions ?? this.conditions,
      );

  factory Pathway.fromJson(Map<String, dynamic> json) => Pathway(
        objectiveName: json["objective_name"],
        type: json["type"],
        description: json["description"],
        routeType: json["route_type"],
        conditions: json["conditions"] == null
            ? []
            : List<String>.from(json["conditions"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "objective_name": objectiveName,
        "type": type,
        "description": description,
        "route_type": routeType,
        "conditions": conditions == null
            ? []
            : List<dynamic>.from(conditions!.map((x) => x)),
      };
}

class ProcessOwnership {
  String? originator;
  String? processOwner;
  String? businessSponsor;

  ProcessOwnership({
    this.originator,
    this.processOwner,
    this.businessSponsor,
  });

  ProcessOwnership copyWith({
    String? originator,
    String? processOwner,
    String? businessSponsor,
  }) =>
      ProcessOwnership(
        originator: originator ?? this.originator,
        processOwner: processOwner ?? this.processOwner,
        businessSponsor: businessSponsor ?? this.businessSponsor,
      );

  factory ProcessOwnership.fromJson(Map<String, dynamic> json) =>
      ProcessOwnership(
        originator: json["originator"],
        processOwner: json["process_owner"],
        businessSponsor: json["business_sponsor"],
      );

  Map<String, dynamic> toJson() => {
        "originator": originator,
        "process_owner": processOwner,
        "business_sponsor": businessSponsor,
      };
}

class TriggerDefinition {
  String? triggerType;
  List<String>? triggerAttributes;
  String? triggerCondition;
  String? triggerSchedule;

  TriggerDefinition({
    this.triggerType,
    this.triggerAttributes,
    this.triggerCondition,
    this.triggerSchedule,
  });

  TriggerDefinition copyWith({
    String? triggerType,
    List<String>? triggerAttributes,
    String? triggerCondition,
    String? triggerSchedule,
  }) =>
      TriggerDefinition(
        triggerType: triggerType ?? this.triggerType,
        triggerAttributes: triggerAttributes ?? this.triggerAttributes,
        triggerCondition: triggerCondition ?? this.triggerCondition,
        triggerSchedule: triggerSchedule ?? this.triggerSchedule,
      );

  factory TriggerDefinition.fromJson(Map<String, dynamic> json) =>
      TriggerDefinition(
        triggerType: json["trigger_type"],
        triggerAttributes: json["trigger_attributes"] == null
            ? []
            : List<String>.from(json["trigger_attributes"]!.map((x) => x)),
        triggerCondition: json["trigger_condition"],
        triggerSchedule: json["trigger_schedule"],
      );

  Map<String, dynamic> toJson() => {
        "trigger_type": triggerType,
        "trigger_attributes": triggerAttributes == null
            ? []
            : List<dynamic>.from(triggerAttributes!.map((x) => x)),
        "trigger_condition": triggerCondition,
        "trigger_schedule": triggerSchedule,
      };
}

class ValidationRule {
  String? validationRuleName;
  String? inputs;
  String? operation;
  String? description;
  String? output;
  String? error;
  String? validation;

  ValidationRule({
    this.validationRuleName,
    this.inputs,
    this.operation,
    this.description,
    this.output,
    this.error,
    this.validation,
  });

  ValidationRule copyWith({
    String? validationRuleName,
    String? inputs,
    String? operation,
    String? description,
    String? output,
    String? error,
    String? validation,
  }) =>
      ValidationRule(
        validationRuleName: validationRuleName ?? this.validationRuleName,
        inputs: inputs ?? this.inputs,
        operation: operation ?? this.operation,
        description: description ?? this.description,
        output: output ?? this.output,
        error: error ?? this.error,
        validation: validation ?? this.validation,
      );

  factory ValidationRule.fromJson(Map<String, dynamic> json) => ValidationRule(
        validationRuleName: json["validation_rule_name"],
        inputs: json["inputs"],
        operation: json["operation"],
        description: json["description"],
        output: json["output"],
        error: json["error"],
        validation: json["validation"],
      );

  Map<String, dynamic> toJson() => {
        "validation_rule_name": validationRuleName,
        "inputs": inputs,
        "operation": operation,
        "description": description,
        "output": output,
        "error": error,
        "validation": validation,
      };
}

class LocalObjective {
  LoStructure? loStructure;

  LocalObjective({
    this.loStructure,
  });

  LocalObjective copyWith({
    LoStructure? loStructure,
  }) =>
      LocalObjective(
        loStructure: loStructure ?? this.loStructure,
      );

  factory LocalObjective.fromJson(Map<String, dynamic> json) => LocalObjective(
        loStructure:
            json["LO_Structure"] == null || json['LO_Structure'].isEmpty
                ? null
                : LoStructure.fromJson(json["LO_Structure"]),
      );

  Map<String, dynamic> toJson() => {
        "LO_Structure": loStructure?.toJson(),
      };
}

class LoStructure {
  String? loName;
  CoreConfiguration? coreConfiguration;
  InputSpecification? inputSpecification;
  OutputSpecification? outputSpecification;
  List<ValidationStack>? validationStack;
  List<UiStack>? uiStack;
  List<MappingStack>? mappingStack;
  List<String>? nestedFunctionStack;
  NestedFunctionPathwayDefinitions? nestedFunctionPathwayDefinitions;
  List<String>? nestedFunctionPathways;
  List<NestedFunctionDetailing>? nestedFunctionDetailing;
  List<String>? executionPathway;

  LoStructure({
    this.loName,
    this.coreConfiguration,
    this.inputSpecification,
    this.outputSpecification,
    this.validationStack,
    this.uiStack,
    this.mappingStack,
    this.nestedFunctionStack,
    this.nestedFunctionPathwayDefinitions,
    this.nestedFunctionPathways,
    this.nestedFunctionDetailing,
    this.executionPathway,
  });

  LoStructure copyWith({
    String? loName,
    CoreConfiguration? coreConfiguration,
    InputSpecification? inputSpecification,
    OutputSpecification? outputSpecification,
    List<ValidationStack>? validationStack,
    List<UiStack>? uiStack,
    List<MappingStack>? mappingStack,
    List<String>? nestedFunctionStack,
    NestedFunctionPathwayDefinitions? nestedFunctionPathwayDefinitions,
    List<String>? nestedFunctionPathways,
    List<NestedFunctionDetailing>? nestedFunctionDetailing,
    List<String>? executionPathway,
  }) =>
      LoStructure(
        loName: loName ?? this.loName,
        coreConfiguration: coreConfiguration ?? this.coreConfiguration,
        inputSpecification: inputSpecification ?? this.inputSpecification,
        outputSpecification: outputSpecification ?? this.outputSpecification,
        validationStack: validationStack ?? this.validationStack,
        uiStack: uiStack ?? this.uiStack,
        mappingStack: mappingStack ?? this.mappingStack,
        nestedFunctionStack: nestedFunctionStack ?? this.nestedFunctionStack,
        nestedFunctionPathwayDefinitions: nestedFunctionPathwayDefinitions ??
            this.nestedFunctionPathwayDefinitions,
        nestedFunctionPathways:
            nestedFunctionPathways ?? this.nestedFunctionPathways,
        nestedFunctionDetailing:
            nestedFunctionDetailing ?? this.nestedFunctionDetailing,
        executionPathway: executionPathway ?? this.executionPathway,
      );

  factory LoStructure.fromJson(Map<String, dynamic> json) => LoStructure(
        loName: json["loName"],
        coreConfiguration: json["coreConfiguration"] == null
            ? null
            : CoreConfiguration.fromJson(json["coreConfiguration"]),
        inputSpecification: json["inputSpecification"] == null
            ? null
            : InputSpecification.fromJson(json["inputSpecification"]),
        outputSpecification: json["outputSpecification"] == null
            ? null
            : OutputSpecification.fromJson(json["outputSpecification"]),
        validationStack: json["validationStack"] == null
            ? []
            : List<ValidationStack>.from(json["validationStack"]!
                .map((x) => ValidationStack.fromJson(x))),
        uiStack: json["uiStack"] == null
            ? []
            : List<UiStack>.from(
                json["uiStack"]!.map((x) => UiStack.fromJson(x))),
        mappingStack: json["mappingStack"] == null
            ? []
            : List<MappingStack>.from(
                json["mappingStack"]!.map((x) => MappingStack.fromJson(x))),
        nestedFunctionStack: json["nestedFunctionStack"] == null
            ? []
            : List<String>.from(json["nestedFunctionStack"]!.map((x) => x)),
        nestedFunctionPathwayDefinitions:
            json["nestedFunctionPathwayDefinitions"] == null
                ? null
                : NestedFunctionPathwayDefinitions.fromJson(
                    json["nestedFunctionPathwayDefinitions"]),
        nestedFunctionPathways: json["nestedFunctionPathways"] == null
            ? []
            : List<String>.from(json["nestedFunctionPathways"]!.map((x) => x)),
        nestedFunctionDetailing: json["nestedFunctionDetailing"] == null
            ? []
            : List<NestedFunctionDetailing>.from(
                json["nestedFunctionDetailing"]!
                    .map((x) => NestedFunctionDetailing.fromJson(x))),
        executionPathway: json["executionPathway"] == null
            ? []
            : List<String>.from(json["executionPathway"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "loName": loName,
        "coreConfiguration": coreConfiguration?.toJson(),
        "inputSpecification": inputSpecification?.toJson(),
        "outputSpecification": outputSpecification?.toJson(),
        "validationStack": validationStack == null
            ? []
            : List<dynamic>.from(validationStack!.map((x) => x.toJson())),
        "uiStack": uiStack == null
            ? []
            : List<dynamic>.from(uiStack!.map((x) => x.toJson())),
        "mappingStack": mappingStack == null
            ? []
            : List<dynamic>.from(mappingStack!.map((x) => x.toJson())),
        "nestedFunctionStack": nestedFunctionStack == null
            ? []
            : List<dynamic>.from(nestedFunctionStack!.map((x) => x)),
        "nestedFunctionPathwayDefinitions":
            nestedFunctionPathwayDefinitions?.toJson(),
        "nestedFunctionPathways": nestedFunctionPathways == null
            ? []
            : List<dynamic>.from(nestedFunctionPathways!.map((x) => x)),
        "nestedFunctionDetailing": nestedFunctionDetailing == null
            ? []
            : List<dynamic>.from(
                nestedFunctionDetailing!.map((x) => x.toJson())),
        "executionPathway": executionPathway == null
            ? []
            : List<dynamic>.from(executionPathway!.map((x) => x)),
      };
}

class CoreConfiguration {
  String? name;
  String? displayName;
  String? workflowOrigin;
  String? nextLoPathwayType;
  String? functionType;
  String? agentType;
  String? uiType;
  String? executionRights;

  CoreConfiguration({
    this.name,
    this.displayName,
    this.workflowOrigin,
    this.nextLoPathwayType,
    this.functionType,
    this.agentType,
    this.uiType,
    this.executionRights,
  });

  CoreConfiguration copyWith({
    String? name,
    String? displayName,
    String? workflowOrigin,
    String? nextLoPathwayType,
    String? functionType,
    String? agentType,
    String? uiType,
    String? executionRights,
  }) =>
      CoreConfiguration(
        name: name ?? this.name,
        displayName: displayName ?? this.displayName,
        workflowOrigin: workflowOrigin ?? this.workflowOrigin,
        nextLoPathwayType: nextLoPathwayType ?? this.nextLoPathwayType,
        functionType: functionType ?? this.functionType,
        agentType: agentType ?? this.agentType,
        uiType: uiType ?? this.uiType,
        executionRights: executionRights ?? this.executionRights,
      );

  factory CoreConfiguration.fromJson(Map<String, dynamic> json) =>
      CoreConfiguration(
        name: json["name"],
        displayName: json["displayName"],
        workflowOrigin: json["workflowOrigin"],
        nextLoPathwayType: json["nextLoPathwayType"],
        functionType: json["functionType"],
        agentType: json["agentType"],
        uiType: json["uiType"],
        executionRights: json["executionRights"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "displayName": displayName,
        "workflowOrigin": workflowOrigin,
        "nextLoPathwayType": nextLoPathwayType,
        "functionType": functionType,
        "agentType": agentType,
        "uiType": uiType,
        "executionRights": executionRights,
      };
}

class InputSpecification {
  String? inputs;

  InputSpecification({
    this.inputs,
  });

  InputSpecification copyWith({
    String? inputs,
  }) =>
      InputSpecification(
        inputs: inputs ?? this.inputs,
      );

  factory InputSpecification.fromJson(Map<String, dynamic> json) =>
      InputSpecification(
        inputs: json["inputs"],
      );

  Map<String, dynamic> toJson() => {
        "inputs": inputs,
      };
}

class MappingStack {
  String? sourceGoName;
  String? sourceLoName;
  String? sourceType;
  String? sourceEntity;
  String? sourceAttribute;
  String? targetGoName;
  String? targetLoName;
  String? targetType;
  String? targetEntity;
  String? targetAttribute;

  MappingStack({
    this.sourceGoName,
    this.sourceLoName,
    this.sourceType,
    this.sourceEntity,
    this.sourceAttribute,
    this.targetGoName,
    this.targetLoName,
    this.targetType,
    this.targetEntity,
    this.targetAttribute,
  });

  MappingStack copyWith({
    String? sourceGoName,
    String? sourceLoName,
    String? sourceType,
    String? sourceEntity,
    String? sourceAttribute,
    String? targetGoName,
    String? targetLoName,
    String? targetType,
    String? targetEntity,
    String? targetAttribute,
  }) =>
      MappingStack(
        sourceGoName: sourceGoName ?? this.sourceGoName,
        sourceLoName: sourceLoName ?? this.sourceLoName,
        sourceType: sourceType ?? this.sourceType,
        sourceEntity: sourceEntity ?? this.sourceEntity,
        sourceAttribute: sourceAttribute ?? this.sourceAttribute,
        targetGoName: targetGoName ?? this.targetGoName,
        targetLoName: targetLoName ?? this.targetLoName,
        targetType: targetType ?? this.targetType,
        targetEntity: targetEntity ?? this.targetEntity,
        targetAttribute: targetAttribute ?? this.targetAttribute,
      );

  factory MappingStack.fromJson(Map<String, dynamic> json) => MappingStack(
        sourceGoName: json["sourceGoName"],
        sourceLoName: json["sourceLoName"],
        sourceType: json["sourceType"],
        sourceEntity: json["sourceEntity"],
        sourceAttribute: json["sourceAttribute"],
        targetGoName: json["targetGoName"],
        targetLoName: json["targetLoName"],
        targetType: json["targetType"],
        targetEntity: json["targetEntity"],
        targetAttribute: json["targetAttribute"],
      );

  Map<String, dynamic> toJson() => {
        "sourceGoName": sourceGoName,
        "sourceLoName": sourceLoName,
        "sourceType": sourceType,
        "sourceEntity": sourceEntity,
        "sourceAttribute": sourceAttribute,
        "targetGoName": targetGoName,
        "targetLoName": targetLoName,
        "targetType": targetType,
        "targetEntity": targetEntity,
        "targetAttribute": targetAttribute,
      };
}

class NestedFunctionDetailing {
  String? function;
  String? sourceAttribute;
  String? functionName;
  String? inputs;
  String? conditions;
  String? outputs;

  NestedFunctionDetailing({
    this.function,
    this.sourceAttribute,
    this.functionName,
    this.inputs,
    this.conditions,
    this.outputs,
  });

  NestedFunctionDetailing copyWith({
    String? function,
    String? sourceAttribute,
    String? functionName,
    String? inputs,
    String? conditions,
    String? outputs,
  }) =>
      NestedFunctionDetailing(
        function: function ?? this.function,
        sourceAttribute: sourceAttribute ?? this.sourceAttribute,
        functionName: functionName ?? this.functionName,
        inputs: inputs ?? this.inputs,
        conditions: conditions ?? this.conditions,
        outputs: outputs ?? this.outputs,
      );

  factory NestedFunctionDetailing.fromJson(Map<String, dynamic> json) =>
      NestedFunctionDetailing(
        function: json["function"],
        sourceAttribute: json["sourceAttribute"],
        functionName: json["functionName"],
        inputs: json["inputs"],
        conditions: json["conditions"],
        outputs: json["outputs"],
      );

  Map<String, dynamic> toJson() => {
        "function": function,
        "sourceAttribute": sourceAttribute,
        "functionName": functionName,
        "inputs": inputs,
        "conditions": conditions,
        "outputs": outputs,
      };
}

class NestedFunctionPathwayDefinitions {
  String? pathway1;
  String? pathway2;
  String? pathway3;

  NestedFunctionPathwayDefinitions({
    this.pathway1,
    this.pathway2,
    this.pathway3,
  });

  NestedFunctionPathwayDefinitions copyWith({
    String? pathway1,
    String? pathway2,
    String? pathway3,
  }) =>
      NestedFunctionPathwayDefinitions(
        pathway1: pathway1 ?? this.pathway1,
        pathway2: pathway2 ?? this.pathway2,
        pathway3: pathway3 ?? this.pathway3,
      );

  factory NestedFunctionPathwayDefinitions.fromJson(
          Map<String, dynamic> json) =>
      NestedFunctionPathwayDefinitions(
        pathway1: json["PATHWAY-1"],
        pathway2: json["PATHWAY-2"],
        pathway3: json["PATHWAY-3"],
      );

  Map<String, dynamic> toJson() => {
        "PATHWAY-1": pathway1,
        "PATHWAY-2": pathway2,
        "PATHWAY-3": pathway3,
      };
}

class OutputSpecification {
  String? outputs;

  OutputSpecification({
    this.outputs,
  });

  OutputSpecification copyWith({
    String? outputs,
  }) =>
      OutputSpecification(
        outputs: outputs ?? this.outputs,
      );

  factory OutputSpecification.fromJson(Map<String, dynamic> json) =>
      OutputSpecification(
        outputs: json["outputs"],
      );

  Map<String, dynamic> toJson() => {
        "outputs": outputs,
      };
}

class UiStack {
  String? entityAttribute;
  String? uiControl;
  String? dataType;
  bool? editable;
  bool? required;
  bool? hidden;

  UiStack({
    this.entityAttribute,
    this.uiControl,
    this.dataType,
    this.editable,
    this.required,
    this.hidden,
  });

  UiStack copyWith({
    String? entityAttribute,
    String? uiControl,
    String? dataType,
    bool? editable,
    bool? required,
    bool? hidden,
  }) =>
      UiStack(
        entityAttribute: entityAttribute ?? this.entityAttribute,
        uiControl: uiControl ?? this.uiControl,
        dataType: dataType ?? this.dataType,
        editable: editable ?? this.editable,
        required: required ?? this.required,
        hidden: hidden ?? this.hidden,
      );

  factory UiStack.fromJson(Map<String, dynamic> json) => UiStack(
        entityAttribute: json["entityAttribute"],
        uiControl: json["uiControl"],
        dataType: json["dataType"],
        editable: json["editable"],
        required: json["required"],
        hidden: json["hidden"],
      );

  Map<String, dynamic> toJson() => {
        "entityAttribute": entityAttribute,
        "uiControl": uiControl,
        "dataType": dataType,
        "editable": editable,
        "required": required,
        "hidden": hidden,
      };
}

class ValidationStack {
  String? attribute;
  String? validationFunction;
  String? successValue;
  String? failureValue;
  String? successMessage;
  String? failureMessage;

  ValidationStack({
    this.attribute,
    this.validationFunction,
    this.successValue,
    this.failureValue,
    this.successMessage,
    this.failureMessage,
  });

  ValidationStack copyWith({
    String? attribute,
    String? validationFunction,
    String? successValue,
    String? failureValue,
    String? successMessage,
    String? failureMessage,
  }) =>
      ValidationStack(
        attribute: attribute ?? this.attribute,
        validationFunction: validationFunction ?? this.validationFunction,
        successValue: successValue ?? this.successValue,
        failureValue: failureValue ?? this.failureValue,
        successMessage: successMessage ?? this.successMessage,
        failureMessage: failureMessage ?? this.failureMessage,
      );

  factory ValidationStack.fromJson(Map<String, dynamic> json) =>
      ValidationStack(
        attribute: json["attribute"],
        validationFunction: json["validationFunction"],
        successValue: json["successValue"],
        failureValue: json["failureValue"],
        successMessage: json["successMessage"],
        failureMessage: json["failureMessage"],
      );

  Map<String, dynamic> toJson() => {
        "attribute": attribute,
        "validationFunction": validationFunction,
        "successValue": successValue,
        "failureValue": failureValue,
        "successMessage": successMessage,
        "failureMessage": failureMessage,
      };
}

class Roles {
  List<Role>? roles;
  List<Department>? departments;

  Roles({
    this.roles,
    this.departments,
  });

  Roles copyWith({
    List<Role>? roles,
    List<Department>? departments,
  }) =>
      Roles(
        roles: roles ?? this.roles,
        departments: departments ?? this.departments,
      );

  factory Roles.fromJson(Map<String, dynamic> json) => Roles(
        roles: json["roles"] == null
            ? []
            : List<Role>.from(json["roles"]!.map((x) => Role.fromJson(x))),
        departments: json["departments"] == null
            ? []
            : List<Department>.from(
                json["departments"]!.map((x) => Department.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "roles": roles == null
            ? []
            : List<dynamic>.from(roles!.map((x) => x.toJson())),
        "departments": departments == null
            ? []
            : List<dynamic>.from(departments!.map((x) => x.toJson())),
      };
}

class Department {
  String? departmentName;
  String? description;
  String? departmentHeadRole;
  String? parentDepartment;

  Department({
    this.departmentName,
    this.description,
    this.departmentHeadRole,
    this.parentDepartment,
  });

  Department copyWith({
    String? departmentName,
    String? description,
    String? departmentHeadRole,
    String? parentDepartment,
  }) =>
      Department(
        departmentName: departmentName ?? this.departmentName,
        description: description ?? this.description,
        departmentHeadRole: departmentHeadRole ?? this.departmentHeadRole,
        parentDepartment: parentDepartment ?? this.parentDepartment,
      );

  factory Department.fromJson(Map<String, dynamic> json) => Department(
        departmentName: json["departmentName"],
        description: json["description"],
        departmentHeadRole: json["departmentHeadRole"],
        parentDepartment: json["parentDepartment"],
      );

  Map<String, dynamic> toJson() => {
        "departmentName": departmentName,
        "description": description,
        "departmentHeadRole": departmentHeadRole,
        "parentDepartment": parentDepartment,
      };
}

class Role {
  String? roleName;
  String? description;
  String? reportsTo;
  String? organizationLevel;
  String? department;
  List<String>? inherits;

  Role({
    this.roleName,
    this.description,
    this.reportsTo,
    this.organizationLevel,
    this.department,
    this.inherits,
  });

  Role copyWith({
    String? roleName,
    String? description,
    String? reportsTo,
    String? organizationLevel,
    String? department,
    List<String>? inherits,
  }) =>
      Role(
        roleName: roleName ?? this.roleName,
        description: description ?? this.description,
        reportsTo: reportsTo ?? this.reportsTo,
        organizationLevel: organizationLevel ?? this.organizationLevel,
        department: department ?? this.department,
        inherits: inherits ?? this.inherits,
      );

  factory Role.fromJson(Map<String, dynamic> json) => Role(
        roleName: json["roleName"],
        description: json["description"],
        reportsTo: json["reportsTo"],
        organizationLevel: json["organizationLevel"],
        department: json["department"],
        inherits: json["inherits"] == null
            ? []
            : List<String>.from(json["inherits"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "roleName": roleName,
        "description": description,
        "reportsTo": reportsTo,
        "organizationLevel": organizationLevel,
        "department": department,
        "inherits":
            inherits == null ? [] : List<dynamic>.from(inherits!.map((x) => x)),
      };
}

class ExtractionSummary {
  int? entitiesExtracted;
  int? entitiesStored;
  int? rolesExtracted;
  int? departmentsExtracted;
  int? globalObjectivesExtracted;
  int? localObjectivesExtracted;
  int? totalAttributes;
  int? totalRelationships;
  int? totalBusinessRules;
  int? totalEnumValues;
  int? stagesCompleted;
  String? extractionMethod;
  double? confidenceScore;

  ExtractionSummary({
    this.entitiesExtracted,
    this.entitiesStored,
    this.rolesExtracted,
    this.departmentsExtracted,
    this.globalObjectivesExtracted,
    this.localObjectivesExtracted,
    this.totalAttributes,
    this.totalRelationships,
    this.totalBusinessRules,
    this.totalEnumValues,
    this.stagesCompleted,
    this.extractionMethod,
    this.confidenceScore,
  });

  ExtractionSummary copyWith({
    int? entitiesExtracted,
    int? entitiesStored,
    int? rolesExtracted,
    int? departmentsExtracted,
    int? globalObjectivesExtracted,
    int? localObjectivesExtracted,
    int? totalAttributes,
    int? totalRelationships,
    int? totalBusinessRules,
    int? totalEnumValues,
    int? stagesCompleted,
    String? extractionMethod,
    double? confidenceScore,
  }) =>
      ExtractionSummary(
        entitiesExtracted: entitiesExtracted ?? this.entitiesExtracted,
        entitiesStored: entitiesStored ?? this.entitiesStored,
        rolesExtracted: rolesExtracted ?? this.rolesExtracted,
        departmentsExtracted: departmentsExtracted ?? this.departmentsExtracted,
        globalObjectivesExtracted:
            globalObjectivesExtracted ?? this.globalObjectivesExtracted,
        localObjectivesExtracted:
            localObjectivesExtracted ?? this.localObjectivesExtracted,
        totalAttributes: totalAttributes ?? this.totalAttributes,
        totalRelationships: totalRelationships ?? this.totalRelationships,
        totalBusinessRules: totalBusinessRules ?? this.totalBusinessRules,
        totalEnumValues: totalEnumValues ?? this.totalEnumValues,
        stagesCompleted: stagesCompleted ?? this.stagesCompleted,
        extractionMethod: extractionMethod ?? this.extractionMethod,
        confidenceScore: confidenceScore ?? this.confidenceScore,
      );

  factory ExtractionSummary.fromJson(Map<String, dynamic> json) =>
      ExtractionSummary(
        entitiesExtracted: json["entities_extracted"],
        entitiesStored: json["entities_stored"],
        rolesExtracted: json["roles_extracted"],
        departmentsExtracted: json["departments_extracted"],
        globalObjectivesExtracted: json["global_objectives_extracted"],
        localObjectivesExtracted: json["local_objectives_extracted"],
        totalAttributes: json["total_attributes"],
        totalRelationships: json["total_relationships"],
        totalBusinessRules: json["total_business_rules"],
        totalEnumValues: json["total_enum_values"],
        stagesCompleted: json["stages_completed"],
        extractionMethod: json["extraction_method"],
        confidenceScore: json["confidence_score"]?.toDouble(),
      );

  Map<String, dynamic> toJson() => {
        "entities_extracted": entitiesExtracted,
        "entities_stored": entitiesStored,
        "roles_extracted": rolesExtracted,
        "departments_extracted": departmentsExtracted,
        "global_objectives_extracted": globalObjectivesExtracted,
        "local_objectives_extracted": localObjectivesExtracted,
        "total_attributes": totalAttributes,
        "total_relationships": totalRelationships,
        "total_business_rules": totalBusinessRules,
        "total_enum_values": totalEnumValues,
        "stages_completed": stagesCompleted,
        "extraction_method": extractionMethod,
        "confidence_score": confidenceScore,
      };
}

class StoredEntity {
  int? entityId;
  String? entityName;
  String? extractionMethod;

  StoredEntity({
    this.entityId,
    this.entityName,
    this.extractionMethod,
  });

  StoredEntity copyWith({
    int? entityId,
    String? entityName,
    String? extractionMethod,
  }) =>
      StoredEntity(
        entityId: entityId ?? this.entityId,
        entityName: entityName ?? this.entityName,
        extractionMethod: extractionMethod ?? this.extractionMethod,
      );

  factory StoredEntity.fromJson(Map<String, dynamic> json) => StoredEntity(
        entityId: json["entity_id"],
        entityName: json["entity_name"],
        extractionMethod: json["extraction_method"],
      );

  Map<String, dynamic> toJson() => {
        "entity_id": entityId,
        "entity_name": entityName,
        "extraction_method": extractionMethod,
      };
}
