class AttributeRule {
  final String role;
  final String assigned;
  final String source;
  final String sourceColor;

  AttributeRule({
    required this.role,
    required this.assigned,
    required this.source,
    required this.sourceColor,
  });

  AttributeRule copyWith({
    String? role,
    String? assigned,
    String? source,
    String? sourceColor,
  }) {
    return AttributeRule(
      role: role ?? this.role,
      assigned: assigned ?? this.assigned,
      source: source ?? this.source,
      sourceColor: sourceColor ?? this.sourceColor,
    );
  }
}
