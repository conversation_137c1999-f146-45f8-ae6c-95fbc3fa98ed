class Mode {
  final String id;
  final String name;
  final String description;

  Mode({
    required this.id,
    required this.name,
    required this.description,
  });

  factory Mode.fromJson(Map<String, dynamic> json) {
    return Mode(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
    };
  }
}

class ModesResponse {
  final List<Mode> modes;
  final String defaultMode;

  ModesResponse({
    required this.modes,
    required this.defaultMode,
  });

  factory ModesResponse.fromJson(Map<String, dynamic> json) {
    return ModesResponse(
      modes: (json['modes'] as List<dynamic>?)
              ?.map((mode) => Mode.fromJson(mode as Map<String, dynamic>))
              .toList() ??
          [],
      defaultMode: json['default_mode'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'modes': modes.map((mode) => mode.toJson()).toList(),
      'default_mode': defaultMode,
    };
  }
}
