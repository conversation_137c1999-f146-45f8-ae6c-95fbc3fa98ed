// To parse this JSON data, do
//
//     final mappingValidationModel = mappingValidationModelFromJson(jsonString);

import 'dart:convert';

MappingValidationModel mappingValidationModelFromJson(String str) =>
    MappingValidationModel.fromJson(json.decode(str));

String mappingValidationModelToJson(MappingValidationModel data) =>
    json.encode(data.toJson());

class MappingValidationModel {
  bool? success;
  List<String>? messages;
  ParsedData? parsedData;
  dynamic validationErrors;

  MappingValidationModel({
    this.success,
    this.messages,
    this.parsedData,
    this.validationErrors,
  });

  MappingValidationModel copyWith({
    bool? success,
    List<String>? messages,
    ParsedData? parsedData,
    dynamic validationErrors,
  }) =>
      MappingValidationModel(
        success: success ?? this.success,
        messages: messages ?? this.messages,
        parsedData: parsedData ?? this.parsedData,
        validationErrors: validationErrors ?? this.validationErrors,
      );

  factory MappingValidationModel.fromJson(Map<String, dynamic> json) =>
      MappingValidationModel(
        success: json["success"],
        messages: json["messages"] == null
            ? []
            : List<String>.from(json["messages"]!.map((x) => x)),
        parsedData: json["parsed_data"] == null
            ? null
            : ParsedData.fromJson(json["parsed_data"]),
        validationErrors: json["validation_errors"],
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "messages":
            messages == null ? [] : List<dynamic>.from(messages!.map((x) => x)),
        "parsed_data": parsedData?.toJson(),
        "validation_errors": validationErrors,
      };
}

class ParsedData {
  LoDataMappingStack? loDataMappingStack;
  List<LoDataMapping>? loDataMappings;

  ParsedData({
    this.loDataMappingStack,
    this.loDataMappings,
  });

  ParsedData copyWith({
    LoDataMappingStack? loDataMappingStack,
    List<LoDataMapping>? loDataMappings,
  }) =>
      ParsedData(
        loDataMappingStack: loDataMappingStack ?? this.loDataMappingStack,
        loDataMappings: loDataMappings ?? this.loDataMappings,
      );

  factory ParsedData.fromJson(Map<String, dynamic> json) => ParsedData(
        loDataMappingStack: json["lo_data_mapping_stack"] == null
            ? null
            : LoDataMappingStack.fromJson(json["lo_data_mapping_stack"]),
        loDataMappings: json["lo_data_mappings"] == null
            ? []
            : List<LoDataMapping>.from(json["lo_data_mappings"]!
                .map((x) => LoDataMapping.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "lo_data_mapping_stack": loDataMappingStack?.toJson(),
        "lo_data_mappings": loDataMappings == null
            ? []
            : List<dynamic>.from(loDataMappings!.map((x) => x.toJson())),
      };
}

class LoDataMappingStack {
  String? id;
  String? loDataMappingStackId;
  String? loId;
  String? description;
  dynamic createdAt;
  dynamic createdBy;
  dynamic updatedBy;
  dynamic updatedAt;
  String? naturalLanguage;

  LoDataMappingStack({
    this.id,
    this.loDataMappingStackId,
    this.loId,
    this.description,
    this.createdAt,
    this.createdBy,
    this.updatedBy,
    this.updatedAt,
    this.naturalLanguage,
  });

  LoDataMappingStack copyWith({
    String? id,
    String? loDataMappingStackId,
    String? loId,
    String? description,
    dynamic createdAt,
    dynamic createdBy,
    dynamic updatedBy,
    dynamic updatedAt,
    String? naturalLanguage,
  }) =>
      LoDataMappingStack(
        id: id ?? this.id,
        loDataMappingStackId: loDataMappingStackId ?? this.loDataMappingStackId,
        loId: loId ?? this.loId,
        description: description ?? this.description,
        createdAt: createdAt ?? this.createdAt,
        createdBy: createdBy ?? this.createdBy,
        updatedBy: updatedBy ?? this.updatedBy,
        updatedAt: updatedAt ?? this.updatedAt,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
      );

  factory LoDataMappingStack.fromJson(Map<String, dynamic> json) =>
      LoDataMappingStack(
        id: json["id"],
        loDataMappingStackId: json["lo_data_mapping_stack_id"],
        loId: json["lo_id"],
        description: json["description"],
        createdAt: json["created_at"],
        createdBy: json["created_by"],
        updatedBy: json["updated_by"],
        updatedAt: json["updated_at"],
        naturalLanguage: json["natural_language"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "lo_data_mapping_stack_id": loDataMappingStackId,
        "lo_id": loId,
        "description": description,
        "created_at": createdAt,
        "created_by": createdBy,
        "updated_by": updatedBy,
        "updated_at": updatedAt,
        "natural_language": naturalLanguage,
      };
}

class LoDataMapping {
  String? id;
  String? loDataMappingItemId;
  String? mappingStack;
  String? source;
  String? target;
  String? sourceOutputStackItemId;
  String? sourceEntity;
  String? sourceAttribute;
  String? targetInputStackItemId;
  String? targetEntity;
  String? targetAttribute;
  String? mappingType;
  String? naturalLanguage;
  String? sourceLoName;
  String? targetLoName;
  dynamic createdAt;
  dynamic createdBy;
  dynamic updatedAt;
  dynamic updatedBy;

  LoDataMapping({
    this.id,
    this.loDataMappingItemId,
    this.mappingStack,
    this.source,
    this.target,
    this.sourceOutputStackItemId,
    this.sourceEntity,
    this.sourceAttribute,
    this.targetInputStackItemId,
    this.targetEntity,
    this.targetAttribute,
    this.mappingType,
    this.naturalLanguage,
    this.sourceLoName,
    this.targetLoName,
    this.createdAt,
    this.createdBy,
    this.updatedAt,
    this.updatedBy,
  });

  LoDataMapping copyWith({
    String? id,
    String? loDataMappingItemId,
    String? mappingStack,
    String? source,
    String? target,
    String? sourceOutputStackItemId,
    String? sourceEntity,
    String? sourceAttribute,
    String? targetInputStackItemId,
    String? targetEntity,
    String? targetAttribute,
    String? mappingType,
    String? naturalLanguage,
    String? sourceLoName,
    String? targetLoName,
    dynamic createdAt,
    dynamic createdBy,
    dynamic updatedAt,
    dynamic updatedBy,
  }) =>
      LoDataMapping(
        id: id ?? this.id,
        loDataMappingItemId: loDataMappingItemId ?? this.loDataMappingItemId,
        mappingStack: mappingStack ?? this.mappingStack,
        source: source ?? this.source,
        target: target ?? this.target,
        sourceOutputStackItemId:
            sourceOutputStackItemId ?? this.sourceOutputStackItemId,
        sourceEntity: sourceEntity ?? this.sourceEntity,
        sourceAttribute: sourceAttribute ?? this.sourceAttribute,
        targetInputStackItemId:
            targetInputStackItemId ?? this.targetInputStackItemId,
        targetEntity: targetEntity ?? this.targetEntity,
        targetAttribute: targetAttribute ?? this.targetAttribute,
        mappingType: mappingType ?? this.mappingType,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
        sourceLoName: sourceLoName ?? this.sourceLoName,
        targetLoName: targetLoName ?? this.targetLoName,
        createdAt: createdAt ?? this.createdAt,
        createdBy: createdBy ?? this.createdBy,
        updatedAt: updatedAt ?? this.updatedAt,
        updatedBy: updatedBy ?? this.updatedBy,
      );

  factory LoDataMapping.fromJson(Map<String, dynamic> json) => LoDataMapping(
        id: json["id"],
        loDataMappingItemId: json["lo_data_mapping_item_id"],
        mappingStack: json["mapping_stack"],
        source: json["source"],
        target: json["target"],
        sourceOutputStackItemId: json["source_output_stack_item_id"],
        sourceEntity: json["source_entity"],
        sourceAttribute: json["source_attribute"],
        targetInputStackItemId: json["target_input_stack_item_id"],
        targetEntity: json["target_entity"],
        targetAttribute: json["target_attribute"],
        mappingType: json["mapping_type"],
        naturalLanguage: json["natural_language"],
        sourceLoName: json["source_lo_name"],
        targetLoName: json["target_lo_name"],
        createdAt: json["created_at"],
        createdBy: json["created_by"],
        updatedAt: json["updated_at"],
        updatedBy: json["updated_by"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "lo_data_mapping_item_id": loDataMappingItemId,
        "mapping_stack": mappingStack,
        "source": source,
        "target": target,
        "source_output_stack_item_id": sourceOutputStackItemId,
        "source_entity": sourceEntity,
        "source_attribute": sourceAttribute,
        "target_input_stack_item_id": targetInputStackItemId,
        "target_entity": targetEntity,
        "target_attribute": targetAttribute,
        "mapping_type": mappingType,
        "natural_language": naturalLanguage,
        "source_lo_name": sourceLoName,
        "target_lo_name": targetLoName,
        "created_at": createdAt,
        "created_by": createdBy,
        "updated_at": updatedAt,
        "updated_by": updatedBy,
      };
}
