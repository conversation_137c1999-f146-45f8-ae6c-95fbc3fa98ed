// To parse this JSON data, do
//
//     final getEntityRelationshipModel = getEntityRelationshipModelFromJson(jsonString);

import 'dart:convert';

GetEntityRelationshipModel getEntityRelationshipModelFromJson(String str) =>
    GetEntityRelationshipModel.fromJson(json.decode(str));

String getEntityRelationshipModelToJson(GetEntityRelationshipModel data) =>
    json.encode(data.toJson());

class GetEntityRelationshipModel {
  bool? success;
  String? entityId;
  List<MongoDraftRelationship>? postgresRelationships;
  List<MongoDraftRelationship>? mongoDrafts;
  int? totalPostgres;
  int? totalDrafts;
  String? operation;

  GetEntityRelationshipModel({
    this.success,
    this.entityId,
    this.postgresRelationships,
    this.mongoDrafts,
    this.totalPostgres,
    this.totalDrafts,
    this.operation,
  });

  GetEntityRelationshipModel copyWith({
    bool? success,
    String? entityId,
    List<MongoDraftRelationship>? postgresRelationships,
    List<MongoDraftRelationship>? mongoDrafts,
    int? totalPostgres,
    int? totalDrafts,
    String? operation,
  }) =>
      GetEntityRelationshipModel(
        success: success ?? this.success,
        entityId: entityId ?? this.entityId,
        postgresRelationships:
            postgresRelationships ?? this.postgresRelationships,
        mongoDrafts: mongoDrafts ?? this.mongoDrafts,
        totalPostgres: totalPostgres ?? this.totalPostgres,
        totalDrafts: totalDrafts ?? this.totalDrafts,
        operation: operation ?? this.operation,
      );

  factory GetEntityRelationshipModel.fromJson(Map<String, dynamic> json) =>
      GetEntityRelationshipModel(
        success: json["success"],
        entityId: json["entity_id"],
        postgresRelationships: json["postgres_relationships"] == null
            ? []
            : List<MongoDraftRelationship>.from(json["postgres_relationships"]!
                .map((x) => MongoDraftRelationship.fromJson(x))),
        mongoDrafts: json["mongo_drafts"] == null
            ? []
            : List<MongoDraftRelationship>.from(json["mongo_drafts"]!
                .map((x) => MongoDraftRelationship.fromJson(x))),
        totalPostgres: json["total_postgres"],
        totalDrafts: json["total_drafts"],
        operation: json["operation"],
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "entity_id": entityId,
        "postgres_relationships": postgresRelationships == null
            ? []
            : List<dynamic>.from(postgresRelationships!.map((x) => x)),
        "mongo_drafts": mongoDrafts == null
            ? []
            : List<dynamic>.from(mongoDrafts!.map((x) => x.toJson())),
        "total_postgres": totalPostgres,
        "total_drafts": totalDrafts,
        "operation": operation,
      };
}

class MongoDraftRelationship {
  String? id;
  int? relationshipId;
  String? sourceEntityId;
  String? targetEntityId;
  String? relationshipType;
  String? sourceAttributeId;
  String? targetAttributeId;
  String? onDelete;
  String? onUpdate;
  String? foreignKeyType;
  String? description;
  String? version;
  String? status;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? createdBy;
  String? updatedBy;
  String? sourceEntityName;
  String? targetEntityName;
  String? sourceAttributeName;
  String? targetAttributeName;

  MongoDraftRelationship({
    this.id,
    this.relationshipId,
    this.sourceEntityId,
    this.targetEntityId,
    this.relationshipType,
    this.sourceAttributeId,
    this.targetAttributeId,
    this.onDelete,
    this.onUpdate,
    this.foreignKeyType,
    this.description,
    this.version,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
    this.sourceEntityName,
    this.targetEntityName,
    this.sourceAttributeName,
    this.targetAttributeName,
  });

  MongoDraftRelationship copyWith({
    String? id,
    int? relationshipId,
    String? sourceEntityId,
    String? targetEntityId,
    String? relationshipType,
    String? sourceAttributeId,
    String? targetAttributeId,
    String? onDelete,
    String? onUpdate,
    String? foreignKeyType,
    String? description,
    String? version,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
    String? sourceEntityName,
    String? targetEntityName,
    String? sourceAttributeName,
    String? targetAttributeName,
  }) =>
      MongoDraftRelationship(
        id: id ?? this.id,
        relationshipId: relationshipId ?? this.relationshipId,
        sourceEntityId: sourceEntityId ?? this.sourceEntityId,
        targetEntityId: targetEntityId ?? this.targetEntityId,
        relationshipType: relationshipType ?? this.relationshipType,
        sourceAttributeId: sourceAttributeId ?? this.sourceAttributeId,
        targetAttributeId: targetAttributeId ?? this.targetAttributeId,
        onDelete: onDelete ?? this.onDelete,
        onUpdate: onUpdate ?? this.onUpdate,
        foreignKeyType: foreignKeyType ?? this.foreignKeyType,
        description: description ?? this.description,
        version: version ?? this.version,
        status: status ?? this.status,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        createdBy: createdBy ?? this.createdBy,
        updatedBy: updatedBy ?? this.updatedBy,
        sourceEntityName: sourceEntityName ?? this.sourceEntityName,
        targetEntityName: targetEntityName ?? this.targetEntityName,
        sourceAttributeName: sourceAttributeName ?? this.sourceAttributeName,
        targetAttributeName: targetAttributeName ?? this.targetAttributeName,
      );

  factory MongoDraftRelationship.fromJson(Map<String, dynamic> json) =>
      MongoDraftRelationship(
        id: json["_id"],
        relationshipId: json["relationship_id"],
        sourceEntityId: json["source_entity_id"],
        targetEntityId: json["target_entity_id"],
        relationshipType: json["relationship_type"],
        sourceAttributeId: json["source_attribute_id"],
        targetAttributeId: json["target_attribute_id"],
        onDelete: json["on_delete"],
        onUpdate: json["on_update"],
        foreignKeyType: json["foreign_key_type"],
        description: json["description"],
        version: json["version"],
        status: json["status"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        createdBy: json["created_by"],
        updatedBy: json["updated_by"],
        sourceEntityName: json["source_entity_name"],
        targetEntityName: json["target_entity_name"],
        sourceAttributeName: json["source_attribute_name"],
        targetAttributeName: json["target_attribute_name"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "relationship_id": relationshipId,
        "source_entity_id": sourceEntityId,
        "target_entity_id": targetEntityId,
        "relationship_type": relationshipType,
        "source_attribute_id": sourceAttributeId,
        "target_attribute_id": targetAttributeId,
        "on_delete": onDelete,
        "on_update": onUpdate,
        "foreign_key_type": foreignKeyType,
        "description": description,
        "version": version,
        "status": status,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "created_by": createdBy,
        "updated_by": updatedBy,
        "source_entity_name": sourceEntityName,
        "target_entity_name": targetEntityName,
        "source_attribute_name": sourceAttributeName,
        "target_attribute_name": targetAttributeName,
      };
}
