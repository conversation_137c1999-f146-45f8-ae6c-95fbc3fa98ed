// To parse this JSON data, do
//
//     final validateLoModel = validateLoModelFromJson(jsonString);

import 'dart:convert';

ValidateLoModel validateLoModelFromJson(String str) =>
    ValidateLoModel.fromJson(json.decode(str));

String validateLoModelToJson(ValidateLoModel data) =>
    json.encode(data.toJson());

class ValidateLoModel {
  bool? success;
  List<String>? messages;
  ParsedData? parsedData;
  List<ValidationError>? validationErrors;
  ErrorDetails? errorDetails;
  ParsedGos? parsedGos;
  ParsedLos? parsedLos;
  dynamic isValid;

  ValidateLoModel({
    this.success,
    this.messages,
    this.parsedData,
    this.validationErrors,
    this.errorDetails,
    this.parsedGos,
    this.parsedLos,
    this.isValid,
  });

  ValidateLoModel copyWith({
    bool? success,
    List<String>? messages,
    ParsedData? parsedData,
    List<ValidationError>? validationErrors,
    ErrorDetails? errorDetails,
    ParsedGos? parsedGos,
    ParsedLos? parsedLos,
    dynamic isValid,
  }) =>
      ValidateLoModel(
        success: success ?? this.success,
        messages: messages ?? this.messages,
        parsedData: parsedData ?? this.parsedData,
        validationErrors: validationErrors ?? this.validationErrors,
        errorDetails: errorDetails ?? this.errorDetails,
        parsedGos: parsedGos ?? this.parsedGos,
        parsedLos: parsedLos ?? this.parsedLos,
        isValid: isValid ?? this.isValid,
      );

  factory ValidateLoModel.fromJson(Map<String, dynamic> json) =>
      ValidateLoModel(
        success: json["success"],
        messages: json["messages"] == null
            ? []
            : List<String>.from(json["messages"]!.map((x) => x)),
        parsedData: json["parsed_data"] == null
            ? null
            : ParsedData.fromJson(json["parsed_data"]),
        validationErrors: json["validation_errors"] == null
            ? []
            : List<ValidationError>.from(json["validation_errors"]!
                .map((x) => ValidationError.fromJson(x))),
        errorDetails: json["error_details"] == null
            ? null
            : ErrorDetails.fromJson(json["error_details"]),
        parsedGos: json["parsed_gos"] == null
            ? null
            : ParsedGos.fromJson(json["parsed_gos"]),
        parsedLos: json["parsed_los"] == null
            ? null
            : ParsedLos.fromJson(json["parsed_los"]),
        isValid: json["is_valid"],
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "messages":
            messages == null ? [] : List<dynamic>.from(messages!.map((x) => x)),
        "parsed_data": parsedData?.toJson(),
        "validation_errors": validationErrors == null
            ? []
            : List<dynamic>.from(validationErrors!.map((x) => x.toJson())),
        "error_details": errorDetails?.toJson(),
        "parsed_gos": parsedGos?.toJson(),
        "parsed_los": parsedLos?.toJson(),
        "is_valid": isValid,
      };
}

class ErrorDetails {
  List<Error>? errors;
  List<dynamic>? warnings;
  List<dynamic>? info;
  Summary? summary;

  ErrorDetails({
    this.errors,
    this.warnings,
    this.info,
    this.summary,
  });

  ErrorDetails copyWith({
    List<Error>? errors,
    List<dynamic>? warnings,
    List<dynamic>? info,
    Summary? summary,
  }) =>
      ErrorDetails(
        errors: errors ?? this.errors,
        warnings: warnings ?? this.warnings,
        info: info ?? this.info,
        summary: summary ?? this.summary,
      );

  factory ErrorDetails.fromJson(Map<String, dynamic> json) => ErrorDetails(
        errors: json["errors"] == null
            ? []
            : List<Error>.from(json["errors"]!.map((x) => Error.fromJson(x))),
        warnings: json["warnings"] == null
            ? []
            : List<dynamic>.from(json["warnings"]!.map((x) => x)),
        info: json["info"] == null
            ? []
            : List<dynamic>.from(json["info"]!.map((x) => x)),
        summary:
            json["summary"] == null ? null : Summary.fromJson(json["summary"]),
      );

  Map<String, dynamic> toJson() => {
        "errors": errors == null
            ? []
            : List<dynamic>.from(errors!.map((x) => x.toJson())),
        "warnings":
            warnings == null ? [] : List<dynamic>.from(warnings!.map((x) => x)),
        "info": info == null ? [] : List<dynamic>.from(info!.map((x) => x)),
        "summary": summary?.toJson(),
      };
}

class Error {
  String? type;
  String? message;
  String? location;
  String? code;
  String? severity;
  Details? details;
  DateTime? timestamp;

  Error({
    this.type,
    this.message,
    this.location,
    this.code,
    this.severity,
    this.details,
    this.timestamp,
  });

  Error copyWith({
    String? type,
    String? message,
    String? location,
    String? code,
    String? severity,
    Details? details,
    DateTime? timestamp,
  }) =>
      Error(
        type: type ?? this.type,
        message: message ?? this.message,
        location: location ?? this.location,
        code: code ?? this.code,
        severity: severity ?? this.severity,
        details: details ?? this.details,
        timestamp: timestamp ?? this.timestamp,
      );

  factory Error.fromJson(Map<String, dynamic> json) => Error(
        type: json["type"],
        message: json["message"],
        location: json["location"],
        code: json["code"],
        severity: json["severity"],
        details:
            json["details"] == null ? null : Details.fromJson(json["details"]),
        timestamp: json["timestamp"] == null
            ? null
            : DateTime.parse(json["timestamp"]),
      );

  Map<String, dynamic> toJson() => {
        "type": type,
        "message": message,
        "location": location,
        "code": code,
        "severity": severity,
        "details": details?.toJson(),
        "timestamp": timestamp?.toIso8601String(),
      };
}

class Details {
  String? sourceText;
  String? validationContext;

  Details({
    this.sourceText,
    this.validationContext,
  });

  Details copyWith({
    String? sourceText,
    String? validationContext,
  }) =>
      Details(
        sourceText: sourceText ?? this.sourceText,
        validationContext: validationContext ?? this.validationContext,
      );

  factory Details.fromJson(Map<String, dynamic> json) => Details(
        sourceText: json["source_text"],
        validationContext: json["validation_context"],
      );

  Map<String, dynamic> toJson() => {
        "source_text": sourceText,
        "validation_context": validationContext,
      };
}

class Summary {
  int? errorCount;
  int? warningCount;
  int? infoCount;
  bool? hasErrors;
  bool? hasWarnings;
  Context? context;

  Summary({
    this.errorCount,
    this.warningCount,
    this.infoCount,
    this.hasErrors,
    this.hasWarnings,
    this.context,
  });

  Summary copyWith({
    int? errorCount,
    int? warningCount,
    int? infoCount,
    bool? hasErrors,
    bool? hasWarnings,
    Context? context,
  }) =>
      Summary(
        errorCount: errorCount ?? this.errorCount,
        warningCount: warningCount ?? this.warningCount,
        infoCount: infoCount ?? this.infoCount,
        hasErrors: hasErrors ?? this.hasErrors,
        hasWarnings: hasWarnings ?? this.hasWarnings,
        context: context ?? this.context,
      );

  factory Summary.fromJson(Map<String, dynamic> json) => Summary(
        errorCount: json["error_count"],
        warningCount: json["warning_count"],
        infoCount: json["info_count"],
        hasErrors: json["has_errors"],
        hasWarnings: json["has_warnings"],
        context:
            json["context"] == null ? null : Context.fromJson(json["context"]),
      );

  Map<String, dynamic> toJson() => {
        "error_count": errorCount,
        "warning_count": warningCount,
        "info_count": infoCount,
        "has_errors": hasErrors,
        "has_warnings": hasWarnings,
        "context": context?.toJson(),
      };
}

class Context {
  String? operation;
  String? inputTextPreview;
  int? inputLength;
  bool? pipelineSuccess;
  LoggingCapture? loggingCapture;

  Context({
    this.operation,
    this.inputTextPreview,
    this.inputLength,
    this.pipelineSuccess,
    this.loggingCapture,
  });

  Context copyWith({
    String? operation,
    String? inputTextPreview,
    int? inputLength,
    bool? pipelineSuccess,
    LoggingCapture? loggingCapture,
  }) =>
      Context(
        operation: operation ?? this.operation,
        inputTextPreview: inputTextPreview ?? this.inputTextPreview,
        inputLength: inputLength ?? this.inputLength,
        pipelineSuccess: pipelineSuccess ?? this.pipelineSuccess,
        loggingCapture: loggingCapture ?? this.loggingCapture,
      );

  factory Context.fromJson(Map<String, dynamic> json) => Context(
        operation: json["operation"],
        inputTextPreview: json["input_text_preview"],
        inputLength: json["input_length"],
        pipelineSuccess: json["pipeline_success"],
        loggingCapture: json["logging_capture"] == null
            ? null
            : LoggingCapture.fromJson(json["logging_capture"]),
      );

  Map<String, dynamic> toJson() => {
        "operation": operation,
        "input_text_preview": inputTextPreview,
        "input_length": inputLength,
        "pipeline_success": pipelineSuccess,
        "logging_capture": loggingCapture?.toJson(),
      };
}

class LoggingCapture {
  int? totalLogsCaptured;
  LogCounts? logCounts;
  List<String>? loggersMonitored;

  LoggingCapture({
    this.totalLogsCaptured,
    this.logCounts,
    this.loggersMonitored,
  });

  LoggingCapture copyWith({
    int? totalLogsCaptured,
    LogCounts? logCounts,
    List<String>? loggersMonitored,
  }) =>
      LoggingCapture(
        totalLogsCaptured: totalLogsCaptured ?? this.totalLogsCaptured,
        logCounts: logCounts ?? this.logCounts,
        loggersMonitored: loggersMonitored ?? this.loggersMonitored,
      );

  factory LoggingCapture.fromJson(Map<String, dynamic> json) => LoggingCapture(
        totalLogsCaptured: json["total_logs_captured"],
        logCounts: json["log_counts"] == null
            ? null
            : LogCounts.fromJson(json["log_counts"]),
        loggersMonitored: json["loggers_monitored"] == null
            ? []
            : List<String>.from(json["loggers_monitored"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "total_logs_captured": totalLogsCaptured,
        "log_counts": logCounts?.toJson(),
        "loggers_monitored": loggersMonitored == null
            ? []
            : List<dynamic>.from(loggersMonitored!.map((x) => x)),
      };
}

class LogCounts {
  int? debug;
  int? info;
  int? warning;
  int? error;
  int? critical;

  LogCounts({
    this.debug,
    this.info,
    this.warning,
    this.error,
    this.critical,
  });

  LogCounts copyWith({
    int? debug,
    int? info,
    int? warning,
    int? error,
    int? critical,
  }) =>
      LogCounts(
        debug: debug ?? this.debug,
        info: info ?? this.info,
        warning: warning ?? this.warning,
        error: error ?? this.error,
        critical: critical ?? this.critical,
      );

  factory LogCounts.fromJson(Map<String, dynamic> json) => LogCounts(
        debug: json["debug"],
        info: json["info"],
        warning: json["warning"],
        error: json["error"],
        critical: json["critical"],
      );

  Map<String, dynamic> toJson() => {
        "debug": debug,
        "info": info,
        "warning": warning,
        "error": error,
        "critical": critical,
      };
}

class ParsedData {
  ParsedLos? localObjectives;
  LoPutStack? loInputStack;
  List<dynamic>? loInputItems;
  LoPutStack? loOutputStack;
  List<LoOutputItem>? loOutputItems;
  LoUiStack? loUiStack;
  List<LoUiEntityAttributeStack>? loUiEntityAttributeStack;
  List<LoNestedFunction>? loNestedFunctions;
  List<dynamic>? terminalPathways;
  List<LoInputValidation>? loInputValidations;
  List<dynamic>? loOutputMappings;
  List<dynamic>? loNestedFunctionInputItems;
  List<dynamic>? loNestedFunctionInputStacks;
  List<LoNestedFunctionMapping>? loNestedFunctionMappings;
  List<LoNestedFunctionOutputItem>? loNestedFunctionOutputItems;
  List<LoNestedFunctionOutputStack>? loNestedFunctionOutputStacks;
  List<dynamic>? dropdownDataSources;

  ParsedData({
    this.localObjectives,
    this.loInputStack,
    this.loInputItems,
    this.loOutputStack,
    this.loOutputItems,
    this.loUiStack,
    this.loUiEntityAttributeStack,
    this.loNestedFunctions,
    this.terminalPathways,
    this.loInputValidations,
    this.loOutputMappings,
    this.loNestedFunctionInputItems,
    this.loNestedFunctionInputStacks,
    this.loNestedFunctionMappings,
    this.loNestedFunctionOutputItems,
    this.loNestedFunctionOutputStacks,
    this.dropdownDataSources,
  });

  ParsedData copyWith({
    ParsedLos? localObjectives,
    LoPutStack? loInputStack,
    List<dynamic>? loInputItems,
    LoPutStack? loOutputStack,
    List<LoOutputItem>? loOutputItems,
    LoUiStack? loUiStack,
    List<LoUiEntityAttributeStack>? loUiEntityAttributeStack,
    List<LoNestedFunction>? loNestedFunctions,
    List<dynamic>? terminalPathways,
    List<LoInputValidation>? loInputValidations,
    List<dynamic>? loOutputMappings,
    List<dynamic>? loNestedFunctionInputItems,
    List<dynamic>? loNestedFunctionInputStacks,
    List<LoNestedFunctionMapping>? loNestedFunctionMappings,
    List<LoNestedFunctionOutputItem>? loNestedFunctionOutputItems,
    List<LoNestedFunctionOutputStack>? loNestedFunctionOutputStacks,
    List<dynamic>? dropdownDataSources,
  }) =>
      ParsedData(
        localObjectives: localObjectives ?? this.localObjectives,
        loInputStack: loInputStack ?? this.loInputStack,
        loInputItems: loInputItems ?? this.loInputItems,
        loOutputStack: loOutputStack ?? this.loOutputStack,
        loOutputItems: loOutputItems ?? this.loOutputItems,
        loUiStack: loUiStack ?? this.loUiStack,
        loUiEntityAttributeStack:
            loUiEntityAttributeStack ?? this.loUiEntityAttributeStack,
        loNestedFunctions: loNestedFunctions ?? this.loNestedFunctions,
        terminalPathways: terminalPathways ?? this.terminalPathways,
        loInputValidations: loInputValidations ?? this.loInputValidations,
        loOutputMappings: loOutputMappings ?? this.loOutputMappings,
        loNestedFunctionInputItems:
            loNestedFunctionInputItems ?? this.loNestedFunctionInputItems,
        loNestedFunctionInputStacks:
            loNestedFunctionInputStacks ?? this.loNestedFunctionInputStacks,
        loNestedFunctionMappings:
            loNestedFunctionMappings ?? this.loNestedFunctionMappings,
        loNestedFunctionOutputItems:
            loNestedFunctionOutputItems ?? this.loNestedFunctionOutputItems,
        loNestedFunctionOutputStacks:
            loNestedFunctionOutputStacks ?? this.loNestedFunctionOutputStacks,
        dropdownDataSources: dropdownDataSources ?? this.dropdownDataSources,
      );

  factory ParsedData.fromJson(Map<String, dynamic> json) => ParsedData(
        localObjectives: json["local_objectives"] == null
            ? null
            : ParsedLos.fromJson(json["local_objectives"]),
        loInputStack: json["lo_input_stack"] == null
            ? null
            : LoPutStack.fromJson(json["lo_input_stack"]),
        loInputItems: json["lo_input_items"] == null
            ? []
            : List<dynamic>.from(json["lo_input_items"]!.map((x) => x)),
        loOutputStack: json["lo_output_stack"] == null
            ? null
            : LoPutStack.fromJson(json["lo_output_stack"]),
        loOutputItems: json["lo_output_items"] == null
            ? []
            : List<LoOutputItem>.from(
                json["lo_output_items"]!.map((x) => LoOutputItem.fromJson(x))),
        loUiStack: json["lo_ui_stack"] == null
            ? null
            : LoUiStack.fromJson(json["lo_ui_stack"]),
        loUiEntityAttributeStack: json["lo_ui_entity_attribute_stack"] == null
            ? []
            : List<LoUiEntityAttributeStack>.from(
                json["lo_ui_entity_attribute_stack"]!
                    .map((x) => LoUiEntityAttributeStack.fromJson(x))),
        loNestedFunctions: json["lo_nested_functions"] == null
            ? []
            : List<LoNestedFunction>.from(json["lo_nested_functions"]!
                .map((x) => LoNestedFunction.fromJson(x))),
        terminalPathways: json["terminal_pathways"] == null
            ? []
            : List<dynamic>.from(json["terminal_pathways"]!.map((x) => x)),
        loInputValidations: json["lo_input_validations"] == null
            ? []
            : List<LoInputValidation>.from(json["lo_input_validations"]!
                .map((x) => LoInputValidation.fromJson(x))),
        loOutputMappings: json["lo_output_mappings"] == null
            ? []
            : List<dynamic>.from(json["lo_output_mappings"]!.map((x) => x)),
        loNestedFunctionInputItems:
            json["lo_nested_function_input_items"] == null
                ? []
                : List<dynamic>.from(
                    json["lo_nested_function_input_items"]!.map((x) => x)),
        loNestedFunctionInputStacks:
            json["lo_nested_function_input_stacks"] == null
                ? []
                : List<dynamic>.from(
                    json["lo_nested_function_input_stacks"]!.map((x) => x)),
        loNestedFunctionMappings: json["lo_nested_function_mappings"] == null
            ? []
            : List<LoNestedFunctionMapping>.from(
                json["lo_nested_function_mappings"]!
                    .map((x) => LoNestedFunctionMapping.fromJson(x))),
        loNestedFunctionOutputItems:
            json["lo_nested_function_output_items"] == null
                ? []
                : List<LoNestedFunctionOutputItem>.from(
                    json["lo_nested_function_output_items"]!
                        .map((x) => LoNestedFunctionOutputItem.fromJson(x))),
        loNestedFunctionOutputStacks:
            json["lo_nested_function_output_stacks"] == null
                ? []
                : List<LoNestedFunctionOutputStack>.from(
                    json["lo_nested_function_output_stacks"]!
                        .map((x) => LoNestedFunctionOutputStack.fromJson(x))),
        dropdownDataSources: json["dropdown_data_sources"] == null
            ? []
            : List<dynamic>.from(json["dropdown_data_sources"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "local_objectives": localObjectives?.toJson(),
        "lo_input_stack": loInputStack?.toJson(),
        "lo_input_items": loInputItems == null
            ? []
            : List<dynamic>.from(loInputItems!.map((x) => x)),
        "lo_output_stack": loOutputStack?.toJson(),
        "lo_output_items": loOutputItems == null
            ? []
            : List<dynamic>.from(loOutputItems!.map((x) => x.toJson())),
        "lo_ui_stack": loUiStack?.toJson(),
        "lo_ui_entity_attribute_stack": loUiEntityAttributeStack == null
            ? []
            : List<dynamic>.from(
                loUiEntityAttributeStack!.map((x) => x.toJson())),
        "lo_nested_functions": loNestedFunctions == null
            ? []
            : List<dynamic>.from(loNestedFunctions!.map((x) => x.toJson())),
        "terminal_pathways": terminalPathways == null
            ? []
            : List<dynamic>.from(terminalPathways!.map((x) => x)),
        "lo_input_validations": loInputValidations == null
            ? []
            : List<dynamic>.from(loInputValidations!.map((x) => x.toJson())),
        "lo_output_mappings": loOutputMappings == null
            ? []
            : List<dynamic>.from(loOutputMappings!.map((x) => x)),
        "lo_nested_function_input_items": loNestedFunctionInputItems == null
            ? []
            : List<dynamic>.from(loNestedFunctionInputItems!.map((x) => x)),
        "lo_nested_function_input_stacks": loNestedFunctionInputStacks == null
            ? []
            : List<dynamic>.from(loNestedFunctionInputStacks!.map((x) => x)),
        "lo_nested_function_mappings": loNestedFunctionMappings == null
            ? []
            : List<dynamic>.from(
                loNestedFunctionMappings!.map((x) => x.toJson())),
        "lo_nested_function_output_items": loNestedFunctionOutputItems == null
            ? []
            : List<dynamic>.from(
                loNestedFunctionOutputItems!.map((x) => x.toJson())),
        "lo_nested_function_output_stacks": loNestedFunctionOutputStacks == null
            ? []
            : List<dynamic>.from(
                loNestedFunctionOutputStacks!.map((x) => x.toJson())),
        "dropdown_data_sources": dropdownDataSources == null
            ? []
            : List<dynamic>.from(dropdownDataSources!.map((x) => x)),
      };
}

class LoPutStack {
  String? id;
  String? loId;
  String? description;
  String? loInputStackId;
  String? naturalLanguage;

  LoPutStack({
    this.id,
    this.loId,
    this.description,
    this.loInputStackId,
    this.naturalLanguage,
  });

  LoPutStack copyWith({
    String? id,
    String? loId,
    String? description,
    String? loInputStackId,
    String? naturalLanguage,
  }) =>
      LoPutStack(
        id: id ?? this.id,
        loId: loId ?? this.loId,
        description: description ?? this.description,
        loInputStackId: loInputStackId ?? this.loInputStackId,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
      );

  factory LoPutStack.fromJson(Map<String, dynamic> json) => LoPutStack(
        id: json["id"],
        loId: json["lo_id"],
        description: json["description"],
        loInputStackId: json["lo_input_stack_id"],
        naturalLanguage: json["natural_language"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "lo_id": loId,
        "description": description,
        "lo_input_stack_id": loInputStackId,
        "natural_language": naturalLanguage,
      };
}

class LoInputValidation {
  String? id;
  String? loId;
  String? fieldName;
  String? validationType;
  String? validationRule;
  String? successValue;
  String? failureValue;
  String? successMessage;
  String? failureMessage;
  String? errorMessage;
  bool? isActive;
  dynamic createdAt;
  dynamic createdBy;
  dynamic updatedAt;
  dynamic updatedBy;
  String? naturalLanguage;

  LoInputValidation({
    this.id,
    this.loId,
    this.fieldName,
    this.validationType,
    this.validationRule,
    this.successValue,
    this.failureValue,
    this.successMessage,
    this.failureMessage,
    this.errorMessage,
    this.isActive,
    this.createdAt,
    this.createdBy,
    this.updatedAt,
    this.updatedBy,
    this.naturalLanguage,
  });

  LoInputValidation copyWith({
    String? id,
    String? loId,
    String? fieldName,
    String? validationType,
    String? validationRule,
    String? successValue,
    String? failureValue,
    String? successMessage,
    String? failureMessage,
    String? errorMessage,
    bool? isActive,
    dynamic createdAt,
    dynamic createdBy,
    dynamic updatedAt,
    dynamic updatedBy,
    String? naturalLanguage,
  }) =>
      LoInputValidation(
        id: id ?? this.id,
        loId: loId ?? this.loId,
        fieldName: fieldName ?? this.fieldName,
        validationType: validationType ?? this.validationType,
        validationRule: validationRule ?? this.validationRule,
        successValue: successValue ?? this.successValue,
        failureValue: failureValue ?? this.failureValue,
        successMessage: successMessage ?? this.successMessage,
        failureMessage: failureMessage ?? this.failureMessage,
        errorMessage: errorMessage ?? this.errorMessage,
        isActive: isActive ?? this.isActive,
        createdAt: createdAt ?? this.createdAt,
        createdBy: createdBy ?? this.createdBy,
        updatedAt: updatedAt ?? this.updatedAt,
        updatedBy: updatedBy ?? this.updatedBy,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
      );

  factory LoInputValidation.fromJson(Map<String, dynamic> json) =>
      LoInputValidation(
        id: json["id"],
        loId: json["lo_id"],
        fieldName: json["field_name"],
        validationType: json["validation_type"],
        validationRule: json["validation_rule"],
        successValue: json["success_value"],
        failureValue: json["failure_value"],
        successMessage: json["success_message"],
        failureMessage: json["failure_message"],
        errorMessage: json["error_message"],
        isActive: json["is_active"],
        createdAt: json["created_at"],
        createdBy: json["created_by"],
        updatedAt: json["updated_at"],
        updatedBy: json["updated_by"],
        naturalLanguage: json["natural_language"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "lo_id": loId,
        "field_name": fieldName,
        "validation_type": validationType,
        "validation_rule": validationRule,
        "success_value": successValue,
        "failure_value": failureValue,
        "success_message": successMessage,
        "failure_message": failureMessage,
        "error_message": errorMessage,
        "is_active": isActive,
        "created_at": createdAt,
        "created_by": createdBy,
        "updated_at": updatedAt,
        "updated_by": updatedBy,
        "natural_language": naturalLanguage,
      };
}

class LoNestedFunctionMapping {
  String? id;
  String? loId;
  String? functionName;
  String? sourceItem;
  String? targetGoName;
  String? targetLoName;
  String? targetNestedFunctionName;
  String? targetStack;
  String? targetItem;
  String? mappingType;
  String? transformationRule;
  dynamic createdAt;
  dynamic createdBy;
  dynamic updatedAt;
  dynamic updatedBy;
  String? naturalLanguage;

  LoNestedFunctionMapping({
    this.id,
    this.loId,
    this.functionName,
    this.sourceItem,
    this.targetGoName,
    this.targetLoName,
    this.targetNestedFunctionName,
    this.targetStack,
    this.targetItem,
    this.mappingType,
    this.transformationRule,
    this.createdAt,
    this.createdBy,
    this.updatedAt,
    this.updatedBy,
    this.naturalLanguage,
  });

  LoNestedFunctionMapping copyWith({
    String? id,
    String? loId,
    String? functionName,
    String? sourceItem,
    String? targetGoName,
    String? targetLoName,
    String? targetNestedFunctionName,
    String? targetStack,
    String? targetItem,
    String? mappingType,
    String? transformationRule,
    dynamic createdAt,
    dynamic createdBy,
    dynamic updatedAt,
    dynamic updatedBy,
    String? naturalLanguage,
  }) =>
      LoNestedFunctionMapping(
        id: id ?? this.id,
        loId: loId ?? this.loId,
        functionName: functionName ?? this.functionName,
        sourceItem: sourceItem ?? this.sourceItem,
        targetGoName: targetGoName ?? this.targetGoName,
        targetLoName: targetLoName ?? this.targetLoName,
        targetNestedFunctionName:
            targetNestedFunctionName ?? this.targetNestedFunctionName,
        targetStack: targetStack ?? this.targetStack,
        targetItem: targetItem ?? this.targetItem,
        mappingType: mappingType ?? this.mappingType,
        transformationRule: transformationRule ?? this.transformationRule,
        createdAt: createdAt ?? this.createdAt,
        createdBy: createdBy ?? this.createdBy,
        updatedAt: updatedAt ?? this.updatedAt,
        updatedBy: updatedBy ?? this.updatedBy,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
      );

  factory LoNestedFunctionMapping.fromJson(Map<String, dynamic> json) =>
      LoNestedFunctionMapping(
        id: json["id"],
        loId: json["lo_id"],
        functionName: json["function_name"],
        sourceItem: json["source_item"],
        targetGoName: json["target_go_name"],
        targetLoName: json["target_lo_name"],
        targetNestedFunctionName: json["target_nested_function_name"],
        targetStack: json["target_stack"],
        targetItem: json["target_item"],
        mappingType: json["mapping_type"],
        transformationRule: json["transformation_rule"],
        createdAt: json["created_at"],
        createdBy: json["created_by"],
        updatedAt: json["updated_at"],
        updatedBy: json["updated_by"],
        naturalLanguage: json["natural_language"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "lo_id": loId,
        "function_name": functionName,
        "source_item": sourceItem,
        "target_go_name": targetGoName,
        "target_lo_name": targetLoName,
        "target_nested_function_name": targetNestedFunctionName,
        "target_stack": targetStack,
        "target_item": targetItem,
        "mapping_type": mappingType,
        "transformation_rule": transformationRule,
        "created_at": createdAt,
        "created_by": createdBy,
        "updated_at": updatedAt,
        "updated_by": updatedBy,
        "natural_language": naturalLanguage,
      };
}

class LoNestedFunctionOutputItem {
  String? id;
  String? loId;
  String? functionName;
  String? outputName;
  String? outputDescription;
  String? dataType;
  bool? isRequired;
  dynamic format;
  String? entityId;
  String? attributeId;
  dynamic createdAt;
  dynamic createdBy;
  dynamic updatedAt;
  dynamic updatedBy;
  String? naturalLanguage;

  LoNestedFunctionOutputItem({
    this.id,
    this.loId,
    this.functionName,
    this.outputName,
    this.outputDescription,
    this.dataType,
    this.isRequired,
    this.format,
    this.entityId,
    this.attributeId,
    this.createdAt,
    this.createdBy,
    this.updatedAt,
    this.updatedBy,
    this.naturalLanguage,
  });

  LoNestedFunctionOutputItem copyWith({
    String? id,
    String? loId,
    String? functionName,
    String? outputName,
    String? outputDescription,
    String? dataType,
    bool? isRequired,
    dynamic format,
    String? entityId,
    String? attributeId,
    dynamic createdAt,
    dynamic createdBy,
    dynamic updatedAt,
    dynamic updatedBy,
    String? naturalLanguage,
  }) =>
      LoNestedFunctionOutputItem(
        id: id ?? this.id,
        loId: loId ?? this.loId,
        functionName: functionName ?? this.functionName,
        outputName: outputName ?? this.outputName,
        outputDescription: outputDescription ?? this.outputDescription,
        dataType: dataType ?? this.dataType,
        isRequired: isRequired ?? this.isRequired,
        format: format ?? this.format,
        entityId: entityId ?? this.entityId,
        attributeId: attributeId ?? this.attributeId,
        createdAt: createdAt ?? this.createdAt,
        createdBy: createdBy ?? this.createdBy,
        updatedAt: updatedAt ?? this.updatedAt,
        updatedBy: updatedBy ?? this.updatedBy,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
      );

  factory LoNestedFunctionOutputItem.fromJson(Map<String, dynamic> json) =>
      LoNestedFunctionOutputItem(
        id: json["id"],
        loId: json["lo_id"],
        functionName: json["function_name"],
        outputName: json["output_name"],
        outputDescription: json["output_description"],
        dataType: json["data_type"],
        isRequired: json["is_required"],
        format: json["format"],
        entityId: json["entity_id"],
        attributeId: json["attribute_id"],
        createdAt: json["created_at"],
        createdBy: json["created_by"],
        updatedAt: json["updated_at"],
        updatedBy: json["updated_by"],
        naturalLanguage: json["natural_language"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "lo_id": loId,
        "function_name": functionName,
        "output_name": outputName,
        "output_description": outputDescription,
        "data_type": dataType,
        "is_required": isRequired,
        "format": format,
        "entity_id": entityId,
        "attribute_id": attributeId,
        "created_at": createdAt,
        "created_by": createdBy,
        "updated_at": updatedAt,
        "updated_by": updatedBy,
        "natural_language": naturalLanguage,
      };
}

class LoNestedFunctionOutputStack {
  String? id;
  String? loId;
  String? functionName;
  String? stackName;
  String? stackDescription;
  String? stackType;
  bool? isActive;
  dynamic createdAt;
  dynamic createdBy;
  dynamic updatedAt;
  dynamic updatedBy;
  String? naturalLanguage;

  LoNestedFunctionOutputStack({
    this.id,
    this.loId,
    this.functionName,
    this.stackName,
    this.stackDescription,
    this.stackType,
    this.isActive,
    this.createdAt,
    this.createdBy,
    this.updatedAt,
    this.updatedBy,
    this.naturalLanguage,
  });

  LoNestedFunctionOutputStack copyWith({
    String? id,
    String? loId,
    String? functionName,
    String? stackName,
    String? stackDescription,
    String? stackType,
    bool? isActive,
    dynamic createdAt,
    dynamic createdBy,
    dynamic updatedAt,
    dynamic updatedBy,
    String? naturalLanguage,
  }) =>
      LoNestedFunctionOutputStack(
        id: id ?? this.id,
        loId: loId ?? this.loId,
        functionName: functionName ?? this.functionName,
        stackName: stackName ?? this.stackName,
        stackDescription: stackDescription ?? this.stackDescription,
        stackType: stackType ?? this.stackType,
        isActive: isActive ?? this.isActive,
        createdAt: createdAt ?? this.createdAt,
        createdBy: createdBy ?? this.createdBy,
        updatedAt: updatedAt ?? this.updatedAt,
        updatedBy: updatedBy ?? this.updatedBy,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
      );

  factory LoNestedFunctionOutputStack.fromJson(Map<String, dynamic> json) =>
      LoNestedFunctionOutputStack(
        id: json["id"],
        loId: json["lo_id"],
        functionName: json["function_name"],
        stackName: json["stack_name"],
        stackDescription: json["stack_description"],
        stackType: json["stack_type"],
        isActive: json["is_active"],
        createdAt: json["created_at"],
        createdBy: json["created_by"],
        updatedAt: json["updated_at"],
        updatedBy: json["updated_by"],
        naturalLanguage: json["natural_language"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "lo_id": loId,
        "function_name": functionName,
        "stack_name": stackName,
        "stack_description": stackDescription,
        "stack_type": stackType,
        "is_active": isActive,
        "created_at": createdAt,
        "created_by": createdBy,
        "updated_at": updatedAt,
        "updated_by": updatedBy,
        "natural_language": naturalLanguage,
      };
}

class LoNestedFunction {
  String? id;
  String? loId;
  String? functionName;
  String? functionType;
  List<dynamic>? functionParameters;
  String? description;
  String? returns;
  String? outputsTo;
  String? sourceAttribute;
  String? naturalLanguage;
  dynamic createdAt;
  dynamic createdBy;
  dynamic updatedAt;
  dynamic updatedBy;

  LoNestedFunction({
    this.id,
    this.loId,
    this.functionName,
    this.functionType,
    this.functionParameters,
    this.description,
    this.returns,
    this.outputsTo,
    this.sourceAttribute,
    this.naturalLanguage,
    this.createdAt,
    this.createdBy,
    this.updatedAt,
    this.updatedBy,
  });

  LoNestedFunction copyWith({
    String? id,
    String? loId,
    String? functionName,
    String? functionType,
    List<dynamic>? functionParameters,
    String? description,
    String? returns,
    String? outputsTo,
    String? sourceAttribute,
    String? naturalLanguage,
    dynamic createdAt,
    dynamic createdBy,
    dynamic updatedAt,
    dynamic updatedBy,
  }) =>
      LoNestedFunction(
        id: id ?? this.id,
        loId: loId ?? this.loId,
        functionName: functionName ?? this.functionName,
        functionType: functionType ?? this.functionType,
        functionParameters: functionParameters ?? this.functionParameters,
        description: description ?? this.description,
        returns: returns ?? this.returns,
        outputsTo: outputsTo ?? this.outputsTo,
        sourceAttribute: sourceAttribute ?? this.sourceAttribute,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
        createdAt: createdAt ?? this.createdAt,
        createdBy: createdBy ?? this.createdBy,
        updatedAt: updatedAt ?? this.updatedAt,
        updatedBy: updatedBy ?? this.updatedBy,
      );

  factory LoNestedFunction.fromJson(Map<String, dynamic> json) =>
      LoNestedFunction(
        id: json["id"],
        loId: json["lo_id"],
        functionName: json["function_name"],
        functionType: json["function_type"],
        functionParameters: json["function_parameters"] == null
            ? []
            : List<dynamic>.from(json["function_parameters"]!.map((x) => x)),
        description: json["description"],
        returns: json["returns"],
        outputsTo: json["outputs_to"],
        sourceAttribute: json["source_attribute"],
        naturalLanguage: json["natural_language"],
        createdAt: json["created_at"],
        createdBy: json["created_by"],
        updatedAt: json["updated_at"],
        updatedBy: json["updated_by"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "lo_id": loId,
        "function_name": functionName,
        "function_type": functionType,
        "function_parameters": functionParameters == null
            ? []
            : List<dynamic>.from(functionParameters!.map((x) => x)),
        "description": description,
        "returns": returns,
        "outputs_to": outputsTo,
        "source_attribute": sourceAttribute,
        "natural_language": naturalLanguage,
        "created_at": createdAt,
        "created_by": createdBy,
        "updated_at": updatedAt,
        "updated_by": updatedBy,
      };
}

class LoOutputItem {
  int? id;
  String? outputStackId;
  String? slotId;
  String? source;
  dynamic createdAt;
  dynamic updatedAt;
  String? loId;
  String? itemId;
  String? name;
  String? type;
  dynamic nestedFunctionId;
  String? createdBy;
  String? updatedBy;
  String? entityId;
  String? attributeId;
  String? entityName;
  String? attributeName;
  String? naturalLanguage;

  LoOutputItem({
    this.id,
    this.outputStackId,
    this.slotId,
    this.source,
    this.createdAt,
    this.updatedAt,
    this.loId,
    this.itemId,
    this.name,
    this.type,
    this.nestedFunctionId,
    this.createdBy,
    this.updatedBy,
    this.entityId,
    this.attributeId,
    this.entityName,
    this.attributeName,
    this.naturalLanguage,
  });

  LoOutputItem copyWith({
    int? id,
    String? outputStackId,
    String? slotId,
    String? source,
    dynamic createdAt,
    dynamic updatedAt,
    String? loId,
    String? itemId,
    String? name,
    String? type,
    dynamic nestedFunctionId,
    String? createdBy,
    String? updatedBy,
    String? entityId,
    String? attributeId,
    String? entityName,
    String? attributeName,
    String? naturalLanguage,
  }) =>
      LoOutputItem(
        id: id ?? this.id,
        outputStackId: outputStackId ?? this.outputStackId,
        slotId: slotId ?? this.slotId,
        source: source ?? this.source,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        loId: loId ?? this.loId,
        itemId: itemId ?? this.itemId,
        name: name ?? this.name,
        type: type ?? this.type,
        nestedFunctionId: nestedFunctionId ?? this.nestedFunctionId,
        createdBy: createdBy ?? this.createdBy,
        updatedBy: updatedBy ?? this.updatedBy,
        entityId: entityId ?? this.entityId,
        attributeId: attributeId ?? this.attributeId,
        entityName: entityName ?? this.entityName,
        attributeName: attributeName ?? this.attributeName,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
      );

  factory LoOutputItem.fromJson(Map<String, dynamic> json) => LoOutputItem(
        id: json["id"],
        outputStackId: json["output_stack_id"],
        slotId: json["slot_id"],
        source: json["source"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
        loId: json["lo_id"],
        itemId: json["item_id"],
        name: json["name"],
        type: json["type"],
        nestedFunctionId: json["nested_function_id"],
        createdBy: json["created_by"],
        updatedBy: json["updated_by"],
        entityId: json["entity_id"],
        attributeId: json["attribute_id"],
        entityName: json["entity_name"],
        attributeName: json["attribute_name"],
        naturalLanguage: json["natural_language"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "output_stack_id": outputStackId,
        "slot_id": slotId,
        "source": source,
        "created_at": createdAt,
        "updated_at": updatedAt,
        "lo_id": loId,
        "item_id": itemId,
        "name": name,
        "type": type,
        "nested_function_id": nestedFunctionId,
        "created_by": createdBy,
        "updated_by": updatedBy,
        "entity_id": entityId,
        "attribute_id": attributeId,
        "entity_name": entityName,
        "attribute_name": attributeName,
        "natural_language": naturalLanguage,
      };
}

class LoUiEntityAttributeStack {
  String? id;
  String? loUiEntityAttributeStackId;
  String? entityId;
  String? attributeId;
  String? uiForm;
  ParsedGos? styleParameters;
  dynamic helperTip;
  bool? readOnly;
  bool? required;
  bool? hidden;
  bool? isVisible;
  String? naturalLanguage;

  LoUiEntityAttributeStack({
    this.id,
    this.loUiEntityAttributeStackId,
    this.entityId,
    this.attributeId,
    this.uiForm,
    this.styleParameters,
    this.helperTip,
    this.readOnly,
    this.required,
    this.hidden,
    this.isVisible,
    this.naturalLanguage,
  });

  LoUiEntityAttributeStack copyWith({
    String? id,
    String? loUiEntityAttributeStackId,
    String? entityId,
    String? attributeId,
    String? uiForm,
    ParsedGos? styleParameters,
    dynamic helperTip,
    bool? readOnly,
    bool? required,
    bool? hidden,
    bool? isVisible,
    String? naturalLanguage,
  }) =>
      LoUiEntityAttributeStack(
        id: id ?? this.id,
        loUiEntityAttributeStackId:
            loUiEntityAttributeStackId ?? this.loUiEntityAttributeStackId,
        entityId: entityId ?? this.entityId,
        attributeId: attributeId ?? this.attributeId,
        uiForm: uiForm ?? this.uiForm,
        styleParameters: styleParameters ?? this.styleParameters,
        helperTip: helperTip ?? this.helperTip,
        readOnly: readOnly ?? this.readOnly,
        required: required ?? this.required,
        hidden: hidden ?? this.hidden,
        isVisible: isVisible ?? this.isVisible,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
      );

  factory LoUiEntityAttributeStack.fromJson(Map<String, dynamic> json) =>
      LoUiEntityAttributeStack(
        id: json["id"],
        loUiEntityAttributeStackId: json["lo_ui_entity_attribute_stack_id"],
        entityId: json["entity_id"],
        attributeId: json["attribute_id"],
        uiForm: json["ui_form"],
        styleParameters: json["style_parameters"] == null
            ? null
            : ParsedGos.fromJson(json["style_parameters"]),
        helperTip: json["helper_tip"],
        readOnly: json["read_only"],
        required: json["required"],
        hidden: json["hidden"],
        isVisible: json["is_visible"],
        naturalLanguage: json["natural_language"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "lo_ui_entity_attribute_stack_id": loUiEntityAttributeStackId,
        "entity_id": entityId,
        "attribute_id": attributeId,
        "ui_form": uiForm,
        "style_parameters": styleParameters?.toJson(),
        "helper_tip": helperTip,
        "read_only": readOnly,
        "required": required,
        "hidden": hidden,
        "is_visible": isVisible,
        "natural_language": naturalLanguage,
      };
}

class ParsedGos {
  ParsedGos();

  ParsedGos copyWith() => ParsedGos();

  factory ParsedGos.fromJson(Map<String, dynamic> json) => ParsedGos();

  Map<String, dynamic> toJson() => {};
}

class LoUiStack {
  String? stackId;
  String? loId;
  String? uiType;
  String? naturalLanguage;

  LoUiStack({
    this.stackId,
    this.loId,
    this.uiType,
    this.naturalLanguage,
  });

  LoUiStack copyWith({
    String? stackId,
    String? loId,
    String? uiType,
    String? naturalLanguage,
  }) =>
      LoUiStack(
        stackId: stackId ?? this.stackId,
        loId: loId ?? this.loId,
        uiType: uiType ?? this.uiType,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
      );

  factory LoUiStack.fromJson(Map<String, dynamic> json) => LoUiStack(
        stackId: json["stack_id"],
        loId: json["lo_id"],
        uiType: json["ui_type"],
        naturalLanguage: json["natural_language"],
      );

  Map<String, dynamic> toJson() => {
        "stack_id": stackId,
        "lo_id": loId,
        "ui_type": uiType,
        "natural_language": naturalLanguage,
      };
}

class ParsedLos {
  String? name;
  String? version;
  String? status;
  String? workflowSource;
  String? functionType;
  String? agentType;
  String? executionRights;
  String? tenantName;
  String? tenantId;
  String? uiType;
  String? naturalLanguage;
  String? goId;
  String? loId;

  ParsedLos({
    this.name,
    this.version,
    this.status,
    this.workflowSource,
    this.functionType,
    this.agentType,
    this.executionRights,
    this.tenantName,
    this.tenantId,
    this.uiType,
    this.naturalLanguage,
    this.goId,
    this.loId,
  });

  ParsedLos copyWith({
    String? name,
    String? version,
    String? status,
    String? workflowSource,
    String? functionType,
    String? agentType,
    String? executionRights,
    String? tenantName,
    String? tenantId,
    String? uiType,
    String? naturalLanguage,
    String? goId,
    String? loId,
  }) =>
      ParsedLos(
        name: name ?? this.name,
        version: version ?? this.version,
        status: status ?? this.status,
        workflowSource: workflowSource ?? this.workflowSource,
        functionType: functionType ?? this.functionType,
        agentType: agentType ?? this.agentType,
        executionRights: executionRights ?? this.executionRights,
        tenantName: tenantName ?? this.tenantName,
        tenantId: tenantId ?? this.tenantId,
        uiType: uiType ?? this.uiType,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
        goId: goId ?? this.goId,
        loId: loId ?? this.loId,
      );

  factory ParsedLos.fromJson(Map<String, dynamic> json) => ParsedLos(
        name: json["name"],
        version: json["version"],
        status: json["status"],
        workflowSource: json["workflow_source"],
        functionType: json["function_type"],
        agentType: json["agent_type"],
        executionRights: json["execution_rights"],
        tenantName: json["tenant_name"],
        tenantId: json["tenant_id"],
        uiType: json["ui_type"],
        naturalLanguage: json["natural_language"],
        goId: json["go_id"],
        loId: json["lo_id"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "version": version,
        "status": status,
        "workflow_source": workflowSource,
        "function_type": functionType,
        "agent_type": agentType,
        "execution_rights": executionRights,
        "tenant_name": tenantName,
        "tenant_id": tenantId,
        "ui_type": uiType,
        "natural_language": naturalLanguage,
        "go_id": goId,
        "lo_id": loId,
      };
}

class ValidationError {
  String? type;
  String? message;
  String? location;

  ValidationError({
    this.type,
    this.message,
    this.location,
  });

  ValidationError copyWith({
    String? type,
    String? message,
    String? location,
  }) =>
      ValidationError(
        type: type ?? this.type,
        message: message ?? this.message,
        location: location ?? this.location,
      );

  factory ValidationError.fromJson(Map<String, dynamic> json) =>
      ValidationError(
        type: json["type"],
        message: json["message"],
        location: json["location"],
      );

  Map<String, dynamic> toJson() => {
        "type": type,
        "message": message,
        "location": location,
      };
}
