// To parse this JSON data, do
//
//     final artifactSaveModel = artifactSaveModelFromJson(jsonString);

import 'dart:convert';

ArtifactSaveModel artifactSaveModelFromJson(String str) =>
    ArtifactSaveModel.fromJson(json.decode(str));

String artifactSaveModelToJson(ArtifactSaveModel data) =>
    json.encode(data.toJson());

class ArtifactSaveModel {
  bool? success;
  String? artifactId;
  String? message;
  Data? data;
  dynamic error;
  DateTime? timestamp;

  ArtifactSaveModel({
    this.success,
    this.artifactId,
    this.message,
    this.data,
    this.error,
    this.timestamp,
  });

  ArtifactSaveModel copyWith({
    bool? success,
    String? artifactId,
    String? message,
    Data? data,
    dynamic error,
    DateTime? timestamp,
  }) =>
      ArtifactSaveModel(
        success: success ?? this.success,
        artifactId: artifactId ?? this.artifactId,
        message: message ?? this.message,
        data: data ?? this.data,
        error: error ?? this.error,
        timestamp: timestamp ?? this.timestamp,
      );

  factory ArtifactSaveModel.fromJson(Map<String, dynamic> json) =>
      ArtifactSaveModel(
        success: json["success"],
        artifactId: json["artifact_id"],
        message: json["message"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        error: json["error"],
        timestamp: json["timestamp"] == null
            ? null
            : DateTime.parse(json["timestamp"]),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "artifact_id": artifactId,
        "message": message,
        "data": data?.toJson(),
        "error": error,
        "timestamp": timestamp?.toIso8601String(),
      };
}

class Data {
  String? artifactId;
  String? tenantId;
  String? artifactType;
  String? action;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? createdBy;
  String? updatedBy;
  String? status;
  String? mongoId;
  String? collectionName;
  List<String>? artifactTypesIncluded;

  Data({
    this.artifactId,
    this.tenantId,
    this.artifactType,
    this.action,
    this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
    this.status,
    this.mongoId,
    this.collectionName,
    this.artifactTypesIncluded,
  });

  Data copyWith({
    String? artifactId,
    String? tenantId,
    String? artifactType,
    String? action,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
    String? status,
    String? mongoId,
    String? collectionName,
    List<String>? artifactTypesIncluded,
  }) =>
      Data(
        artifactId: artifactId ?? this.artifactId,
        tenantId: tenantId ?? this.tenantId,
        artifactType: artifactType ?? this.artifactType,
        action: action ?? this.action,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        createdBy: createdBy ?? this.createdBy,
        updatedBy: updatedBy ?? this.updatedBy,
        status: status ?? this.status,
        mongoId: mongoId ?? this.mongoId,
        collectionName: collectionName ?? this.collectionName,
        artifactTypesIncluded:
            artifactTypesIncluded ?? this.artifactTypesIncluded,
      );

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        artifactId: json["artifact_id"],
        tenantId: json["tenant_id"],
        artifactType: json["artifact_type"],
        action: json["action"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        createdBy: json["created_by"],
        updatedBy: json["updated_by"],
        status: json["status"],
        mongoId: json["mongo_id"],
        collectionName: json["collection_name"],
        artifactTypesIncluded: json["artifact_types_included"] == null
            ? []
            : List<String>.from(json["artifact_types_included"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "artifact_id": artifactId,
        "tenant_id": tenantId,
        "artifact_type": artifactType,
        "action": action,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "created_by": createdBy,
        "updated_by": updatedBy,
        "status": status,
        "mongo_id": mongoId,
        "collection_name": collectionName,
        "artifact_types_included": artifactTypesIncluded == null
            ? []
            : List<dynamic>.from(artifactTypesIncluded!.map((x) => x)),
      };
}
