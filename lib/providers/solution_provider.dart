import 'package:flutter/foundation.dart';
import '../screens/web/static_flow/extract_details_middle_static.dart';

class SolutionProvider extends ChangeNotifier {
  // Private list to store solutions
  final List<SolutionModel> _solutions = [];

  // Getter for solutions list
  List<SolutionModel> get solutions => List.unmodifiable(_solutions);

  // Getter for solutions count
  int get solutionCount => _solutions.length;

  // Getter to check if there are any solutions
  bool get hasSolutions => _solutions.isNotEmpty;

  /// Add a new solution to the list
  void addSolution(String solutionName) {
    if (solutionName.trim().isEmpty) return;

    final newSolution = SolutionModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: solutionName.trim(),
      description: 'Solution: ${solutionName.trim()}',
    );

    _solutions.add(newSolution);
    notifyListeners();
  }

  /// Remove a solution by ID
  void removeSolution(String solutionId) {
    _solutions.removeWhere((solution) => solution.id == solutionId);
    notifyListeners();
  }

  /// Update an existing solution
  void updateSolution(String solutionId, {String? name, String? description}) {
    final index = _solutions.indexWhere((solution) => solution.id == solutionId);
    if (index != -1) {
      final existingSolution = _solutions[index];
      _solutions[index] = SolutionModel(
        id: existingSolution.id,
        name: name ?? existingSolution.name,
        description: description ?? existingSolution.description,
        createdAt: existingSolution.createdAt,
      );
      notifyListeners();
    }
  }

  /// Get a solution by ID
  SolutionModel? getSolutionById(String solutionId) {
    try {
      return _solutions.firstWhere((solution) => solution.id == solutionId);
    } catch (e) {
      return null;
    }
  }

  /// Clear all solutions
  void clearSolutions() {
    _solutions.clear();
    notifyListeners();
  }

  /// Get solutions by name (partial match)
  List<SolutionModel> searchSolutions(String query) {
    if (query.trim().isEmpty) return solutions;
    
    final lowercaseQuery = query.toLowerCase();
    return _solutions.where((solution) =>
        solution.name.toLowerCase().contains(lowercaseQuery) ||
        solution.description.toLowerCase().contains(lowercaseQuery)
    ).toList();
  }
}
