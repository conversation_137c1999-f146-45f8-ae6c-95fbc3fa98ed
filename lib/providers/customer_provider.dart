import 'package:flutter/foundation.dart';

import '../models/attribute_model.dart';

class AttributeRuleProvider extends ChangeNotifier {
  List<AttributeRule> _rules = [
    AttributeRule(role: 'Organizer', assigned: 'Customer', source: 'Inferred', sourceColor: 'orange'),
    AttributeRule(role: 'Organizer', assigned: 'Customer', source: 'Inferred', sourceColor: 'orange'),
    AttributeRule(role: 'Organizer', assigned: 'Customer', source: 'Inferred', sourceColor: 'orange'),
  ];

  List<AttributeRule> get rules => _rules;

  void addRule(AttributeRule rule) {
    _rules.add(rule);
    notifyListeners();
  }

  void deleteRule(index) {
    _rules.remove(index);
    notifyListeners();
  }

  void updateRule(int index, AttributeRule newRule) {
    _rules[index] = newRule;
    notifyListeners();
  }
}
