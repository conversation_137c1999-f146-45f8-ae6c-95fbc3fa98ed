import 'dart:async';

import 'package:flutter/material.dart';
import 'package:nsl/models/discovery_chat_model.dart';
import 'package:nsl/services/discovery_service.dart';

class DiscoveryProvider with ChangeNotifier {
  // bool isLoading = false;
  bool isFileLoading = false;
  bool isSpeechLoading = false;
  TextEditingController chatController =
      TextEditingController();

  final DiscoveryService _apiService = DiscoveryService();
  String _response = "";
  List<History> _history=[];
  StreamSubscription<String>? _subscription;


  String get response => _response;
  List<History> get history=>_history;

  void sendMessage(String message) {
    // Clear previous response
    _history.add(History(content: message,role: "user"));
    _response = "";
    _isLoading=true;
    notifyListeners();

    // Cancel existing subscription
    _subscription?.cancel();
    List<History> temp = []
  ;
  for(var element  in _history){
    if(element != _history.last){
      temp.add(element);
    }
  }
    _subscription = _apiService.sendMessage(message, temp).listen((newChunk) {
      // Append new chunk to existing response
      _response += newChunk;
      notifyListeners();
    },
     onDone: () {
      _isLoading=false;
      // Only add assistant response once, after streaming is done
      _history.add(History(content: _response, role: "assistant"));
      notifyListeners();
    },
     onError: (error) {
      _isLoading=false;
      _response = "Error: $error";
      notifyListeners();
    });
  }

  List<String> _messages = [];
  bool _isLoading = false;

  List<String> get messages => _messages;
  bool get isLoading => _isLoading;

  // Future<void> sendMessage(String message) async {
  //   _messages.clear();
  //   _isLoading = true;
  //   notifyListeners();

  //   final stream = _apiService.sendMessage(message);

  //   await for (final chunk in stream) {
  //     _messages.add(chunk);
  //     notifyListeners(); // 👈 this will refresh UI for each chunk
  //   }

  //   _isLoading = false;
  //   notifyListeners();
  // }

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }
}
