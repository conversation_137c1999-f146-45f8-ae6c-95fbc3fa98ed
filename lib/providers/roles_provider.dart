import 'package:flutter/foundation.dart';
import 'package:nsl/models/role_model.dart';
import 'package:nsl/services/roles_service.dart';
import 'package:nsl/utils/logger.dart';

class RolesProvider extends ChangeNotifier {
  final RolesService _rolesService = RolesService();

  // State variables
  List<PostgresRole> _roles = [];
  bool _isLoading = false;
  String? _error;
  PostgresRole? _selectedRole;

  // Getters
  List<PostgresRole> get roles => _roles;
  bool get isLoading => _isLoading;
  String? get error => _error;
  PostgresRole? get selectedRole => _selectedRole;

  /// Fetches roles from the API
  Future<void> fetchRoles() async {
    if (_isLoading) return; // Prevent multiple simultaneous requests

    _setLoading(true);
    _setError(null);

    try {
      Logger.info('RolesProvider: Starting to fetch roles...');

      final roleModel = await _rolesService.fetchRoles();

      _roles = roleModel.postgresRoles ?? [];
      Logger.info('RolesProvider: Successfully loaded ${_roles.length} roles');

      // Debug: Print role names
      for (var role in _roles) {
        Logger.info('RolesProvider: Role - ID: ${role.roleId}, Name: ${role.name}');
      }

    } catch (e) {
      _setError('An unexpected error occurred: $e');
      Logger.error('RolesProvider: Exception while fetching roles - $e');

    } finally {
      _setLoading(false);
    }
  }

  /// Sets the selected role
  void setSelectedRole(PostgresRole? role) {
    if (_selectedRole != role) {
      _selectedRole = role;
      Logger.info('RolesProvider: Selected role changed to: ${role?.name ?? 'None'}');
      notifyListeners();
    }
  }

  /// Clears the selected role
  void clearSelectedRole() {
    setSelectedRole(null);
  }

  /// Refreshes the roles list
  Future<void> refreshRoles() async {
    Logger.info('RolesProvider: Refreshing roles...');
    _roles.clear();
    await fetchRoles();
  }

  /// Gets a role by ID
  PostgresRole? getRoleById(String id) {
    try {
      return _roles.firstWhere((role) => role.roleId == id);
    } catch (e) {
      Logger.warning('RolesProvider: Role with ID $id not found');
      return null;
    }
  }

  /// Gets a role by name
  PostgresRole? getRoleByName(String name) {
    try {
      return _roles.firstWhere((role) => role.name == name);
    } catch (e) {
      Logger.warning('RolesProvider: Role with name $name not found');
      return null;
    }
  }

  /// Private helper methods
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  void _setError(String? error) {
    if (_error != error) {
      _error = error;
      notifyListeners();
    }
  }

  /// Clears all data
  void clear() {
    _roles.clear();
    _selectedRole = null;
    _error = null;
    _isLoading = false;
    notifyListeners();
  }

  @override
  void dispose() {
    _rolesService.dispose();
    super.dispose();
  }
}
